
.home-page {
  background-color: #ffffff !important;
}

// Home Header Styles
.home-header {
  position: relative;
  width: 100%;
  min-height: 120vh;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);

  // Navigation Bar
  .navbar {
    position: relative;
    z-index: 10;

    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .container-fluid {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .navbar-brand {
      .logo-img {
        height: 50px;
        width: auto;
        object-fit: contain;
      }
    }

    .nav-list {
      list-style: none;
      gap: 2rem;
      margin: 0;
      padding: 0;

      .nav-item {
        .nav-link {
          color: #2c3e50;
          text-decoration: none;
          font-weight: 500;
          font-size: 1rem;
          padding: 0.5rem 1rem;
          border-radius: 8px;
          transition: all 0.3s ease;
          direction: rtl;

          &:hover {
            background-color: #f8f9fa;
            color: #27ae60;
            transform: translateY(-2px);
          }

          &.user-link {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 25px;
            padding: 0.7rem 1.5rem;

            &:hover {
              background: linear-gradient(135deg, #229954, #27ae60);
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            }

            i {
              font-size: 0.9rem;
            }
          }
        }
      }
    }

    // User Registration Link (separate from nav list)
    .user-link {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white !important;
      border-radius: 25px;
      padding: 0.7rem 1.5rem;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;

      &:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        color: white !important;
        text-decoration: none;
      }

      i {
        font-size: 0.9rem;
      }
    }

    // User Profile (when logged in)
    .user-profile {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 25px;
      padding: 0.5rem 1rem;
      border: 1px solid rgba(250, 250, 250, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(39, 174, 96, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);
      }

      .user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #27ae60;
      }

      .user-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.95rem;
      }

      i.fa-chevron-down {
        font-size: 0.8rem;
        color: #27ae60;
        transition: transform 0.3s ease;
      }
    }

    // User Dropdown Menu
    .user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border-radius: 10px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      min-width: 220px;
      z-index: 1000;
      border: 1px solid rgba(0, 0, 0, 0.08);
      overflow: hidden;
      animation: dropdownFadeIn 0.3s ease;

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 10px 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f8f9fa;
          transform: translateX(-3px);
        }

        i, .menu-icon {
          width: 18px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .menu-icon {
          svg {
            width: 16px;
            height: 16px;
          }
        }

        span {
          font-weight: 500;
          color: #2c3e50;
          font-size: 0.9rem;
        }

        &.logout-item {
          &:hover {
            background-color: #fff5f5;
          }

          span {
            color: #e74c3c;
          }
        }

        &.new-request-item {
          background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
          border-top: 2px solid #27ae60;

          &:hover {
            background: linear-gradient(135deg, #d4f4d4, #e8f5e8);
          }

          span {
            color: #27ae60;
            font-weight: 600;
            text-align: center;
            width: 100%;
          }
        }
      }

      .dropdown-divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 0;
      }
    }

    @keyframes dropdownFadeIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  // Hero Section
  .hero-section {
    position: relative;
    height: calc(100vh - 80px);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 20px;
    // margin-left: 10px;
    // margin-right: 10px;

    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.15),
      0 8px 25px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      overflow: hidden;

      .hero-bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;

        transition: transform 0.3s ease;
      }

      &:hover .hero-bg-image {
        transform: scale(1.02);
      }

      .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        background: linear-gradient(
          135deg,
          rgba(38, 83, 147, 0.515) 0%,
          rgba(52, 73, 94, 0.5) 30%,
          rgba(39, 174, 95, 0.518) 70%,
          rgba(46, 204, 113, 0.8) 100%
        );
        backdrop-filter: blur(2px);
      }
    }

    .hero-content {
      position: relative;
      z-index: 5;
      width: 100%;

      .hero-text-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 4rem;

        .hero-text-item {
          animation: fadeInUp 1s ease-out;

          &:nth-child(1) {
            text-align: right;
            animation-delay: 0.2s;
          }

          &:nth-child(2) {
            text-align: center;
            animation-delay: 0.4s;
          }

          &:nth-child(3) {
            text-align: left;
            animation-delay: 0.6s;
          }

          .hero-text {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            text-shadow:
              0 0 20px rgba(255, 255, 255, 0.5),
              2px 2px 8px rgba(0, 0, 0, 0.7),
              4px 4px 15px rgba(0, 0, 0, 0.4);
            margin: 0;
            padding: 1.5rem 2.5rem;
            border-radius: 20px;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.15) 0%,
              rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow:
              0 8px 32px rgba(0, 0, 0, 0.2),
              0 4px 16px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
              );
              transition: left 0.6s ease;
            }

            &:hover {
              transform: translateY(-8px) scale(1.08);
              background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.1) 100%
              );
              box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.3),
                0 10px 30px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
              text-shadow:
                0 0 30px rgba(255, 255, 255, 0.8),
                2px 2px 10px rgba(0, 0, 0, 0.8),
                4px 4px 20px rgba(0, 0, 0, 0.5);

              &::before {
                left: 100%;
              }
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .home-header {
    .hero-section {
      margin-left: 20px;
      margin-right: 20px;
      border-radius: 20px;

      .hero-content {
        .hero-text-container {
          padding: 0 2rem;

          .hero-text-item {
            .hero-text {
              font-size: 2.5rem;
              padding: 1.2rem 2rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .home-header {
    .navbar {
      .container-fluid {
        flex-direction: column;
        gap: 1rem;
      }

      .nav-list {
        gap: 1rem;
        flex-direction: column;
        text-align: center;

        .nav-item {
          .nav-link {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
          }
        }
      }

      .user-link {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
      }

      .user-profile {
        padding: 0.4rem 0.8rem;

        .user-avatar {
          width: 30px;
          height: 30px;
        }

        .user-name {
          font-size: 0.85rem;
        }
      }

      .user-dropdown {
        min-width: 200px;
        right: -15px;

        .dropdown-item {
          padding: 8px 12px;

          span {
            font-size: 0.8rem;
          }

          i, .menu-icon {
            width: 16px;
          }

          .menu-icon {
            svg {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }

    .hero-section {
      margin-left: 15px;
      margin-right: 15px;
      margin-top: 15px;
      border-radius: 15px;

      .hero-content {
        .hero-text-container {
          flex-direction: column;
          gap: 1.5rem;
          padding: 0 1rem;

          .hero-text-item {
            text-align: center !important;

            .hero-text {
              font-size: 2rem;
              padding: 1rem 1.5rem;
              border-radius: 15px;
              box-shadow:
                0 6px 20px rgba(0, 0, 0, 0.2),
                0 3px 10px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .home-header {
    .navbar {
      .navbar-brand {
        .logo-img {
          height: 40px;
        }
      }
    }

    .hero-section {
      margin-left: 10px;
      margin-right: 10px;
      margin-top: 10px;
      border-radius: 12px;

      .hero-content {
        .hero-text-container {
          padding: 0 0.5rem;

          .hero-text-item {
            .hero-text {
              font-size: 1.5rem;
              padding: 0.8rem 1.2rem;
              border-radius: 12px;
              box-shadow:
                0 4px 15px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}

// Mobile Navigation Styles
.navbar-toggler {
  border: none;
  background: transparent;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 123, 255, 0.1);
  }

  &:focus {
    box-shadow: none;
    outline: none;
  }

  .navbar-toggler-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      font-size: 1.2rem;
      color: #333;
      transition: all 0.3s ease;
    }
  }
}

// Mobile Navigation Dropdown
.mobile-nav-dropdown {
  position: absolute;
  top: calc(100% + -1.8rem) !important;
  left: 50%;
  transform: translateX(-50%);
  margin-left: -25px !important;
  width: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 99;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: hidden;
  animation: dropdownFadeIn 0.3s ease;
  margin-top: 0;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: rgba(0, 123, 255, 0.05);
      color: #007bff;
    }

    .menu-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;

      i {
        font-size: 0.8rem;
      }
    }

    span {
      font-size: 0.8rem;
      font-weight: 500;
    }
  }
}

// Mobile dropdown size adjustments - removed since main CSS applies to all screens

@media (max-width: 576px) {
  .mobile-nav-dropdown {
    width: 180px !important;

    .dropdown-item {
      padding: 6px 10px !important;

      .menu-icon {
        width: 16px !important;
        height: 16px !important;

        i {
          font-size: 0.7rem !important;
        }
      }

      span {
        font-size: 0.75rem !important;
      }
    }
  }
}

// Mobile Layout - Force Single Line
@media (max-width: 991.98px) {
  .navbar {
    .container-fluid {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      width: 100% !important;
      padding: 0.5rem 1rem !important;
      min-height: auto !important;
    }

    .navbar-brand {
      flex: 0 0 auto !important;
      margin: 0 !important;

      img {
        height: 35px !important;
        width: auto !important;
      }
    }

    .navbar-toggler {
      flex: 0 0 auto !important;
      margin: 0 !important;
      padding: 0.25rem 0.5rem !important;
      border: none !important;
      background: transparent !important;
      order: 0 !important;
    }

    .navbar-nav {
      flex: 0 0 auto !important;
      margin: 0 !important;

      &.position-relative {
        position: relative !important;
      }

      .user-profile {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.85rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;

        .user-avatar {
          width: 28px !important;
          height: 28px !important;
        }

        .user-name {
          display: inline !important; // Show username on larger mobile screens
        }

        .fas.fa-chevron-down {
          font-size: 0.7rem !important;
          margin-left: 0.25rem !important;
        }
      }

      .user-link {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.85rem !important;
        white-space: nowrap !important;
        display: flex !important;
        align-items: center !important;

        i {
          margin-right: 0.25rem !important;
        }
      }
    }

    // Hide desktop navigation
    .navbar-nav.mx-auto {
      display: none !important;
    }
  }
}

// Extra Small Screens
@media (max-width: 576px) {
  .navbar {
    .container-fluid {
      padding: 0.4rem 0.75rem !important;
    }

    .navbar-brand img {
      height: 30px !important;
    }

    .navbar-toggler {
      padding: 0.2rem 0.4rem !important;

      i {
        font-size: 1rem !important;
      }
    }

    .navbar-nav {
      .user-profile {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.8rem !important;

        .user-avatar {
          width: 24px !important;
          height: 24px !important;
        }
      }

      .user-link {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.8rem !important;

        span {
          display: none !important;
        }

        i {
          margin-right: 0 !important;
          font-size: 1rem !important;
        }
      }
    }
  }
}

// Force horizontal layout on all mobile screens
@media (max-width: 991.98px) {
  .home-header .navbar {
    .container-fluid {
      display: flex !important;
      flex-direction: row !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      overflow: visible !important;
    }

    // Ensure all direct children stay on same line
    > .container-fluid > * {
      flex-shrink: 0 !important;
      white-space: nowrap !important;
    }
  }
}

// Very small screens - Hide username only below 320px
@media (max-width: 319px) {
  .navbar {
    .navbar-nav {
      .user-profile {
        .user-name {
          display: none !important; // Hide username only on very small screens
        }
      }
    }
  }
}

// Properties Section
.properties-section {
  padding: 50px 0;

  // background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4rem;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #3498db, #2980b9);
      border-radius: 2px;
    }
  }
}

.property-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  margin-top: 30px;
 cursor: pointer;

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .property-image {
    position: relative;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover img {
      transform: scale(1.1);
    }

    .property-badge {
      position: absolute;
      top: 15px;
      left: 15px;
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white;
      padding: 5px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.property-badge-rent {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
      }
    }

    .property-location {
      position: absolute;
      bottom: 15px;
      left: 15px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 0.8rem;
      display: flex;
      align-items: center;
      gap: 5px;

      i {
        font-size: 0.7rem;
      }
    }
  }

  .property-content {
    padding: 20px;

    .property-title {
      font-size: 1.3rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
      line-height: 1.3;
    }

    .property-description {
      color: #7f8c8d;
      font-size: 0.9rem;
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .property-price {
      margin-bottom: 15px;

      .price {
        font-size: 1.4rem;
        font-weight: 700;
        color: #3498db;
        background: linear-gradient(135deg, #3498db, #2980b9);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .property-rating {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;

      .stars {
        display: flex;
        gap: 2px;

        i {
          color: #f39c12;
          font-size: 0.9rem;
        }
      }

      .rating-text {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        margin-right: 15px;
      }
    }

    .property-actions {
      display: flex;
      gap: 10px;

      .btn {
        border-radius: 8px;
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        background: white;
        color: #7f8c8d;
        transition: all 0.3s ease;

        &:hover {
          border-color: #3498db;
          color: #3498db;
          background: rgba(52, 152, 219, 0.1);
        }

        i {
          font-size: 0.9rem;
        }
      }
    }

    // Rating and Actions on same line
    .property-rating-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0;

      .property-rating {
        margin-bottom: 0;
        flex: 1;
      }

      .property-actions {
        flex: 0 0 auto;
      }
    }
  }
}

// Responsive Design for Properties
@media (max-width: 768px) {
  .properties-section {
    padding: 60px 0;

    .section-title {
      font-size: 2rem;
      margin-bottom: 2rem;
    }
  }

  .property-card {
    margin-bottom: 20px;

    .property-image {
      height: 180px;
    }

    .property-content {
      padding: 15px;

      .property-title {
        font-size: 1.2rem;
      }

      .property-price .price {
        font-size: 1.2rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .properties-section {
    padding: 40px 0;

    .section-title {
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
    }
  }

  .property-card {
    .property-image {
      height: 160px;

      .property-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
      }

      .property-location {
        font-size: 0.7rem;
        padding: 4px 8px;
      }
    }

    .property-content {
      padding: 12px;

      .property-title {
        font-size: 1.1rem;
      }

      .property-description {
        font-size: 0.8rem;
      }

      .property-price .price {
        font-size: 1.1rem;
      }

      .property-rating {
        .stars i {
          font-size: 0.8rem;
        }

        .rating-text {
          font-size: 0.8rem;
        }
      }

      .property-actions {
        .btn {
          padding: 6px 10px;

          i {
            font-size: 0.8rem;
          }
        }
      }
    }
  }
}

// Horizontal Carousel Section
.horizontal-carousel-section {
  background-color: #F8F8F8;
  margin-top: 90px;

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3rem;
    position: relative;


    &::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #007bff, #0056b3);
      border-radius: 2px;


    }
  }

  .carousel-container {
    margin: 0 30px;
    margin-top: 30px;


    .carousel {
      .carousel-inner {
        .carousel-item {
          .location-card {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            height: 200px;



            &:hover {
              transform: translateY(-5px);
              box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

              img {
                transform: scale(1.05);
              }

              .location-overlay {
                background: rgba(0, 0, 0, 0.7);
              }
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }

            .location-overlay {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: rgba(0, 0, 0, 0.5);
              color: white;
              padding: 15px;
              transition: all 0.3s ease;

              .location-info {
                h5 {
                  font-size: 1.1rem;
                  font-weight: 600;
                  margin-bottom: 5px;
                  color: white;
                  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                }

                p {
                  font-size: 0.85rem;
                  margin: 0;
                  opacity: 0.9;
                  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);

                  i {
                    margin-right: 5px;
                    color: #ffd700;
                  }
                }
              }
            }
          }
        }
      }

      // Custom Carousel Controls
      .carousel-control-prev,
      .carousel-control-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px !important;
        height: 50px !important;
        background: #031752;
        border: none;
        border-radius: 50%;
        opacity: 1;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);

        &:hover {
          background: #1e40af;
          transform: translateY(-50%) scale(1.1);
          box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 20px;
          height: 20px;
          background-size: 20px 20px;
        }
      }

      .carousel-control-prev {
        left: 1px;
      }

      .carousel-control-next {
        right: 1px;
      }
    }
  }
}

// Extra Large screens (1400px and up)
@media (min-width: 1400px) {
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 50px;

      .carousel {
        .carousel-control-prev,
        .carousel-control-next {
          width: 60px;
          height: 60px;
        }
      }

      .location-card {
        height: 220px;

        .location-overlay {
          .location-info {
            h5 {
              font-size: 1.3rem;
            }

            p {
              font-size: 0.95rem;
            }
          }
        }
      }
    }
  }
}

// Large screens (992px to 1399px)
@media (min-width: 992px) and (max-width: 1399px) {
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 40px;

      .location-card {
        height: 200px;
      }
    }
  }
}

// Medium screens (768px to 991px)
@media (min-width: 768px) and (max-width: 991px) {
  .horizontal-carousel-section {
    .section-title {
      font-size: 2.2rem;
      margin-bottom: 2.5rem;
    }

    .carousel-container {
      margin: 0 30px;

      .carousel {
        .carousel-control-prev,
        .carousel-control-next {
          width: 45px;
          height: 45px;
        }

        .carousel-control-prev {
          left: -5px;
        }

        .carousel-control-next {
          right: -5px;
        }
      }

      .location-card {
        height: 180px;

        .location-overlay {
          padding: 12px;

          .location-info {
            h5 {
              font-size: 1rem;
              color: white;
            }

            p {
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
}

// Small screens (576px to 767px)
@media (min-width: 576px) and (max-width: 767px) {
  .horizontal-carousel-section {
    .section-title {
      font-size: 2rem;
      margin-bottom: 2rem;
    }

    .carousel-container {
      margin: 0 20px;

      .carousel {
        .carousel-control-prev,
        .carousel-control-next {
          width: 40px;
          height: 40px;
        }

        .carousel-control-prev {
          left: -10px;
        }

        .carousel-control-next {
          right: -10px;
        }
      }

      .location-card {
        height: 160px;

        .location-overlay {
          padding: 10px;

          .location-info {
            h5 {
              font-size: 0.95rem;
              color: white;
            }

            p {
              font-size: 0.75rem;
            }
          }
        }
      }
    }
  }
}

// Extra Small screens (up to 575px)
@media (max-width: 575px) {
  .horizontal-carousel-section {
    padding: 40px 0;

    .section-title {
      font-size: 1.8rem;
      margin-bottom: 1.5rem;
    }

    .carousel-container {
      margin: 0 15px;

      .carousel {
        .carousel-control-prev,
        .carousel-control-next {
          width: 35px;
          height: 35px;
        }

        .carousel-control-prev {
          left: -15px;
        }

        .carousel-control-next {
          right: -15px;
        }

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 16px;
          height: 16px;
          background-size: 16px 16px;
        }
      }

      .location-card {
        height: 140px;

        .location-overlay {
          padding: 8px;

          .location-info {
            h5 {
              font-size: 0.85rem;
              color: white;
              margin-bottom: 3px;
            }

            p {
              font-size: 0.7rem;

              i {
                margin-right: 3px;
              }
            }
          }
        }
      }
    }
  }
}

// Very Small screens (up to 400px)
@media (max-width: 400px) {
  .horizontal-carousel-section {
    .carousel-container {
      margin: 0 10px;

      .carousel {
        .carousel-control-prev {
          left: -20px;
        }

        .carousel-control-next {
          right: -20px;
        }
      }

      .location-card {
        height: 120px;

        .location-overlay {
          padding: 6px;

          .location-info {
            h5 {
              font-size: 0.8rem;
            }

            p {
              font-size: 0.65rem;
            }
          }
        }
      }
    }
  }
}

// Articles Section
.articles-section {
  background-color: #ffffff;
  padding: 100px 0;
  margin-top: 80px;
  margin-bottom: 50px;

  .section-header {
    margin-bottom: 60px;

    .left-controls {
      .carousel-control-btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: none;
        background: #1e3a8a;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.25);

        &:hover {
          background: #1e40af;
          transform: scale(1.05);
          box-shadow: 0 8px 25px rgba(30, 58, 138, 0.35);
        }

        i {
          font-size: 18px;
        }
      }
    }

    .articles-title {
      font-size: 2.8rem;
      font-weight: 800;
      color: #1e3a8a;
      text-shadow: 2px 2px 4px rgba(30, 58, 138, 0.1);
      margin: 0;
      letter-spacing: -0.5px;
      direction: ltr;
      text-align: center;
      margin-left: 50%;
    }

    .right-link {
      .view-all-link {
        text-decoration: none;
        font-size: 1.4rem;
        font-weight: 600;
        position: relative;
        display: inline-block;
        transition: all 0.3s ease;
        direction: ltr;

        .text-success {
          color: #28a745 !important;
        }

        .green-underline {
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 100%;
          height: 3px;
          background: #28a745;
          border-radius: 2px;
        }

        &:hover {
          transform: translateX(-5px);

          .text-success {
            color: #1e7e34 !important;
          }

          .green-underline {
            background: #1e7e34;
          }

          i {
            transform: translateX(-3px);
          }
        }

        i {
          transition: transform 0.3s ease;
        }
      }
    }
  }

  #articlesCarousel {
    .carousel-inner {
      .carousel-item {
        .article-card {
          position: relative;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          cursor: pointer;
          height: 400px;

          &:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);

            .article-image img {
              transform: scale(1.03);
            }

            .article-overlay {
              background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
            }
          }

          .article-image {
            position: relative;
            height: 100%;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            }

            .article-overlay {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
              color: white;
              padding: 40px 25px 25px;
              transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              .article-content {
                direction: ltr;
                text-align: left;

                h4 {
                  font-size: 1.4rem;
                  font-weight: 700;
                  margin-bottom: 12px;
                  color: white;
                  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                  line-height: 1.3;
                }

                p {
                  font-size: 1rem;
                  margin: 0;
                  opacity: 0.95;
                  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                  line-height: 1.6;
                }
              }
            }
          }
        }
      }
    }

    // Custom Carousel Controls for Articles
    .carousel-control-prev,
    .carousel-control-next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: #1e3a8a;
      border: none;
      border-radius: 50%;
      opacity: 1;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);

      &:hover {
        background: #1e40af;
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
      }

      .carousel-control-prev-icon,
      .carousel-control-next-icon {
        width: 20px;
        height: 20px;
        background-size: 20px 20px;
      }
    }

    .carousel-control-prev {
      left: -25px;
    }

    .carousel-control-next {
      right: -25px;
    }

    // Custom Indicators
    .carousel-indicators {
      bottom: -50px;
      margin-bottom: 0;

      button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: none;
        background: rgba(30, 58, 138, 0.3);
        margin: 0 6px;
        transition: all 0.3s ease;

        &.active {
          background: #1e3a8a;
          transform: scale(1.2);
        }

        &:hover {
          background: #1e40af;
          transform: scale(1.1);
        }
      }
    }
  }
}

// Responsive Design for Articles Section
@media (max-width: 1200px) {
  .articles-section {
    #articlesCarousel {
      .carousel-control-prev {
        left: -15px;
      }

      .carousel-control-next {
        right: -15px;
      }
    }
  }
}

@media (max-width: 992px) {
  .articles-section {
    .section-header {
      .articles-title {
        font-size: 2rem;
      }

      .view-all-link a {
        font-size: 1.2rem;
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .article-card {
            height: 280px;

            .article-image {
              .article-overlay {
                padding: 25px;

                .article-content {
                  h4 {
                    font-size: 1.2rem;
                  }

                  p {
                    font-size: 0.9rem;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 45px;
        height: 45px;
      }
    }
  }
}

@media (max-width: 768px) {
  .articles-section {
    .section-header {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 15px;

      .articles-title {
        font-size: 1.8rem;
      }
    }

    // Responsive layout for All Articles link and carousel
    .row {
      flex-direction: column;

      .col-lg-2, .col-md-3 {
        order: 2;
        margin-top: 20px;

        .right-link {
          text-align: center;

          .view-all-link {
            font-size: 1.2rem !important;

            i {
              font-size: 1rem !important;
            }

            span {
              font-size: 1.2rem !important;
            }
          }
        }
      }

      .col-lg-10, .col-md-9 {
        order: 1;
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .article-card {
            height: 250px;

            .article-image {
              .article-overlay {
                padding: 20px;

                .article-content {
                  h4 {
                    font-size: 1.1rem;
                    margin-bottom: 10px;
                  }

                  p {
                    font-size: 0.85rem;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 40px;
        height: 40px;
        top: 45%;

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 16px;
          height: 16px;
          background-size: 16px 16px;
        }
      }

      .carousel-control-prev {
        left: 10px;
      }

      .carousel-control-next {
        right: 10px;
      }

      .carousel-indicators {
        bottom: -40px;

        button {
          width: 10px;
          height: 10px;
          margin: 0 4px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .articles-section {
    .section-header {
      .articles-title {
        font-size: 1.6rem;
      }
    }

    // Extra responsive adjustments for very small screens
    .row {
      .col-lg-2, .col-md-3 {
        margin-top: 15px;

        .right-link {
          .view-all-link {
            font-size: 1rem !important;

            i {
              font-size: 0.9rem !important;
            }

            span {
              font-size: 1rem !important;
            }
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .article-card {
            height: 220px;

            .article-image {
              .article-overlay {
                padding: 15px;

                .article-content {
                  h4 {
                    font-size: 1rem;
                    margin-bottom: 8px;
                  }

                  p {
                    font-size: 0.8rem;
                    line-height: 1.4;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-control-prev,
      .carousel-control-next {
        width: 35px;
        height: 35px;

        .carousel-control-prev-icon,
        .carousel-control-next-icon {
          width: 14px;
          height: 14px;
          background-size: 14px 14px;
        }
      }

      .carousel-indicators {
        bottom: -35px;

        button {
          width: 8px;
          height: 8px;
          margin: 0 3px;
        }
      }
    }
  }
}

// Extra small screens (phones in portrait mode)
@media (max-width: 480px) {
  .articles-section {
    padding: 40px 0;

    .section-header {
      margin-bottom: 30px;

      .articles-title {
        font-size: 1.4rem;
        text-align: center;
      }

      .left-controls {
        justify-content: center;

        .carousel-control-btn {
          width: 35px;
          height: 35px;

          i {
            font-size: 12px;
          }
        }
      }
    }

    .row {
      .col-lg-2, .col-md-3 {
        margin-top: 10px;

        .right-link {
          .view-all-link {
            font-size: 0.9rem !important;

            i {
              font-size: 0.8rem !important;
            }

            span {
              font-size: 0.9rem !important;
            }
          }
        }
      }
    }

    #articlesCarousel {
      .carousel-inner {
        .carousel-item {
          .row {
            .col-lg-4, .col-md-6, .col-sm-8 {
              max-width: 90%;
              margin: 0 auto;
            }
          }

          .article-card {
            height: 200px;
            margin-bottom: 15px;

            .article-image {
              .article-overlay {
                padding: 12px;

                .article-content {
                  h4 {
                    font-size: 0.9rem;
                    margin-bottom: 6px;
                  }

                  p {
                    font-size: 0.75rem;
                    line-height: 1.3;
                  }
                }
              }
            }
          }
        }
      }

      .carousel-indicators {
        bottom: -30px;

        button {
          width: 6px;
          height: 6px;
          margin: 0 2px;
        }
      }
    }
  }
}


