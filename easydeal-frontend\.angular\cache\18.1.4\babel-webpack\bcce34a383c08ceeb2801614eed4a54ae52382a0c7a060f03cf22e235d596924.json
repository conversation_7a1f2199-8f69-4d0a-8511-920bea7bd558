{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication/services/authentication.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../_metronic/shared/keenicon/keenicon.component\";\nfunction HomeComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"span\", 107);\n    i0.ɵɵelement(3, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 106)(7, \"span\", 107);\n    i0.ɵɵelement(8, \"i\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"About EasyDeal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 106)(12, \"span\", 107);\n    i0.ɵɵelement(13, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"New Projects\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 106)(17, \"span\", 107);\n    i0.ɵɵelement(18, \"i\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Advertisements\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 106)(22, \"span\", 107);\n    i0.ɵɵelement(23, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Contact Us\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_27_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserDropdown());\n    });\n    i0.ɵɵelement(1, \"img\", 114);\n    i0.ɵɵelementStart(2, \"span\", 115);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 116);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getUserProfileImage(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n  }\n}\nfunction HomeComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(2, \"span\", 107);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 119)(4, \"g\", 120);\n    i0.ɵɵelement(5, \"path\", 121)(6, \"path\", 122)(7, \"path\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"defs\")(9, \"clipPath\", 124);\n    i0.ɵɵelement(10, \"rect\", 125);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Requests\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(14, \"span\", 107);\n    i0.ɵɵelement(15, \"app-keenicon\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \" My Profile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(19, \"span\", 107);\n    i0.ɵɵelement(20, \"app-keenicon\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(24, \"span\", 107);\n    i0.ɵɵelement(25, \"i\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \" Help \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(29, \"span\", 107);\n    i0.ɵɵelement(30, \"app-keenicon\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \" Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"div\", 130);\n    i0.ɵɵelementStart(34, \"div\", 131);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"span\", 107);\n    i0.ɵɵelement(36, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"div\", 130);\n    i0.ɵɵelementStart(40, \"div\", 133);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_40_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(41, \"span\", 134);\n    i0.ɵɵtext(42, \" New Request \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_a_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 135);\n    i0.ɵɵelement(1, \"i\", 136);\n    i0.ɵɵtext(2, \" Register Guest \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_196_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 139)(1, \"div\", 140);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_196_div_2_Template_div_click_1_listener() {\n      const location_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLocationClick(location_r5));\n    });\n    i0.ɵɵelement(2, \"img\", 141);\n    i0.ɵɵelementStart(3, \"div\", 142)(4, \"div\", 143)(5, \"h5\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵelement(8, \"i\", 38);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const location_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", location_r5.image || \"assets/media/auth/404-error.png\", i0.ɵɵsanitizeUrl)(\"alt\", location_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(location_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", location_r5.propertyCount, \" Properties Available\");\n  }\n}\nfunction HomeComponent_div_196_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 137);\n    i0.ɵɵtemplate(2, HomeComponent_div_196_div_2_Template, 10, 4, \"div\", 138);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const slide_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r7 === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", slide_r6);\n  }\n}\nexport class HomeComponent {\n  authService;\n  currentUser = null;\n  isLoggedIn = false;\n  showUserDropdown = false;\n  showMobileMenu = false;\n  // Location Carousel Data\n  locationSlides = [];\n  currentSlideIndex = 0;\n  carouselInterval;\n  locations = [{\n    id: 1,\n    name: ' New Cairo',\n    image: './assets/media/stock/600x400/img-10.jpg',\n    propertyCount: 2341\n  }, {\n    id: 2,\n    name: '  Maadi',\n    image: './assets/media/stock/600x400/img-20.jpg',\n    propertyCount: 1234\n  }, {\n    id: 3,\n    name: ' Sheikh Zayed  ',\n    image: './assets/media/stock/600x400/img-30.jpg',\n    propertyCount: 3421\n  }, {\n    id: 4,\n    name: '   Heliopolis',\n    image: './assets/media/stock/600x400/img-40.jpg',\n    propertyCount: 2341\n  }, {\n    id: 5,\n    name: '   Nasr City',\n    image: './assets/media/stock/600x400/img-50.jpg',\n    propertyCount: 987\n  }, {\n    id: 6,\n    name: '  6 October',\n    image: './assets/media/stock/600x400/img-60.jpg',\n    propertyCount: 1543\n  }, {\n    id: 7,\n    name: '  Maadi',\n    image: './assets/media/stock/600x400/img-70.jpg',\n    propertyCount: 876\n  }, {\n    id: 8,\n    name: '  Zamalek',\n    image: './assets/media/stock/600x400/img-80.jpg',\n    propertyCount: 654\n  }, {\n    id: 9,\n    name: '  New Cairo',\n    image: './assets/media/stock/600x400/img-90.jpg',\n    propertyCount: 1098\n  }, {\n    id: 10,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }, {\n    id: 11,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }, {\n    id: 12,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }];\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.checkUserSession();\n    this.initializeLocationSlides();\n  }\n  ngAfterViewInit() {\n    // Initialize Bootstrap carousel after view is loaded\n    setTimeout(() => {\n      this.initializeCarousel();\n    }, 100);\n  }\n  ngOnDestroy() {\n    // Clean up interval when component is destroyed\n    if (this.carouselInterval) {\n      clearInterval(this.carouselInterval);\n    }\n  }\n  checkUserSession() {\n    // Check if user is logged in by checking localStorage\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      try {\n        this.currentUser = JSON.parse(currentUser);\n        this.isLoggedIn = true;\n      } catch (error) {\n        // If parsing fails, user is not logged in\n        this.isLoggedIn = false;\n        this.currentUser = null;\n      }\n    } else {\n      this.isLoggedIn = false;\n      this.currentUser = null;\n    }\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return this.currentUser.fullName || 'User';\n    }\n    return 'Guest';\n  }\n  getUserProfileImage() {\n    if (this.currentUser && this.currentUser.image) {\n      return this.currentUser.image;\n    }\n    // Return default avatar if no profile image\n    return 'assets/media/avatars/blank.png';\n  }\n  toggleUserDropdown() {\n    this.showUserDropdown = !this.showUserDropdown;\n  }\n  closeUserDropdown() {\n    this.showUserDropdown = false;\n  }\n  logout() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('currentUser');\n    this.isLoggedIn = false;\n    this.currentUser = null;\n    this.showUserDropdown = false;\n    // Optionally redirect to login page\n    // this.router.navigate(['/authentication/login']);\n  }\n  toggleMobileMenu() {\n    this.showMobileMenu = !this.showMobileMenu;\n    // Close user dropdown when mobile menu is toggled\n    if (this.showMobileMenu) {\n      this.showUserDropdown = false;\n    }\n  }\n  closeMobileMenu() {\n    this.showMobileMenu = false;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const userProfile = target.closest('.user-profile');\n    const userDropdown = target.closest('.user-dropdown');\n    const navbarToggler = target.closest('.navbar-toggler');\n    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');\n    // Close user dropdown if clicked outside of user profile and dropdown\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\n      this.showUserDropdown = false;\n    }\n    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown\n    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {\n      this.showMobileMenu = false;\n    }\n  }\n  // Location Carousel Methods\n  initializeLocationSlides() {\n    // Split locations into slides of 5 items each\n    const itemsPerSlide = 5;\n    this.locationSlides = [];\n    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {\n      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));\n    }\n  }\n  initializeCarousel() {\n    try {\n      const carouselElement = document.getElementById('horizontalCarousel');\n      if (carouselElement) {\n        // Try Bootstrap first\n        if (typeof bootstrap !== 'undefined') {\n          const carousel = new bootstrap.Carousel(carouselElement, {\n            interval: 5000,\n            ride: 'carousel',\n            wrap: true,\n            keyboard: true,\n            pause: 'hover'\n          });\n          console.log('Bootstrap carousel initialized');\n        } else {\n          // Fallback: Manual carousel control\n          this.startManualCarousel();\n          console.log('Manual carousel initialized');\n        }\n      }\n    } catch (error) {\n      console.error('Error initializing carousel:', error);\n      // Fallback to manual carousel\n      this.startManualCarousel();\n    }\n  }\n  startManualCarousel() {\n    // Clear any existing interval\n    if (this.carouselInterval) {\n      clearInterval(this.carouselInterval);\n    }\n    // Start auto-play\n    this.carouselInterval = setInterval(() => {\n      this.nextSlide();\n    }, 5000);\n  }\n  nextSlide() {\n    const totalSlides = this.locationSlides.length;\n    if (totalSlides > 0) {\n      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;\n      this.updateCarouselDisplay();\n    }\n  }\n  prevSlide() {\n    const totalSlides = this.locationSlides.length;\n    if (totalSlides > 0) {\n      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;\n      this.updateCarouselDisplay();\n    }\n  }\n  updateCarouselDisplay() {\n    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');\n    carouselItems.forEach((item, index) => {\n      if (index === this.currentSlideIndex) {\n        item.classList.add('active');\n      } else {\n        item.classList.remove('active');\n      }\n    });\n  }\n  onLocationClick(location) {\n    console.log('Location clicked:', location);\n    // Add your navigation logic here\n    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });\n  }\n  // Method to load locations from API (for future integration)\n  // loadLocations(): void {\n  //   // Replace with actual API call\n  //   // this.locationService.getLocations().subscribe(data => {\n  //   //   this.locations = data;\n  //   //   this.initializeLocationSlides();\n  //   //   // Re-initialize carousel after data loads\n  //   //   setTimeout(() => this.initializeCarousel(), 100);\n  //   // });\n  // }\n  loadMoreLocations() {}\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function HomeComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 300,\n    vars: 6,\n    consts: [[1, \"home-page\"], [1, \"home-header\"], [1, \"navbar\", \"navbar-expand-lg\"], [1, \"container-fluid\", \"px-4\"], [1, \"navbar-brand\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/loading-logo.png\", 1, \"h-40px\", \"app-sidebar-logo-default\"], [\"type\", \"button\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\", \"d-lg-none\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"navbar-nav\", \"mx-auto\", \"d-none\", \"d-lg-flex\"], [1, \"nav-list\", \"d-flex\", \"align-items-center\", \"mb-0\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\"], [\"class\", \"mobile-nav-dropdown d-lg-none\", 4, \"ngIf\"], [1, \"navbar-nav\", \"position-relative\"], [\"class\", \"nav-link user-profile\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [\"href\", \"#\", \"class\", \"nav-link user-link\", 4, \"ngIf\"], [1, \"hero-section\"], [1, \"hero-background\"], [\"src\", \"./assets/media/home/<USER>\", \"alt\", \"Hero Background\", 1, \"hero-bg-image\"], [1, \"hero-overlay\"], [1, \"hero-content\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-12\"], [1, \"hero-text-container\"], [1, \"hero-text-item\"], [1, \"hero-text\"], [1, \"properties-section\"], [1, \"row\"], [1, \"section-title\", \"text-center\", \"mb-5\"], [1, \"row\", \"g-4\"], [1, \"col-lg-3\", \"col-md-6\", \"col-sm-12\"], [1, \"property-card\"], [1, \"property-image\"], [\"src\", \"./assets/media/stock/600x400/img-1.jpg\", \"alt\", \"Property 1\", 1, \"img-fluid\"], [1, \"property-badge\"], [1, \"property-location\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"property-content\"], [1, \"property-title\"], [1, \"property-description\"], [1, \"property-price\"], [1, \"price\"], [1, \"property-rating-actions\"], [1, \"property-rating\"], [1, \"stars\"], [1, \"fas\", \"fa-star\"], [1, \"rating-text\"], [1, \"property-actions\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"far\", \"fa-heart\"], [1, \"fas\", \"fa-share-alt\"], [\"src\", \"./assets/media/stock/600x400/img-15.jpg\", \"alt\", \"Property 2\", 1, \"img-fluid\"], [1, \"property-badge\", \"property-badge-rent\"], [\"src\", \"./assets/media/stock/600x400/img-25.jpg\", \"alt\", \"Property 3\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-35.jpg\", \"alt\", \"Property 4\", 1, \"img-fluid\"], [1, \"horizontal-carousel-section\", \"py-5\"], [1, \"carousel-container\", \"position-relative\"], [\"id\", \"horizontalCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"5000\", 1, \"carousel\", \"slide\"], [1, \"carousel-inner\"], [\"class\", \"carousel-item \", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"data-bs-target\", \"#horizontalCarousel\", \"data-bs-slide\", \"prev\", 1, \"carousel-control-prev\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"carousel-control-prev-icon\"], [1, \"visually-hidden\"], [\"type\", \"button\", \"data-bs-target\", \"#horizontalCarousel\", \"data-bs-slide\", \"next\", 1, \"carousel-control-next\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"carousel-control-next-icon\"], [1, \"row\", \"justify-content-center\", \"mt-5\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-secondary\", \"btn-lg\", 3, \"click\"], [1, \"articles-section\", \"py-5\"], [1, \"row\", \"mb-5\"], [1, \"section-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"left-controls\", \"d-flex\", \"align-items-center\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide\", \"prev\", 1, \"carousel-control-btn\", \"prev-btn\"], [1, \"fas\", \"fa-chevron-left\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide\", \"next\", 1, \"carousel-control-btn\", \"next-btn\", \"ms-2\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"articles-title\", \"text-center\", \"flex-grow-1\"], [1, \"col-lg-2\", \"col-md-3\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"right-link\"], [\"href\", \"#\", 1, \"view-all-link\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\", \"text-success\", \"fs-4\"], [1, \"text-success\", \"fs-2\"], [1, \"green-underline\"], [1, \"col-lg-10\", \"col-md-9\"], [\"id\", \"articlesCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"6000\", 1, \"carousel\", \"slide\"], [1, \"carousel-item\", \"active\"], [1, \"row\", \"g-4\", \"justify-content-center\"], [1, \"col-lg-4\", \"col-md-6\", \"col-sm-8\"], [1, \"article-card\"], [1, \"article-image\"], [\"src\", \"./assets/media/stock/600x400/img-10.jpg\", \"alt\", \"Article 1\", 1, \"img-fluid\"], [1, \"article-overlay\"], [1, \"article-content\"], [\"src\", \"./assets/media/stock/600x400/img-20.jpg\", \"alt\", \"Article 2\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-30.jpg\", \"alt\", \"Article 3\", 1, \"img-fluid\"], [1, \"carousel-item\"], [\"src\", \"./assets/media/stock/600x400/img-40.jpg\", \"alt\", \"Article 4\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-50.jpg\", \"alt\", \"Article 5\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-60.jpg\", \"alt\", \"Article 6\", 1, \"img-fluid\"], [1, \"carousel-indicators\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"0\", 1, \"active\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"1\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"2\"], [1, \"mobile-nav-dropdown\", \"d-lg-none\"], [1, \"dropdown-item\"], [1, \"menu-icon\", \"me-2\"], [1, \"fas\", \"fa-home\", \"fs-6\", \"text-primary\"], [1, \"fas\", \"fa-info-circle\", \"fs-6\", \"text-info\"], [1, \"fas\", \"fa-building\", \"fs-6\", \"text-success\"], [1, \"fas\", \"fa-bullhorn\", \"fs-6\", \"text-warning\"], [1, \"fas\", \"fa-phone\", \"fs-6\", \"text-gray-600\"], [1, \"nav-link\", \"user-profile\", 3, \"click\"], [1, \"user-avatar\", \"me-2\", 3, \"src\", \"alt\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\", \"ms-2\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [\"width\", \"19\", \"height\", \"19\", \"viewBox\", \"0 0 19 19\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"clip-path\", \"url(#clip0_24_2533)\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"], [\"id\", \"clip0_24_2533\"], [\"width\", \"19\", \"height\", \"19\", \"fill\", \"white\"], [\"name\", \"user\", \"type\", \"outline\", 1, \"fs-5\", \"text-primary\"], [\"name\", \"messages\", \"type\", \"outline\", 1, \"fs-5\", \"text-info\"], [1, \"fa-regular\", \"fa-circle-question\", \"fs-6\", \"text-warning\"], [\"name\", \"notification-on\", \"type\", \"outline\", 1, \"fs-5\", \"text-gray-600\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"logout-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"fs-6\", \"text-danger\"], [1, \"dropdown-item\", \"new-request-item\", 3, \"click\"], [1, \"text-success\"], [\"href\", \"#\", 1, \"nav-link\", \"user-link\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"row\", \"justify-content-center\", \"g-2\", \"g-md-3\", \"mt-5\"], [\"class\", \"col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-3\", \"col-sm-4\", \"col-6\"], [1, \"location-card\", 3, \"click\"], [1, \"img-fluid\", 3, \"src\", \"alt\"], [1, \"location-overlay\"], [1, \"location-info\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"nav\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_6_listener() {\n          return ctx.toggleMobileMenu();\n        });\n        i0.ɵɵelement(7, \"span\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\", 10)(11, \"a\", 11);\n        i0.ɵɵtext(12, \" Home \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"li\", 10)(14, \"a\", 11);\n        i0.ɵɵtext(15, \" About EasyDeal \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 10)(17, \"a\", 11);\n        i0.ɵɵtext(18, \" New Projects \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"li\", 10)(20, \"a\", 11);\n        i0.ɵɵtext(21, \" Advertisements \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"li\", 10)(23, \"a\", 11);\n        i0.ɵɵtext(24, \" Contact Us \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(25, HomeComponent_div_25_Template, 26, 0, \"div\", 12);\n        i0.ɵɵelementStart(26, \"div\", 13);\n        i0.ɵɵtemplate(27, HomeComponent_div_27_Template, 5, 3, \"div\", 14)(28, HomeComponent_div_28_Template, 43, 0, \"div\", 15)(29, HomeComponent_a_29_Template, 3, 0, \"a\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18);\n        i0.ɵɵelement(32, \"img\", 19)(33, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 21)(35, \"div\", 22)(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 25)(39, \"div\", 26)(40, \"h2\", 27);\n        i0.ɵɵtext(41, \" Easy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 26)(43, \"h2\", 27);\n        i0.ɵɵtext(44, \" Speed \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 26)(46, \"h2\", 27);\n        i0.ɵɵtext(47, \" Reliability \");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(48, \"section\", 28)(49, \"div\", 22)(50, \"div\", 29)(51, \"div\", 24)(52, \"h2\", 30);\n        i0.ɵɵtext(53, \"Featured Properties\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(54, \"div\", 31)(55, \"div\", 32)(56, \"div\", 33)(57, \"div\", 34);\n        i0.ɵɵelement(58, \"img\", 35);\n        i0.ɵɵelementStart(59, \"div\", 36);\n        i0.ɵɵtext(60, \"For Sale\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"div\", 37);\n        i0.ɵɵelement(62, \"i\", 38);\n        i0.ɵɵelementStart(63, \"span\");\n        i0.ɵɵtext(64, \"New Cairo\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(65, \"div\", 39)(66, \"h4\", 40);\n        i0.ɵɵtext(67, \"Luxury Apartment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"p\", 41);\n        i0.ɵɵtext(69, \"3 Bedrooms \\u2022 2 Bathrooms \\u2022 150 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 42)(71, \"span\", 43);\n        i0.ɵɵtext(72, \"2.5M EGP\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(73, \"div\", 44)(74, \"div\", 45)(75, \"div\", 46);\n        i0.ɵɵelement(76, \"i\", 47)(77, \"i\", 47)(78, \"i\", 47)(79, \"i\", 47)(80, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"span\", 48);\n        i0.ɵɵtext(82, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"div\", 49)(84, \"button\", 50);\n        i0.ɵɵelement(85, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(86, \"button\", 50);\n        i0.ɵɵelement(87, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(88, \"div\", 32)(89, \"div\", 33)(90, \"div\", 34);\n        i0.ɵɵelement(91, \"img\", 53);\n        i0.ɵɵelementStart(92, \"div\", 54);\n        i0.ɵɵtext(93, \"For Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"div\", 37);\n        i0.ɵɵelement(95, \"i\", 38);\n        i0.ɵɵelementStart(96, \"span\");\n        i0.ɵɵtext(97, \"Maadi\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(98, \"div\", 39)(99, \"h4\", 40);\n        i0.ɵɵtext(100, \"Modern Villa\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(101, \"p\", 41);\n        i0.ɵɵtext(102, \"4 Bedrooms \\u2022 3 Bathrooms \\u2022 250 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"div\", 42)(104, \"span\", 43);\n        i0.ɵɵtext(105, \"25K EGP/month\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(106, \"div\", 44)(107, \"div\", 45)(108, \"div\", 46);\n        i0.ɵɵelement(109, \"i\", 47)(110, \"i\", 47)(111, \"i\", 47)(112, \"i\", 47)(113, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"span\", 48);\n        i0.ɵɵtext(115, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(116, \"div\", 49)(117, \"button\", 50);\n        i0.ɵɵelement(118, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"button\", 50);\n        i0.ɵɵelement(120, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(121, \"div\", 32)(122, \"div\", 33)(123, \"div\", 34);\n        i0.ɵɵelement(124, \"img\", 55);\n        i0.ɵɵelementStart(125, \"div\", 36);\n        i0.ɵɵtext(126, \"For Sale\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"div\", 37);\n        i0.ɵɵelement(128, \"i\", 38);\n        i0.ɵɵelementStart(129, \"span\");\n        i0.ɵɵtext(130, \"Zamalek\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(131, \"div\", 39)(132, \"h4\", 40);\n        i0.ɵɵtext(133, \"Penthouse Suite\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(134, \"p\", 41);\n        i0.ɵɵtext(135, \"5 Bedrooms \\u2022 4 Bathrooms \\u2022 300 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(136, \"div\", 42)(137, \"span\", 43);\n        i0.ɵɵtext(138, \"8.5M EGP\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(139, \"div\", 44)(140, \"div\", 45)(141, \"div\", 46);\n        i0.ɵɵelement(142, \"i\", 47)(143, \"i\", 47)(144, \"i\", 47)(145, \"i\", 47)(146, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(147, \"span\", 48);\n        i0.ɵɵtext(148, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(149, \"div\", 49)(150, \"button\", 50);\n        i0.ɵɵelement(151, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(152, \"button\", 50);\n        i0.ɵɵelement(153, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(154, \"div\", 32)(155, \"div\", 33)(156, \"div\", 34);\n        i0.ɵɵelement(157, \"img\", 56);\n        i0.ɵɵelementStart(158, \"div\", 54);\n        i0.ɵɵtext(159, \"For Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"div\", 37);\n        i0.ɵɵelement(161, \"i\", 38);\n        i0.ɵɵelementStart(162, \"span\");\n        i0.ɵɵtext(163, \"Heliopolis\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(164, \"div\", 39)(165, \"h4\", 40);\n        i0.ɵɵtext(166, \"Family House\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(167, \"p\", 41);\n        i0.ɵɵtext(168, \"3 Bedrooms \\u2022 2 Bathrooms \\u2022 180 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(169, \"div\", 42)(170, \"span\", 43);\n        i0.ɵɵtext(171, \"18K EGP/month\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(172, \"div\", 44)(173, \"div\", 45)(174, \"div\", 46);\n        i0.ɵɵelement(175, \"i\", 47)(176, \"i\", 47)(177, \"i\", 47)(178, \"i\", 47)(179, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(180, \"span\", 48);\n        i0.ɵɵtext(181, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(182, \"div\", 49)(183, \"button\", 50);\n        i0.ɵɵelement(184, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(185, \"button\", 50);\n        i0.ɵɵelement(186, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(187, \"section\", 57)(188, \"div\", 22)(189, \"div\", 29)(190, \"div\", 24)(191, \"h2\", 30);\n        i0.ɵɵtext(192, \"Explore Locations\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(193, \"div\", 58)(194, \"div\", 59)(195, \"div\", 60);\n        i0.ɵɵtemplate(196, HomeComponent_div_196_Template, 3, 3, \"div\", 61);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(197, \"button\", 62);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_197_listener() {\n          return ctx.prevSlide();\n        });\n        i0.ɵɵelement(198, \"span\", 63);\n        i0.ɵɵelementStart(199, \"span\", 64);\n        i0.ɵɵtext(200, \"Previous\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(201, \"button\", 65);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_201_listener() {\n          return ctx.nextSlide();\n        });\n        i0.ɵɵelement(202, \"span\", 66);\n        i0.ɵɵelementStart(203, \"span\", 64);\n        i0.ɵɵtext(204, \"Next\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(205, \"div\", 67)(206, \"div\", 68)(207, \"button\", 69);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_207_listener() {\n          return ctx.loadMoreLocations();\n        });\n        i0.ɵɵtext(208, \" Load More Locations \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(209, \"section\", 70)(210, \"div\", 22)(211, \"div\", 71)(212, \"div\", 24)(213, \"div\", 72)(214, \"div\", 73)(215, \"button\", 74);\n        i0.ɵɵelement(216, \"i\", 75);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(217, \"button\", 76);\n        i0.ɵɵelement(218, \"i\", 77);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(219, \"h1\", 78);\n        i0.ɵɵtext(220, \"Articles That Interest You\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(221, \"div\", 29)(222, \"div\", 79)(223, \"div\", 80)(224, \"a\", 81);\n        i0.ɵɵelement(225, \"i\", 82);\n        i0.ɵɵelementStart(226, \"span\", 83);\n        i0.ɵɵtext(227, \"All Articles\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(228, \"div\", 84);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(229, \"div\", 85)(230, \"div\", 86)(231, \"div\", 60)(232, \"div\", 87)(233, \"div\", 88)(234, \"div\", 89)(235, \"div\", 90)(236, \"div\", 91);\n        i0.ɵɵelement(237, \"img\", 92);\n        i0.ɵɵelementStart(238, \"div\", 93)(239, \"div\", 94)(240, \"h4\");\n        i0.ɵɵtext(241, \"Modern Finishing Materials - Shop with the Best\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(242, \"p\");\n        i0.ɵɵtext(243, \"A very quiet area away from the noise and hustle of the city, suitable for large and small families, spacious area with a private garden.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(244, \"div\", 89)(245, \"div\", 90)(246, \"div\", 91);\n        i0.ɵɵelement(247, \"img\", 95);\n        i0.ɵɵelementStart(248, \"div\", 93)(249, \"div\", 94)(250, \"h4\");\n        i0.ɵɵtext(251, \"Invest Your Money with Hotel Property\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(252, \"p\");\n        i0.ɵɵtext(253, \"Excellent investment opportunity in the heart of the city, guaranteed returns and integrated management, strategic location near the airport and commercial centers.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(254, \"div\", 89)(255, \"div\", 90)(256, \"div\", 91);\n        i0.ɵɵelement(257, \"img\", 96);\n        i0.ɵɵelementStart(258, \"div\", 93)(259, \"div\", 94)(260, \"h4\");\n        i0.ɵɵtext(261, \"Villa 10 October April 2019\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(262, \"p\");\n        i0.ɵɵtext(263, \"Latest international finishing materials, high quality and competitive prices, specialized team to implement finishing works to the highest standards.\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(264, \"div\", 97)(265, \"div\", 88)(266, \"div\", 89)(267, \"div\", 90)(268, \"div\", 91);\n        i0.ɵɵelement(269, \"img\", 98);\n        i0.ɵɵelementStart(270, \"div\", 93)(271, \"div\", 94)(272, \"h4\");\n        i0.ɵɵtext(273, \"Apartment in New Cairo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(274, \"p\");\n        i0.ɵɵtext(275, \"Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated facilities, close to universities and international schools.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(276, \"div\", 89)(277, \"div\", 90)(278, \"div\", 91);\n        i0.ɵɵelement(279, \"img\", 99);\n        i0.ɵɵelementStart(280, \"div\", 93)(281, \"div\", 94)(282, \"h4\");\n        i0.ɵɵtext(283, \"North Coast Properties\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(284, \"p\");\n        i0.ɵɵtext(285, \"Residential units directly on the sea, wonderful panoramic view, integrated recreational facilities and suitable for summer investment.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(286, \"div\", 89)(287, \"div\", 90)(288, \"div\", 91);\n        i0.ɵɵelement(289, \"img\", 100);\n        i0.ɵɵelementStart(290, \"div\", 93)(291, \"div\", 94)(292, \"h4\");\n        i0.ɵɵtext(293, \"Administrative Offices Downtown\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(294, \"p\");\n        i0.ɵɵtext(295, \"Modern office spaces in the heart of Cairo, suitable for companies and institutions, parking and integrated service facilities.\");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(296, \"div\", 101);\n        i0.ɵɵelement(297, \"button\", 102)(298, \"button\", 103)(299, \"button\", 104);\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵattribute(\"aria-expanded\", ctx.showMobileMenu);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngIf\", ctx.showMobileMenu);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn && ctx.showUserDropdown);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n        i0.ɵɵadvance(167);\n        i0.ɵɵproperty(\"ngForOf\", ctx.locationSlides);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.KeeniconComponent],\n    styles: [\".home-page[_ngcontent-%COMP%] {\\n  background-color: #ffffff !important;\\n}\\n\\n.home-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  min-height: 120vh;\\n  overflow: hidden;\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 1rem 0;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  gap: 2rem;\\n  margin: 0;\\n  padding: 0;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 1rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  direction: rtl;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  color: #27ae60;\\n  transform: translateY(-2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white !important;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n  color: white !important;\\n  text-decoration: none;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 25px;\\n  padding: 0.5rem 1rem;\\n  border: 1px solid rgba(250, 250, 250, 0.3);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(39, 174, 96, 0.5);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.95rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #27ae60;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\\n  min-width: 220px;\\n  z-index: 1000;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(-3px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  width: 18px;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fff5f5;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\\n  border-top: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #d4f4d4, #e8f5e8);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-weight: 600;\\n  text-align: center;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 0;\\n}\\n@keyframes _ngcontent-%COMP%_dropdownFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: calc(100vh - 80px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  margin-top: 20px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-bg-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]:hover   .hero-bg-image[_ngcontent-%COMP%] {\\n  transform: scale(1.02);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(38, 83, 147, 0.515) 0%, rgba(52, 73, 94, 0.5) 30%, rgba(39, 174, 95, 0.518) 70%, rgba(46, 204, 113, 0.8) 100%);\\n  -webkit-backdrop-filter: blur(2px);\\n          backdrop-filter: blur(2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0 4rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 1s ease-out;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(1) {\\n  text-align: right;\\n  animation-delay: 0.2s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(2) {\\n  text-align: center;\\n  animation-delay: 0.4s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(3) {\\n  text-align: left;\\n  animation-delay: 0.6s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 700;\\n  color: white;\\n  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5), 2px 2px 8px rgba(0, 0, 0, 0.7), 4px 4px 15px rgba(0, 0, 0, 0.4);\\n  margin: 0;\\n  padding: 1.5rem 2.5rem;\\n  border-radius: 20px;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);\\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.6s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px) scale(1.08);\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 10px 30px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.4);\\n  text-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 2px 2px 10px rgba(0, 0, 0, 0.8), 4px 4px 20px rgba(0, 0, 0, 0.5);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 20px;\\n    margin-right: 20px;\\n    border-radius: 20px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    padding: 0 2rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n    padding: 1.2rem 2rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.5rem 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    right: -15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 15px;\\n    margin-right: 15px;\\n    margin-top: 15px;\\n    border-radius: 15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n    padding: 0 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n    text-align: center !important;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    padding: 1rem 1.5rem;\\n    border-radius: 15px;\\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n    height: 40px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 10px;\\n    margin-right: 10px;\\n    margin-top: 10px;\\n    border-radius: 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n    padding: 0.8rem 1.2rem;\\n    border-radius: 12px;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n.navbar-toggler[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.1);\\n}\\n.navbar-toggler[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n\\n.mobile-nav-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% - 1.8rem) !important;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  margin-left: -25px !important;\\n  width: 200px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  z-index: 99;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n  margin-top: 0;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  color: #333;\\n  text-decoration: none;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.05);\\n  color: #007bff;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 18px;\\n  height: 18px;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 576px) {\\n  .mobile-nav-dropdown[_ngcontent-%COMP%] {\\n    width: 180px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 6px 10px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px !important;\\n    height: 16px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.7rem !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.75rem !important;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    justify-content: space-between !important;\\n    align-items: center !important;\\n    flex-wrap: nowrap !important;\\n    width: 100% !important;\\n    padding: 0.5rem 1rem !important;\\n    min-height: auto !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 35px !important;\\n    width: auto !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n    padding: 0.25rem 0.5rem !important;\\n    border: none !important;\\n    background: transparent !important;\\n    order: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav.position-relative[_ngcontent-%COMP%] {\\n    position: relative !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem !important;\\n    font-size: 0.85rem !important;\\n    white-space: nowrap !important;\\n    display: flex !important;\\n    align-items: center !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 28px !important;\\n    height: 28px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: inline !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .fas.fa-chevron-down[_ngcontent-%COMP%] {\\n    font-size: 0.7rem !important;\\n    margin-left: 0.25rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem !important;\\n    font-size: 0.85rem !important;\\n    white-space: nowrap !important;\\n    display: flex !important;\\n    align-items: center !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 0.25rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav.mx-auto[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.75rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 30px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 24px !important;\\n    height: 24px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 0 !important;\\n    font-size: 1rem !important;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    flex-direction: row !important;\\n    justify-content: space-between !important;\\n    align-items: center !important;\\n    flex-wrap: nowrap !important;\\n    overflow: visible !important;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]    > .container-fluid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    flex-shrink: 0 !important;\\n    white-space: nowrap !important;\\n  }\\n}\\n@media (max-width: 319px) {\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n.properties-section[_ngcontent-%COMP%] {\\n  padding: 50px 0;\\n}\\n.properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 4rem;\\n  position: relative;\\n}\\n.properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -15px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #3498db, #2980b9);\\n  border-radius: 2px;\\n}\\n\\n.property-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  height: 100%;\\n  margin-top: 30px;\\n  cursor: pointer;\\n}\\n.property-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  left: 15px;\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  padding: 5px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge.property-badge-rent[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c, #c0392b);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 15px;\\n  left: 15px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 8px;\\n  line-height: 1.3;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-description[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n  margin-bottom: 15px;\\n  line-height: 1.5;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  color: #3498db;\\n  background: linear-gradient(135deg, #3498db, #2980b9);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  font-size: 0.9rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  color: #7f8c8d;\\n  transition: all 0.3s ease;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3498db;\\n  color: #3498db;\\n  background: rgba(52, 152, 219, 0.1);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  flex: 1;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n}\\n\\n@media (max-width: 768px) {\\n  .properties-section[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n  .property-card[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .properties-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n    margin-bottom: 1.5rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 4px 8px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 4px 8px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%] {\\n  background-color: #F8F8F8;\\n  margin-top: 90px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 3rem;\\n  position: relative;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -15px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #007bff, #0056b3);\\n  border-radius: 2px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n  margin: 0 30px;\\n  margin-top: 30px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 200px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover   .location-overlay[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  padding: 15px;\\n  transition: all 0.3s ease;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  color: white;\\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin: 0;\\n  opacity: 0.9;\\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n  color: #ffd700;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 50px !important;\\n  height: 50px !important;\\n  background: #031752;\\n  border: none;\\n  border-radius: 50%;\\n  opacity: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]:hover, \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-size: 20px 20px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n  left: 1px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  right: 1px;\\n}\\n\\n@media (min-width: 1400px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 50px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n}\\n@media (min-width: 992px) and (max-width: 1399px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 40px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n}\\n@media (min-width: 768px) and (max-width: 991px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2.2rem;\\n    margin-bottom: 2.5rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 30px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -5px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -5px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    color: white;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n@media (min-width: 576px) and (max-width: 767px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n    color: white;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n@media (max-width: 575px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n    margin-bottom: 1.5rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    background-size: 16px 16px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 140px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n    color: white;\\n    margin-bottom: 3px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 3px;\\n  }\\n}\\n@media (max-width: 400px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n}\\n.articles-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 100px 0;\\n  margin-top: 80px;\\n  margin-bottom: 50px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #1e3a8a;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.25);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.35);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n  font-size: 2.8rem;\\n  font-weight: 800;\\n  color: #1e3a8a;\\n  text-shadow: 2px 2px 4px rgba(30, 58, 138, 0.1);\\n  margin: 0;\\n  letter-spacing: -0.5px;\\n  direction: ltr;\\n  text-align: center;\\n  margin-left: 200px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  position: relative;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n  direction: ltr;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   .green-underline[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 100%;\\n  height: 3px;\\n  background: #28a745;\\n  border-radius: 2px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover {\\n  transform: translateX(-5px);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   .text-success[_ngcontent-%COMP%] {\\n  color: #1e7e34 !important;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   .green-underline[_ngcontent-%COMP%] {\\n  background: #1e7e34;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(-3px);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  cursor: pointer;\\n  height: 400px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.03);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover   .article-overlay[_ngcontent-%COMP%] {\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));\\n  color: white;\\n  padding: 40px 25px 25px;\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%] {\\n  direction: ltr;\\n  text-align: left;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  margin-bottom: 12px;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  line-height: 1.3;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n  opacity: 0.95;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n  line-height: 1.6;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 50px;\\n  height: 50px;\\n  background: #1e3a8a;\\n  border: none;\\n  border-radius: 50%;\\n  opacity: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]:hover, \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-size: 20px 20px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n  left: -25px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  right: -25px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n  bottom: -50px;\\n  margin-bottom: 0;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(30, 58, 138, 0.3);\\n  margin: 0 6px;\\n  transition: all 0.3s ease;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background: #1e3a8a;\\n  transform: scale(1.2);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: scale(1.1);\\n}\\n\\n@media (max-width: 1200px) {\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n    gap: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n    margin-bottom: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    top: 45%;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    background-size: 16px 16px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -40px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n    margin: 0 4px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.6rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-bottom: 8px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    line-height: 1.4;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n    background-size: 14px 14px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 8px;\\n    height: 8px;\\n    margin: 0 3px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9ob21lLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vLi4vY29tcHUlMjB6b25lL0Rlc2t0b3AvdGFza2VzL05ldyUyMGZvbGRlci9lYXN5ZGVhbC1mcm9udGVuZC9zcmMvYXBwL3BhZ2VzL2hvbWUvaG9tZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNFLG9DQUFBO0FDQUY7O0FESUE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUNBQUE7QUNERjtBRElFO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0VBRUEsbUNBQUE7VUFBQSwyQkFBQTtFQUNBLGVBQUE7RUFDQSx5Q0FBQTtBQ0hKO0FES0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBQ0hOO0FET007RUFDRSxZQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FDTFI7QURTSTtFQUNFLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0FDUE47QURVUTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLG9CQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGNBQUE7QUNSVjtBRFVVO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EsMkJBQUE7QUNSWjtBRFdVO0VBQ0UscURBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxzQkFBQTtBQ1RaO0FEV1k7RUFDRSxxREFBQTtFQUNBLDJCQUFBO0VBQ0EsNkNBQUE7QUNUZDtBRFlZO0VBQ0UsaUJBQUE7QUNWZDtBRGtCSTtFQUNFLHFEQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FDaEJOO0FEa0JNO0VBQ0UscURBQUE7RUFDQSwyQkFBQTtFQUNBLDZDQUFBO0VBQ0EsdUJBQUE7RUFDQSxxQkFBQTtBQ2hCUjtBRG1CTTtFQUNFLGlCQUFBO0FDakJSO0FEc0JJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esb0NBQUE7RUFDQSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsMENBQUE7RUFDQSx5QkFBQTtFQUNBLGVBQUE7QUNwQk47QURzQk07RUFDRSxvQ0FBQTtFQUNBLG9DQUFBO0VBQ0EsMkJBQUE7RUFDQSw2Q0FBQTtBQ3BCUjtBRHVCTTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLHlCQUFBO0FDckJSO0FEd0JNO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUN0QlI7QUR5Qk07RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSwrQkFBQTtBQ3ZCUjtBRDRCSTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFFBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsMENBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxxQ0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUNBQUE7QUMxQk47QUQ0Qk07RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLDRDQUFBO0FDMUJSO0FENEJRO0VBQ0UseUJBQUE7RUFDQSwyQkFBQTtBQzFCVjtBRDZCUTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FDM0JWO0FEK0JVO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUM3Qlo7QURpQ1E7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtBQy9CVjtBRG1DVTtFQUNFLHlCQUFBO0FDakNaO0FEb0NVO0VBQ0UsY0FBQTtBQ2xDWjtBRHNDUTtFQUNFLHFEQUFBO0VBQ0EsNkJBQUE7QUNwQ1Y7QURzQ1U7RUFDRSxxREFBQTtBQ3BDWjtBRHVDVTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBQ3JDWjtBRDBDTTtFQUNFLFdBQUE7RUFDQSw4QkFBQTtFQUNBLFNBQUE7QUN4Q1I7QUQ0Q0k7RUFDRTtJQUNFLFVBQUE7SUFDQSw0QkFBQTtFQzFDTjtFRDRDSTtJQUNFLFVBQUE7SUFDQSx3QkFBQTtFQzFDTjtBQUNGO0FEK0NFO0VBQ0Usa0JBQUE7RUFDQSwwQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUlBLGtIQUNFO0VBR0YsbUNBQUE7VUFBQSwyQkFBQTtFQUNBLDBDQUFBO0FDbkRKO0FEcURJO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtFQUVBLGdCQUFBO0FDcEROO0FEc0RNO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLHVCQUFBO0VBRUEsK0JBQUE7QUNyRFI7QUR3RE07RUFDRSxzQkFBQTtBQ3REUjtBRHlETTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUVBLHVKQUFBO0VBT0Esa0NBQUE7VUFBQSwwQkFBQTtBQzlEUjtBRGtFSTtFQUNFLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7QUNoRU47QURrRU07RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0FDaEVSO0FEa0VRO0VBQ0UsK0JBQUE7QUNoRVY7QURrRVU7RUFDRSxpQkFBQTtFQUNBLHFCQUFBO0FDaEVaO0FEbUVVO0VBQ0Usa0JBQUE7RUFDQSxxQkFBQTtBQ2pFWjtBRG9FVTtFQUNFLGdCQUFBO0VBQ0EscUJBQUE7QUNsRVo7QURxRVU7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsK0dBQ0U7RUFHRixTQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLGlHQUFBO0VBS0EsbUNBQUE7VUFBQSwyQkFBQTtFQUNBLDBDQUFBO0VBQ0EsZ0hBQ0U7RUFHRiw0REFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUM3RVo7QUQrRVk7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esc0ZBQUE7RUFNQSwwQkFBQTtBQ2xGZDtBRHFGWTtFQUNFLHVDQUFBO0VBQ0EsZ0dBQUE7RUFLQSxrSEFDRTtFQUdGLGdIQUNFO0FDM0ZoQjtBRCtGYztFQUNFLFVBQUE7QUM3RmhCOztBRHdHQTtFQUNFO0lBQ0UsVUFBQTtJQUNBLDJCQUFBO0VDckdGO0VEdUdBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VDckdGO0FBQ0Y7QUR5R0E7RUFFSTtJQUNFLGlCQUFBO0lBQ0Esa0JBQUE7SUFDQSxtQkFBQTtFQ3hHSjtFRDJHTTtJQUNFLGVBQUE7RUN6R1I7RUQ0R1U7SUFDRSxpQkFBQTtJQUNBLG9CQUFBO0VDMUdaO0FBQ0Y7QURrSEE7RUFHTTtJQUNFLHNCQUFBO0lBQ0EsU0FBQTtFQ2xITjtFRHFISTtJQUNFLFNBQUE7SUFDQSxzQkFBQTtJQUNBLGtCQUFBO0VDbkhOO0VEc0hRO0lBQ0UsaUJBQUE7SUFDQSxzQkFBQTtFQ3BIVjtFRHlISTtJQUNFLGlCQUFBO0lBQ0Esb0JBQUE7RUN2SE47RUQwSEk7SUFDRSxzQkFBQTtFQ3hITjtFRDBITTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VDeEhSO0VEMkhNO0lBQ0Usa0JBQUE7RUN6SFI7RUQ2SEk7SUFDRSxnQkFBQTtJQUNBLFlBQUE7RUMzSE47RUQ2SE07SUFDRSxpQkFBQTtFQzNIUjtFRDZIUTtJQUNFLGlCQUFBO0VDM0hWO0VEOEhRO0lBQ0UsV0FBQTtFQzVIVjtFRGdJVTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VDOUhaO0VEcUlFO0lBQ0UsaUJBQUE7SUFDQSxrQkFBQTtJQUNBLGdCQUFBO0lBQ0EsbUJBQUE7RUNuSUo7RURzSU07SUFDRSxzQkFBQTtJQUNBLFdBQUE7SUFDQSxlQUFBO0VDcElSO0VEc0lRO0lBQ0UsNkJBQUE7RUNwSVY7RURzSVU7SUFDRSxlQUFBO0lBQ0Esb0JBQUE7SUFDQSxtQkFBQTtJQUNBLHdFQUNFO0VDcklkO0FBQ0Y7QUQ4SUE7RUFJUTtJQUNFLFlBQUE7RUMvSVI7RURvSkU7SUFDRSxpQkFBQTtJQUNBLGtCQUFBO0lBQ0EsZ0JBQUE7SUFDQSxtQkFBQTtFQ2xKSjtFRHFKTTtJQUNFLGlCQUFBO0VDbkpSO0VEc0pVO0lBQ0UsaUJBQUE7SUFDQSxzQkFBQTtJQUNBLG1CQUFBO0lBQ0EsdUVBQ0U7RUNySmQ7QUFDRjtBRCtKQTtFQUNFLFlBQUE7RUFDQSx1QkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQzdKRjtBRCtKRTtFQUNFLGtDQUFBO0FDN0pKO0FEZ0tFO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0FDOUpKO0FEaUtFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUMvSko7QURpS0k7RUFDRSxpQkFBQTtFQUNBLFdBQUE7RUFDQSx5QkFBQTtBQy9KTjs7QURxS0E7RUFDRSxrQkFBQTtFQUNBLG1DQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0EsNkJBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlDQUFBO0VBQ0EsV0FBQTtFQUNBLHFDQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQ0FBQTtFQUNBLGFBQUE7QUNsS0Y7QURvS0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLDRDQUFBO0FDbEtKO0FEb0tJO0VBQ0UsbUJBQUE7QUNsS047QURxS0k7RUFDRSxtQ0FBQTtFQUNBLGNBQUE7QUNuS047QURzS0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FDcEtOO0FEc0tNO0VBQ0UsaUJBQUE7QUNwS1I7QUR3S0k7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0FDdEtOOztBRDZLQTtFQUNFO0lBQ0UsdUJBQUE7RUMxS0Y7RUQ0S0U7SUFDRSw0QkFBQTtFQzFLSjtFRDRLSTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7RUMxS047RUQ0S007SUFDRSw0QkFBQTtFQzFLUjtFRDhLSTtJQUNFLDZCQUFBO0VDNUtOO0FBQ0Y7QURrTEE7RUFFSTtJQUNFLHdCQUFBO0lBQ0EseUNBQUE7SUFDQSw4QkFBQTtJQUNBLDRCQUFBO0lBQ0Esc0JBQUE7SUFDQSwrQkFBQTtJQUNBLDJCQUFBO0VDakxKO0VEb0xFO0lBQ0UseUJBQUE7SUFDQSxvQkFBQTtFQ2xMSjtFRG9MSTtJQUNFLHVCQUFBO0lBQ0Esc0JBQUE7RUNsTE47RURzTEU7SUFDRSx5QkFBQTtJQUNBLG9CQUFBO0lBQ0Esa0NBQUE7SUFDQSx1QkFBQTtJQUNBLGtDQUFBO0lBQ0EsbUJBQUE7RUNwTEo7RUR1TEU7SUFDRSx5QkFBQTtJQUNBLG9CQUFBO0VDckxKO0VEdUxJO0lBQ0UsNkJBQUE7RUNyTE47RUR3TEk7SUFDRSxrQ0FBQTtJQUNBLDZCQUFBO0lBQ0EsOEJBQUE7SUFDQSx3QkFBQTtJQUNBLDhCQUFBO0VDdExOO0VEd0xNO0lBQ0Usc0JBQUE7SUFDQSx1QkFBQTtFQ3RMUjtFRHlMTTtJQUNFLDBCQUFBO0VDdkxSO0VEMExNO0lBQ0UsNEJBQUE7SUFDQSwrQkFBQTtFQ3hMUjtFRDRMSTtJQUNFLGtDQUFBO0lBQ0EsNkJBQUE7SUFDQSw4QkFBQTtJQUNBLHdCQUFBO0lBQ0EsOEJBQUE7RUMxTE47RUQ0TE07SUFDRSxnQ0FBQTtFQzFMUjtFRGdNRTtJQUNFLHdCQUFBO0VDOUxKO0FBQ0Y7QURtTUE7RUFFSTtJQUNFLGtDQUFBO0VDbE1KO0VEcU1FO0lBQ0UsdUJBQUE7RUNuTUo7RURzTUU7SUFDRSxpQ0FBQTtFQ3BNSjtFRHNNSTtJQUNFLDBCQUFBO0VDcE1OO0VEeU1JO0lBQ0UsaUNBQUE7SUFDQSw0QkFBQTtFQ3ZNTjtFRHlNTTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7RUN2TVI7RUQyTUk7SUFDRSxpQ0FBQTtJQUNBLDRCQUFBO0VDek1OO0VEMk1NO0lBQ0Usd0JBQUE7RUN6TVI7RUQ0TU07SUFDRSwwQkFBQTtJQUNBLDBCQUFBO0VDMU1SO0FBQ0Y7QURpTkE7RUFFSTtJQUNFLHdCQUFBO0lBQ0EsOEJBQUE7SUFDQSx5Q0FBQTtJQUNBLDhCQUFBO0lBQ0EsNEJBQUE7SUFDQSw0QkFBQTtFQ2hOSjtFRG9ORTtJQUNFLHlCQUFBO0lBQ0EsOEJBQUE7RUNsTko7QUFDRjtBRHVOQTtFQUlRO0lBQ0Usd0JBQUE7RUN4TlI7QUFDRjtBRCtOQTtFQUNFLGVBQUE7QUM3TkY7QURpT0U7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUMvTko7QURpT0k7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsU0FBQTtFQUNBLDJCQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtFQUNBLGtCQUFBO0FDL05OOztBRG9PQTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDRCxlQUFBO0FDak9EO0FEbU9FO0VBQ0UsNEJBQUE7RUFDQSwyQ0FBQTtBQ2pPSjtBRG9PRTtFQUNFLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLGdCQUFBO0FDbE9KO0FEb09JO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLCtCQUFBO0FDbE9OO0FEcU9JO0VBQ0UscUJBQUE7QUNuT047QURzT0k7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EscURBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLHFCQUFBO0FDcE9OO0FEc09NO0VBQ0UscURBQUE7QUNwT1I7QUR3T0k7RUFDRSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0VBQ0EsOEJBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQ3RPTjtBRHdPTTtFQUNFLGlCQUFBO0FDdE9SO0FEMk9FO0VBQ0UsYUFBQTtBQ3pPSjtBRDJPSTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQ3pPTjtBRDRPSTtFQUNFLGNBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUMxT047QUQ2T0k7RUFDRSxtQkFBQTtBQzNPTjtBRDZPTTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EscURBQUE7RUFDQSw2QkFBQTtFQUNBLG9DQUFBO0VBQ0EscUJBQUE7QUMzT1I7QUQrT0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGdCQUFBO0FDN09OO0FEK09NO0VBQ0UsYUFBQTtFQUNBLFFBQUE7QUM3T1I7QUQrT1E7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUM3T1Y7QURpUE07RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0FDL09SO0FEbVBJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUNqUE47QURtUE07RUFDRSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSx5QkFBQTtBQ2pQUjtBRG1QUTtFQUNFLHFCQUFBO0VBQ0EsY0FBQTtFQUNBLG1DQUFBO0FDalBWO0FEb1BRO0VBQ0UsaUJBQUE7QUNsUFY7QUR3UEk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGdCQUFBO0FDdFBOO0FEd1BNO0VBQ0UsZ0JBQUE7RUFDQSxPQUFBO0FDdFBSO0FEeVBNO0VBQ0UsY0FBQTtBQ3ZQUjs7QUQ4UEE7RUFDRTtJQUNFLGVBQUE7RUMzUEY7RUQ2UEU7SUFDRSxlQUFBO0lBQ0EsbUJBQUE7RUMzUEo7RUQrUEE7SUFDRSxtQkFBQTtFQzdQRjtFRCtQRTtJQUNFLGFBQUE7RUM3UEo7RURnUUU7SUFDRSxhQUFBO0VDOVBKO0VEZ1FJO0lBQ0UsaUJBQUE7RUM5UE47RURpUUk7SUFDRSxpQkFBQTtFQy9QTjtBQUNGO0FEb1FBO0VBQ0U7SUFDRSxlQUFBO0VDbFFGO0VEb1FFO0lBQ0UsaUJBQUE7SUFDQSxxQkFBQTtFQ2xRSjtFRHVRRTtJQUNFLGFBQUE7RUNyUUo7RUR1UUk7SUFDRSxpQkFBQTtJQUNBLGdCQUFBO0VDclFOO0VEd1FJO0lBQ0UsaUJBQUE7SUFDQSxnQkFBQTtFQ3RRTjtFRDBRRTtJQUNFLGFBQUE7RUN4UUo7RUQwUUk7SUFDRSxpQkFBQTtFQ3hRTjtFRDJRSTtJQUNFLGlCQUFBO0VDelFOO0VENFFJO0lBQ0UsaUJBQUE7RUMxUU47RUQ4UU07SUFDRSxpQkFBQTtFQzVRUjtFRCtRTTtJQUNFLGlCQUFBO0VDN1FSO0VEa1JNO0lBQ0UsaUJBQUE7RUNoUlI7RURrUlE7SUFDRSxpQkFBQTtFQ2hSVjtBQUNGO0FEd1JBO0VBQ0UseUJBQUE7RUFDQSxnQkFBQTtBQ3RSRjtBRHdSRTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQ3RSSjtBRHlSSTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLG9EQUFBO0VBQ0Esa0JBQUE7QUN2Uk47QUQ2UkU7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUMzUko7QURpU1U7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5Q0FBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7QUMvUlo7QURtU1k7RUFDRSwyQkFBQTtFQUNBLDJDQUFBO0FDalNkO0FEbVNjO0VBQ0Usc0JBQUE7QUNqU2hCO0FEb1NjO0VBQ0UsOEJBQUE7QUNsU2hCO0FEc1NZO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLCtCQUFBO0FDcFNkO0FEdVNZO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSw4QkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7QUNyU2Q7QUR3U2dCO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLDJDQUFBO0FDdFNsQjtBRHlTZ0I7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxZQUFBO0VBQ0EsMkNBQUE7QUN2U2xCO0FEeVNrQjtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtBQ3ZTcEI7QURpVE07O0VBRUUsa0JBQUE7RUFDQSxRQUFBO0VBQ0EsMkJBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0VBQ0EseUJBQUE7RUFDQSw2Q0FBQTtBQy9TUjtBRGlUUTs7RUFDRSxtQkFBQTtFQUNBLHNDQUFBO0VBQ0EsNkNBQUE7QUM5U1Y7QURpVFE7Ozs7RUFFRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDBCQUFBO0FDN1NWO0FEaVRNO0VBQ0UsU0FBQTtBQy9TUjtBRGtUTTtFQUNFLFVBQUE7QUNoVFI7O0FEdVRBO0VBRUk7SUFDRSxjQUFBO0VDclRKO0VEd1RNOztJQUVFLFdBQUE7SUFDQSxZQUFBO0VDdFRSO0VEMFRJO0lBQ0UsYUFBQTtFQ3hUTjtFRDRUVTtJQUNFLGlCQUFBO0VDMVRaO0VENlRVO0lBQ0Usa0JBQUE7RUMzVFo7QUFDRjtBRG9VQTtFQUVJO0lBQ0UsY0FBQTtFQ25VSjtFRHFVSTtJQUNFLGFBQUE7RUNuVU47QUFDRjtBRHlVQTtFQUVJO0lBQ0UsaUJBQUE7SUFDQSxxQkFBQTtFQ3hVSjtFRDJVRTtJQUNFLGNBQUE7RUN6VUo7RUQ0VU07O0lBRUUsV0FBQTtJQUNBLFlBQUE7RUMxVVI7RUQ2VU07SUFDRSxVQUFBO0VDM1VSO0VEOFVNO0lBQ0UsV0FBQTtFQzVVUjtFRGdWSTtJQUNFLGFBQUE7RUM5VU47RURnVk07SUFDRSxhQUFBO0VDOVVSO0VEaVZVO0lBQ0UsZUFBQTtJQUNBLFlBQUE7RUMvVVo7RURrVlU7SUFDRSxpQkFBQTtFQ2hWWjtBQUNGO0FEeVZBO0VBRUk7SUFDRSxlQUFBO0lBQ0EsbUJBQUE7RUN4Vko7RUQyVkU7SUFDRSxjQUFBO0VDelZKO0VENFZNOztJQUVFLFdBQUE7SUFDQSxZQUFBO0VDMVZSO0VENlZNO0lBQ0UsV0FBQTtFQzNWUjtFRDhWTTtJQUNFLFlBQUE7RUM1VlI7RURnV0k7SUFDRSxhQUFBO0VDOVZOO0VEZ1dNO0lBQ0UsYUFBQTtFQzlWUjtFRGlXVTtJQUNFLGtCQUFBO0lBQ0EsWUFBQTtFQy9WWjtFRGtXVTtJQUNFLGtCQUFBO0VDaFdaO0FBQ0Y7QUR5V0E7RUFDRTtJQUNFLGVBQUE7RUN2V0Y7RUR5V0U7SUFDRSxpQkFBQTtJQUNBLHFCQUFBO0VDdldKO0VEMFdFO0lBQ0UsY0FBQTtFQ3hXSjtFRDJXTTs7SUFFRSxXQUFBO0lBQ0EsWUFBQTtFQ3pXUjtFRDRXTTtJQUNFLFdBQUE7RUMxV1I7RUQ2V007SUFDRSxZQUFBO0VDM1dSO0VEOFdNOztJQUVFLFdBQUE7SUFDQSxZQUFBO0lBQ0EsMEJBQUE7RUM1V1I7RURnWEk7SUFDRSxhQUFBO0VDOVdOO0VEZ1hNO0lBQ0UsWUFBQTtFQzlXUjtFRGlYVTtJQUNFLGtCQUFBO0lBQ0EsWUFBQTtJQUNBLGtCQUFBO0VDL1daO0VEa1hVO0lBQ0UsaUJBQUE7RUNoWFo7RURrWFk7SUFDRSxpQkFBQTtFQ2hYZDtBQUNGO0FEMFhBO0VBRUk7SUFDRSxjQUFBO0VDelhKO0VENFhNO0lBQ0UsV0FBQTtFQzFYUjtFRDZYTTtJQUNFLFlBQUE7RUMzWFI7RUQrWEk7SUFDRSxhQUFBO0VDN1hOO0VEK1hNO0lBQ0UsWUFBQTtFQzdYUjtFRGdZVTtJQUNFLGlCQUFBO0VDOVhaO0VEaVlVO0lBQ0Usa0JBQUE7RUMvWFo7QUFDRjtBRHdZQTtFQUNFLHlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FDdFlGO0FEd1lFO0VBQ0UsbUJBQUE7QUN0WUo7QUR5WU07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7RUFDQSw4Q0FBQTtBQ3ZZUjtBRHlZUTtFQUNFLG1CQUFBO0VBQ0Esc0JBQUE7RUFDQSw4Q0FBQTtBQ3ZZVjtBRDBZUTtFQUNFLGVBQUE7QUN4WVY7QUQ2WUk7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLCtDQUFBO0VBQ0EsU0FBQTtFQUNBLHNCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUMzWU47QUQrWU07RUFDRSxxQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHFCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0FDN1lSO0FEK1lRO0VBQ0UseUJBQUE7QUM3WVY7QURnWlE7RUFDRSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FDOVlWO0FEaVpRO0VBQ0UsMkJBQUE7QUMvWVY7QURpWlU7RUFDRSx5QkFBQTtBQy9ZWjtBRGtaVTtFQUNFLG1CQUFBO0FDaFpaO0FEbVpVO0VBQ0UsMkJBQUE7QUNqWlo7QURxWlE7RUFDRSwrQkFBQTtBQ25aVjtBRDRaUTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDJDQUFBO0VBQ0EseURBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtBQzFaVjtBRDRaVTtFQUNFLDJCQUFBO0VBQ0EsMkNBQUE7QUMxWlo7QUQ0Wlk7RUFDRSxzQkFBQTtBQzFaZDtBRDZaWTtFQUNFLDREQUFBO0FDM1pkO0FEK1pVO0VBQ0Usa0JBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7QUM3Wlo7QUQrWlk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsK0RBQUE7QUM3WmQ7QURnYVk7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLDZEQUFBO0VBQ0EsWUFBQTtFQUNBLHVCQUFBO0VBQ0EseURBQUE7QUM5WmQ7QURnYWM7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7QUM5WmhCO0FEZ2FnQjtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSx5Q0FBQTtFQUNBLGdCQUFBO0FDOVpsQjtBRGlhZ0I7RUFDRSxlQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7RUFDQSx5Q0FBQTtFQUNBLGdCQUFBO0FDL1psQjtBRHlhSTs7RUFFRSxrQkFBQTtFQUNBLFFBQUE7RUFDQSwyQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0VBQ0EseUJBQUE7RUFDQSw2Q0FBQTtBQ3ZhTjtBRHlhTTs7RUFDRSxtQkFBQTtFQUNBLHNDQUFBO0VBQ0EsNkNBQUE7QUN0YVI7QUR5YU07Ozs7RUFFRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDBCQUFBO0FDcmFSO0FEeWFJO0VBQ0UsV0FBQTtBQ3ZhTjtBRDBhSTtFQUNFLFlBQUE7QUN4YU47QUQ0YUk7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7QUMxYU47QUQ0YU07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGtDQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0FDMWFSO0FENGFRO0VBQ0UsbUJBQUE7RUFDQSxxQkFBQTtBQzFhVjtBRDZhUTtFQUNFLG1CQUFBO0VBQ0EscUJBQUE7QUMzYVY7O0FEbWJBO0VBR007SUFDRSxXQUFBO0VDbGJOO0VEcWJJO0lBQ0UsWUFBQTtFQ25iTjtBQUNGO0FEd2JBO0VBR007SUFDRSxlQUFBO0VDeGJOO0VEMmJJO0lBQ0UsaUJBQUE7RUN6Yk47RURnY1E7SUFDRSxhQUFBO0VDOWJWO0VEaWNZO0lBQ0UsYUFBQTtFQy9iZDtFRGtjZ0I7SUFDRSxpQkFBQTtFQ2hjbEI7RURtY2dCO0lBQ0UsaUJBQUE7RUNqY2xCO0VEMGNJOztJQUVFLFdBQUE7SUFDQSxZQUFBO0VDeGNOO0FBQ0Y7QUQ2Y0E7RUFFSTtJQUNFLHNCQUFBO0lBQ0Esa0NBQUE7SUFDQSxTQUFBO0VDNWNKO0VEOGNJO0lBQ0UsaUJBQUE7RUM1Y047RURtZFE7SUFDRSxhQUFBO0VDamRWO0VEb2RZO0lBQ0UsYUFBQTtFQ2xkZDtFRHFkZ0I7SUFDRSxpQkFBQTtJQUNBLG1CQUFBO0VDbmRsQjtFRHNkZ0I7SUFDRSxrQkFBQTtFQ3BkbEI7RUQ2ZEk7O0lBRUUsV0FBQTtJQUNBLFlBQUE7SUFDQSxRQUFBO0VDM2ROO0VENmRNOzs7O0lBRUUsV0FBQTtJQUNBLFlBQUE7SUFDQSwwQkFBQTtFQ3pkUjtFRDZkSTtJQUNFLFVBQUE7RUMzZE47RUQ4ZEk7SUFDRSxXQUFBO0VDNWROO0VEK2RJO0lBQ0UsYUFBQTtFQzdkTjtFRCtkTTtJQUNFLFdBQUE7SUFDQSxZQUFBO0lBQ0EsYUFBQTtFQzdkUjtBQUNGO0FEbWVBO0VBR007SUFDRSxpQkFBQTtFQ25lTjtFRHNlSTtJQUNFLGlCQUFBO0VDcGVOO0VEMmVRO0lBQ0UsYUFBQTtFQ3plVjtFRDRlWTtJQUNFLGFBQUE7RUMxZWQ7RUQ2ZWdCO0lBQ0UsZUFBQTtJQUNBLGtCQUFBO0VDM2VsQjtFRDhlZ0I7SUFDRSxpQkFBQTtJQUNBLGdCQUFBO0VDNWVsQjtFRHFmSTs7SUFFRSxXQUFBO0lBQ0EsWUFBQTtFQ25mTjtFRHFmTTs7OztJQUVFLFdBQUE7SUFDQSxZQUFBO0lBQ0EsMEJBQUE7RUNqZlI7RURxZkk7SUFDRSxhQUFBO0VDbmZOO0VEcWZNO0lBQ0UsVUFBQTtJQUNBLFdBQUE7SUFDQSxhQUFBO0VDbmZSO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLmhvbWUtcGFnZSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4vLyBIb21lIEhlYWRlciBTdHlsZXNcclxuLmhvbWUtaGVhZGVyIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgbWluLWhlaWdodDogMTIwdmg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpO1xyXG5cclxuICAvLyBOYXZpZ2F0aW9uIEJhclxyXG4gIC5uYXZiYXIge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgei1pbmRleDogMTA7XHJcblxyXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xyXG4gICAgcGFkZGluZzogMXJlbSAwO1xyXG4gICAgYm94LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcblxyXG4gICAgLmNvbnRhaW5lci1mbHVpZCB7XHJcbiAgICAgIG1heC13aWR0aDogMTQwMHB4O1xyXG4gICAgICBtYXJnaW46IDAgYXV0bztcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgfVxyXG5cclxuICAgIC5uYXZiYXItYnJhbmQge1xyXG4gICAgICAubG9nby1pbWcge1xyXG4gICAgICAgIGhlaWdodDogNTBweDtcclxuICAgICAgICB3aWR0aDogYXV0bztcclxuICAgICAgICBvYmplY3QtZml0OiBjb250YWluO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLm5hdi1saXN0IHtcclxuICAgICAgbGlzdC1zdHlsZTogbm9uZTtcclxuICAgICAgZ2FwOiAycmVtO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICAgIHBhZGRpbmc6IDA7XHJcblxyXG4gICAgICAubmF2LWl0ZW0ge1xyXG4gICAgICAgIC5uYXYtbGluayB7XHJcbiAgICAgICAgICBjb2xvcjogIzJjM2U1MDtcclxuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgICAgICBkaXJlY3Rpb246IHJ0bDtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgY29sb3I6ICMyN2FlNjA7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmLnVzZXItbGluayB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyN2FlNjAsICMyZWNjNzEpO1xyXG4gICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDI1cHg7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAuN3JlbSAxLjVyZW07XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjI5OTU0LCAjMjdhZTYwKTtcclxuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDM5LCAxNzQsIDk2LCAwLjMpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpIHtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFVzZXIgUmVnaXN0cmF0aW9uIExpbmsgKHNlcGFyYXRlIGZyb20gbmF2IGxpc3QpXHJcbiAgICAudXNlci1saW5rIHtcclxuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI3YWU2MCwgIzJlY2M3MSk7XHJcbiAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAyNXB4O1xyXG4gICAgICBwYWRkaW5nOiAwLjdyZW0gMS41cmVtO1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjI5OTU0LCAjMjdhZTYwKTtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDM5LCAxNzQsIDk2LCAwLjMpO1xyXG4gICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xyXG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBVc2VyIFByb2ZpbGUgKHdoZW4gbG9nZ2VkIGluKVxyXG4gICAgLnVzZXItcHJvZmlsZSB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMjVweDtcclxuICAgICAgcGFkZGluZzogMC41cmVtIDFyZW07XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjUwLCAyNTAsIDI1MCwgMC4zKTtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgzOSwgMTc0LCA5NiwgMC41KTtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDM5LCAxNzQsIDk2LCAwLjIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAudXNlci1hdmF0YXIge1xyXG4gICAgICAgIHdpZHRoOiAzNXB4O1xyXG4gICAgICAgIGhlaWdodDogMzVweDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7XHJcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgIzI3YWU2MDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLnVzZXItbmFtZSB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBjb2xvcjogIzJjM2U1MDtcclxuICAgICAgICBmb250LXNpemU6IDAuOTVyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGkuZmEtY2hldnJvbi1kb3duIHtcclxuICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICBjb2xvcjogIzI3YWU2MDtcclxuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXNlciBEcm9wZG93biBNZW51XHJcbiAgICAudXNlci1kcm9wZG93biB7XHJcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgdG9wOiAxMDAlO1xyXG4gICAgICByaWdodDogMDtcclxuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjEyKTtcclxuICAgICAgbWluLXdpZHRoOiAyMjBweDtcclxuICAgICAgei1pbmRleDogMTAwMDtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA4KTtcclxuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgYW5pbWF0aW9uOiBkcm9wZG93bkZhZGVJbiAwLjNzIGVhc2U7XHJcblxyXG4gICAgICAuZHJvcGRvd24taXRlbSB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIHBhZGRpbmc6IDEwcHggMTRweDtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0zcHgpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaSwgLm1lbnUtaWNvbiB7XHJcbiAgICAgICAgICB3aWR0aDogMThweDtcclxuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAubWVudS1pY29uIHtcclxuICAgICAgICAgIHN2ZyB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxNnB4O1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDE2cHg7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzcGFuIHtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogIzJjM2U1MDtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJi5sb2dvdXQtaXRlbSB7XHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjVmNTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBzcGFuIHtcclxuICAgICAgICAgICAgY29sb3I6ICNlNzRjM2M7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmLm5ldy1yZXF1ZXN0LWl0ZW0ge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2U4ZjVlOCwgI2YwZjhmMCk7XHJcbiAgICAgICAgICBib3JkZXItdG9wOiAycHggc29saWQgIzI3YWU2MDtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Q0ZjRkNCwgI2U4ZjVlOCk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgc3BhbiB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjMjdhZTYwO1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmRyb3Bkb3duLWRpdmlkZXIge1xyXG4gICAgICAgIGhlaWdodDogMXB4O1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAa2V5ZnJhbWVzIGRyb3Bkb3duRmFkZUluIHtcclxuICAgICAgZnJvbSB7XHJcbiAgICAgICAgb3BhY2l0eTogMDtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpO1xyXG4gICAgICB9XHJcbiAgICAgIHRvIHtcclxuICAgICAgICBvcGFjaXR5OiAxO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gSGVybyBTZWN0aW9uXHJcbiAgLmhlcm8tc2VjdGlvbiB7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA4MHB4KTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgbWFyZ2luLXRvcDogMjBweDtcclxuICAgIC8vIG1hcmdpbi1sZWZ0OiAxMHB4O1xyXG4gICAgLy8gbWFyZ2luLXJpZ2h0OiAxMHB4O1xyXG5cclxuICAgIGJveC1zaGFkb3c6XHJcbiAgICAgIDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSksXHJcbiAgICAgIDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjEpLFxyXG4gICAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcclxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcclxuXHJcbiAgICAuaGVyby1iYWNrZ3JvdW5kIHtcclxuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICB0b3A6IDA7XHJcbiAgICAgIGxlZnQ6IDA7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgIHotaW5kZXg6IDE7XHJcblxyXG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG5cclxuICAgICAgLmhlcm8tYmctaW1hZ2Uge1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgICBvYmplY3QtcG9zaXRpb246IGNlbnRlcjtcclxuXHJcbiAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZTtcclxuICAgICAgfVxyXG5cclxuICAgICAgJjpob3ZlciAuaGVyby1iZy1pbWFnZSB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmhlcm8tb3ZlcmxheSB7XHJcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgIHRvcDogMDtcclxuICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuXHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KFxyXG4gICAgICAgICAgMTM1ZGVnLFxyXG4gICAgICAgICAgcmdiYSgzOCwgODMsIDE0NywgMC41MTUpIDAlLFxyXG4gICAgICAgICAgcmdiYSg1MiwgNzMsIDk0LCAwLjUpIDMwJSxcclxuICAgICAgICAgIHJnYmEoMzksIDE3NCwgOTUsIDAuNTE4KSA3MCUsXHJcbiAgICAgICAgICByZ2JhKDQ2LCAyMDQsIDExMywgMC44KSAxMDAlXHJcbiAgICAgICAgKTtcclxuICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMnB4KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5oZXJvLWNvbnRlbnQge1xyXG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgIHotaW5kZXg6IDU7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG5cclxuICAgICAgLmhlcm8tdGV4dC1jb250YWluZXIge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgcGFkZGluZzogMCA0cmVtO1xyXG5cclxuICAgICAgICAuaGVyby10ZXh0LWl0ZW0ge1xyXG4gICAgICAgICAgYW5pbWF0aW9uOiBmYWRlSW5VcCAxcyBlYXNlLW91dDtcclxuXHJcbiAgICAgICAgICAmOm50aC1jaGlsZCgxKSB7XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0O1xyXG4gICAgICAgICAgICBhbmltYXRpb24tZGVsYXk6IDAuMnM7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgJjpudGgtY2hpbGQoMikge1xyXG4gICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGFuaW1hdGlvbi1kZWxheTogMC40cztcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOm50aC1jaGlsZCgzKSB7XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgICAgICAgICAgIGFuaW1hdGlvbi1kZWxheTogMC42cztcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuaGVyby10ZXh0IHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAzcmVtO1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgIHRleHQtc2hhZG93OlxyXG4gICAgICAgICAgICAgIDAgMCAyMHB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSxcclxuICAgICAgICAgICAgICAycHggMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuNyksXHJcbiAgICAgICAgICAgICAgNHB4IDRweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC40KTtcclxuICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxLjVyZW0gMi41cmVtO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXHJcbiAgICAgICAgICAgICAgMTM1ZGVnLFxyXG4gICAgICAgICAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSkgMCUsXHJcbiAgICAgICAgICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KSAxMDAlXHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxNXB4KTtcclxuICAgICAgICAgICAgYm9yZGVyOiAycHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OlxyXG4gICAgICAgICAgICAgIDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjIpLFxyXG4gICAgICAgICAgICAgIDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEpLFxyXG4gICAgICAgICAgICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSk7XHJcbiAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgY29udGVudDogJyc7XHJcbiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICAgIHRvcDogMDtcclxuICAgICAgICAgICAgICBsZWZ0OiAtMTAwJTtcclxuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KFxyXG4gICAgICAgICAgICAgICAgOTBkZWcsXHJcbiAgICAgICAgICAgICAgICB0cmFuc3BhcmVudCxcclxuICAgICAgICAgICAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSxcclxuICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBsZWZ0IDAuNnMgZWFzZTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpIHNjYWxlKDEuMDgpO1xyXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcclxuICAgICAgICAgICAgICAgIDEzNWRlZyxcclxuICAgICAgICAgICAgICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yNSkgMCUsXHJcbiAgICAgICAgICAgICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMTAwJVxyXG4gICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzpcclxuICAgICAgICAgICAgICAgIDAgMjBweCA2MHB4IHJnYmEoMCwgMCwgMCwgMC4zKSxcclxuICAgICAgICAgICAgICAgIDAgMTBweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4yKSxcclxuICAgICAgICAgICAgICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpO1xyXG4gICAgICAgICAgICAgIHRleHQtc2hhZG93OlxyXG4gICAgICAgICAgICAgICAgMCAwIDMwcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpLFxyXG4gICAgICAgICAgICAgICAgMnB4IDJweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC44KSxcclxuICAgICAgICAgICAgICAgIDRweCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuNSk7XHJcblxyXG4gICAgICAgICAgICAgICY6OmJlZm9yZSB7XHJcbiAgICAgICAgICAgICAgICBsZWZ0OiAxMDAlO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gQW5pbWF0aW9uc1xyXG5Aa2V5ZnJhbWVzIGZhZGVJblVwIHtcclxuICBmcm9tIHtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMzBweCk7XHJcbiAgfVxyXG4gIHRvIHtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBSZXNwb25zaXZlIERlc2lnblxyXG5AbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7XHJcbiAgLmhvbWUtaGVhZGVyIHtcclxuICAgIC5oZXJvLXNlY3Rpb24ge1xyXG4gICAgICBtYXJnaW4tbGVmdDogMjBweDtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4O1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG5cclxuICAgICAgLmhlcm8tY29udGVudCB7XHJcbiAgICAgICAgLmhlcm8tdGV4dC1jb250YWluZXIge1xyXG4gICAgICAgICAgcGFkZGluZzogMCAycmVtO1xyXG5cclxuICAgICAgICAgIC5oZXJvLXRleHQtaXRlbSB7XHJcbiAgICAgICAgICAgIC5oZXJvLXRleHQge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMi41cmVtO1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDEuMnJlbSAycmVtO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuaG9tZS1oZWFkZXIge1xyXG4gICAgLm5hdmJhciB7XHJcbiAgICAgIC5jb250YWluZXItZmx1aWQge1xyXG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgZ2FwOiAxcmVtO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAubmF2LWxpc3Qge1xyXG4gICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuXHJcbiAgICAgICAgLm5hdi1pdGVtIHtcclxuICAgICAgICAgIC5uYXYtbGluayB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwLjRyZW0gMC44cmVtO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnVzZXItbGluayB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgcGFkZGluZzogMC41cmVtIDFyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC51c2VyLXByb2ZpbGUge1xyXG4gICAgICAgIHBhZGRpbmc6IDAuNHJlbSAwLjhyZW07XHJcblxyXG4gICAgICAgIC51c2VyLWF2YXRhciB7XHJcbiAgICAgICAgICB3aWR0aDogMzBweDtcclxuICAgICAgICAgIGhlaWdodDogMzBweDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC51c2VyLW5hbWUge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnVzZXItZHJvcGRvd24ge1xyXG4gICAgICAgIG1pbi13aWR0aDogMjAwcHg7XHJcbiAgICAgICAgcmlnaHQ6IC0xNXB4O1xyXG5cclxuICAgICAgICAuZHJvcGRvd24taXRlbSB7XHJcbiAgICAgICAgICBwYWRkaW5nOiA4cHggMTJweDtcclxuXHJcbiAgICAgICAgICBzcGFuIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaSwgLm1lbnUtaWNvbiB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxNnB4O1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5tZW51LWljb24ge1xyXG4gICAgICAgICAgICBzdmcge1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAxNHB4O1xyXG4gICAgICAgICAgICAgIGhlaWdodDogMTRweDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5oZXJvLXNlY3Rpb24ge1xyXG4gICAgICBtYXJnaW4tbGVmdDogMTVweDtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiAxNXB4O1xyXG4gICAgICBtYXJnaW4tdG9wOiAxNXB4O1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG5cclxuICAgICAgLmhlcm8tY29udGVudCB7XHJcbiAgICAgICAgLmhlcm8tdGV4dC1jb250YWluZXIge1xyXG4gICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICAgIGdhcDogMS41cmVtO1xyXG4gICAgICAgICAgcGFkZGluZzogMCAxcmVtO1xyXG5cclxuICAgICAgICAgIC5oZXJvLXRleHQtaXRlbSB7XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAgICAgLmhlcm8tdGV4dCB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAycmVtO1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDFyZW0gMS41cmVtO1xyXG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE1cHg7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzpcclxuICAgICAgICAgICAgICAgIDAgNnB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjIpLFxyXG4gICAgICAgICAgICAgICAgMCAzcHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xyXG4gIC5ob21lLWhlYWRlciB7XHJcbiAgICAubmF2YmFyIHtcclxuICAgICAgLm5hdmJhci1icmFuZCB7XHJcbiAgICAgICAgLmxvZ28taW1nIHtcclxuICAgICAgICAgIGhlaWdodDogNDBweDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuaGVyby1zZWN0aW9uIHtcclxuICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7XHJcbiAgICAgIG1hcmdpbi1yaWdodDogMTBweDtcclxuICAgICAgbWFyZ2luLXRvcDogMTBweDtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuXHJcbiAgICAgIC5oZXJvLWNvbnRlbnQge1xyXG4gICAgICAgIC5oZXJvLXRleHQtY29udGFpbmVyIHtcclxuICAgICAgICAgIHBhZGRpbmc6IDAgMC41cmVtO1xyXG5cclxuICAgICAgICAgIC5oZXJvLXRleHQtaXRlbSB7XHJcbiAgICAgICAgICAgIC5oZXJvLXRleHQge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDAuOHJlbSAxLjJyZW07XHJcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgICAgICAgICAgICBib3gtc2hhZG93OlxyXG4gICAgICAgICAgICAgICAgMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMiksXHJcbiAgICAgICAgICAgICAgICAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBNb2JpbGUgTmF2aWdhdGlvbiBTdHlsZXNcclxuLm5hdmJhci10b2dnbGVyIHtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZDogcmdiYSgwLCAxMjMsIDI1NSwgMC4xKTtcclxuICB9XHJcblxyXG4gICY6Zm9jdXMge1xyXG4gICAgYm94LXNoYWRvdzogbm9uZTtcclxuICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgfVxyXG5cclxuICAubmF2YmFyLXRvZ2dsZXItaWNvbiB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG5cclxuICAgIGkge1xyXG4gICAgICBmb250LXNpemU6IDEuMnJlbTtcclxuICAgICAgY29sb3I6ICMzMzM7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBNb2JpbGUgTmF2aWdhdGlvbiBEcm9wZG93blxyXG4ubW9iaWxlLW5hdi1kcm9wZG93biB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIHRvcDogY2FsYygxMDAlICsgLTEuOHJlbSkgIWltcG9ydGFudDtcclxuICBsZWZ0OiA1MCU7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xyXG4gIG1hcmdpbi1sZWZ0OiAtMjVweCAhaW1wb3J0YW50O1xyXG4gIHdpZHRoOiAyMDBweDtcclxuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgei1pbmRleDogOTk7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA4KTtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIGFuaW1hdGlvbjogZHJvcGRvd25GYWRlSW4gMC4zcyBlYXNlO1xyXG4gIG1hcmdpbi10b3A6IDA7XHJcblxyXG4gIC5kcm9wZG93bi1pdGVtIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICBjb2xvcjogIzMzMztcclxuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuXHJcbiAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICBib3JkZXItYm90dG9tOiBub25lO1xyXG4gICAgfVxyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDEyMywgMjU1LCAwLjA1KTtcclxuICAgICAgY29sb3I6ICMwMDdiZmY7XHJcbiAgICB9XHJcblxyXG4gICAgLm1lbnUtaWNvbiB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICB3aWR0aDogMThweDtcclxuICAgICAgaGVpZ2h0OiAxOHB4O1xyXG5cclxuICAgICAgaSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBzcGFuIHtcclxuICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBNb2JpbGUgZHJvcGRvd24gc2l6ZSBhZGp1c3RtZW50cyAtIHJlbW92ZWQgc2luY2UgbWFpbiBDU1MgYXBwbGllcyB0byBhbGwgc2NyZWVuc1xyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDU3NnB4KSB7XHJcbiAgLm1vYmlsZS1uYXYtZHJvcGRvd24ge1xyXG4gICAgd2lkdGg6IDE4MHB4ICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgLmRyb3Bkb3duLWl0ZW0ge1xyXG4gICAgICBwYWRkaW5nOiA2cHggMTBweCAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgLm1lbnUtaWNvbiB7XHJcbiAgICAgICAgd2lkdGg6IDE2cHggIWltcG9ydGFudDtcclxuICAgICAgICBoZWlnaHQ6IDE2cHggIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuN3JlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgc3BhbiB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIE1vYmlsZSBMYXlvdXQgLSBGb3JjZSBTaW5nbGUgTGluZVxyXG5AbWVkaWEgKG1heC13aWR0aDogOTkxLjk4cHgpIHtcclxuICAubmF2YmFyIHtcclxuICAgIC5jb250YWluZXItZmx1aWQge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbiAhaW1wb3J0YW50O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XHJcbiAgICAgIGZsZXgtd3JhcDogbm93cmFwICFpbXBvcnRhbnQ7XHJcbiAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IGF1dG8gIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAubmF2YmFyLWJyYW5kIHtcclxuICAgICAgZmxleDogMCAwIGF1dG8gIWltcG9ydGFudDtcclxuICAgICAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgICBpbWcge1xyXG4gICAgICAgIGhlaWdodDogMzVweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIHdpZHRoOiBhdXRvICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAubmF2YmFyLXRvZ2dsZXIge1xyXG4gICAgICBmbGV4OiAwIDAgYXV0byAhaW1wb3J0YW50O1xyXG4gICAgICBtYXJnaW46IDAgIWltcG9ydGFudDtcclxuICAgICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW0gIWltcG9ydGFudDtcclxuICAgICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7XHJcbiAgICAgIG9yZGVyOiAwICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcblxyXG4gICAgLm5hdmJhci1uYXYge1xyXG4gICAgICBmbGV4OiAwIDAgYXV0byAhaW1wb3J0YW50O1xyXG4gICAgICBtYXJnaW46IDAgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICYucG9zaXRpb24tcmVsYXRpdmUge1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAudXNlci1wcm9maWxlIHtcclxuICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXAgIWltcG9ydGFudDtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAudXNlci1hdmF0YXIge1xyXG4gICAgICAgICAgd2lkdGg6IDI4cHggIWltcG9ydGFudDtcclxuICAgICAgICAgIGhlaWdodDogMjhweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnVzZXItbmFtZSB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUgIWltcG9ydGFudDsgLy8gU2hvdyB1c2VybmFtZSBvbiBsYXJnZXIgbW9iaWxlIHNjcmVlbnNcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5mYXMuZmEtY2hldnJvbi1kb3duIHtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC43cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBtYXJnaW4tbGVmdDogMC4yNXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnVzZXItbGluayB7XHJcbiAgICAgICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBIaWRlIGRlc2t0b3AgbmF2aWdhdGlvblxyXG4gICAgLm5hdmJhci1uYXYubXgtYXV0byB7XHJcbiAgICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEV4dHJhIFNtYWxsIFNjcmVlbnNcclxuQG1lZGlhIChtYXgtd2lkdGg6IDU3NnB4KSB7XHJcbiAgLm5hdmJhciB7XHJcbiAgICAuY29udGFpbmVyLWZsdWlkIHtcclxuICAgICAgcGFkZGluZzogMC40cmVtIDAuNzVyZW0gIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAubmF2YmFyLWJyYW5kIGltZyB7XHJcbiAgICAgIGhlaWdodDogMzBweCAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG5cclxuICAgIC5uYXZiYXItdG9nZ2xlciB7XHJcbiAgICAgIHBhZGRpbmc6IDAuMnJlbSAwLjRyZW0gIWltcG9ydGFudDtcclxuXHJcbiAgICAgIGkge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbSAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLm5hdmJhci1uYXYge1xyXG4gICAgICAudXNlci1wcm9maWxlIHtcclxuICAgICAgICBwYWRkaW5nOiAwLjJyZW0gMC40cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjhyZW0gIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgLnVzZXItYXZhdGFyIHtcclxuICAgICAgICAgIHdpZHRoOiAyNHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBoZWlnaHQ6IDI0cHggIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC51c2VyLWxpbmsge1xyXG4gICAgICAgIHBhZGRpbmc6IDAuMnJlbSAwLjRyZW0gIWltcG9ydGFudDtcclxuICAgICAgICBmb250LXNpemU6IDAuOHJlbSAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICBzcGFuIHtcclxuICAgICAgICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGkge1xyXG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiAwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICBmb250LXNpemU6IDFyZW0gIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEZvcmNlIGhvcml6b250YWwgbGF5b3V0IG9uIGFsbCBtb2JpbGUgc2NyZWVuc1xyXG5AbWVkaWEgKG1heC13aWR0aDogOTkxLjk4cHgpIHtcclxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciB7XHJcbiAgICAuY29udGFpbmVyLWZsdWlkIHtcclxuICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogcm93ICFpbXBvcnRhbnQ7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbiAhaW1wb3J0YW50O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XHJcbiAgICAgIGZsZXgtd3JhcDogbm93cmFwICFpbXBvcnRhbnQ7XHJcbiAgICAgIG92ZXJmbG93OiB2aXNpYmxlICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRW5zdXJlIGFsbCBkaXJlY3QgY2hpbGRyZW4gc3RheSBvbiBzYW1lIGxpbmVcclxuICAgID4gLmNvbnRhaW5lci1mbHVpZCA+ICoge1xyXG4gICAgICBmbGV4LXNocmluazogMCAhaW1wb3J0YW50O1xyXG4gICAgICB3aGl0ZS1zcGFjZTogbm93cmFwICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBWZXJ5IHNtYWxsIHNjcmVlbnMgLSBIaWRlIHVzZXJuYW1lIG9ubHkgYmVsb3cgMzIwcHhcclxuQG1lZGlhIChtYXgtd2lkdGg6IDMxOXB4KSB7XHJcbiAgLm5hdmJhciB7XHJcbiAgICAubmF2YmFyLW5hdiB7XHJcbiAgICAgIC51c2VyLXByb2ZpbGUge1xyXG4gICAgICAgIC51c2VyLW5hbWUge1xyXG4gICAgICAgICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OyAvLyBIaWRlIHVzZXJuYW1lIG9ubHkgb24gdmVyeSBzbWFsbCBzY3JlZW5zXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBQcm9wZXJ0aWVzIFNlY3Rpb25cclxuLnByb3BlcnRpZXMtc2VjdGlvbiB7XHJcbiAgcGFkZGluZzogNTBweCAwO1xyXG5cclxuICAvLyBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZmZmZmIDAlLCAjZmZmZmZmIDEwMCUpO1xyXG5cclxuICAuc2VjdGlvbi10aXRsZSB7XHJcbiAgICBmb250LXNpemU6IDIuNXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICBjb2xvcjogIzJjM2U1MDtcclxuICAgIG1hcmdpbi1ib3R0b206IDRyZW07XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgJjo6YWZ0ZXIge1xyXG4gICAgICBjb250ZW50OiAnJztcclxuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICBib3R0b206IC0xNXB4O1xyXG4gICAgICBsZWZ0OiA1MCU7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcclxuICAgICAgd2lkdGg6IDgwcHg7XHJcbiAgICAgIGhlaWdodDogNHB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMzNDk4ZGIsICMyOTgwYjkpO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4ucHJvcGVydHktY2FyZCB7XHJcbiAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIGJveC1zaGFkb3c6IDAgMTBweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIGhlaWdodDogMTAwJTtcclxuICBtYXJnaW4tdG9wOiAzMHB4O1xyXG4gY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XHJcbiAgICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gIH1cclxuXHJcbiAgLnByb3BlcnR5LWltYWdlIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIGhlaWdodDogMjAwcHg7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG5cclxuICAgIGltZyB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xyXG4gICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgfVxyXG5cclxuICAgICY6aG92ZXIgaW1nIHtcclxuICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgfVxyXG5cclxuICAgIC5wcm9wZXJ0eS1iYWRnZSB7XHJcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgdG9wOiAxNXB4O1xyXG4gICAgICBsZWZ0OiAxNXB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjdhZTYwLCAjMmVjYzcxKTtcclxuICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICBwYWRkaW5nOiA1cHggMTJweDtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMjBweDtcclxuICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDtcclxuXHJcbiAgICAgICYucHJvcGVydHktYmFkZ2UtcmVudCB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2U3NGMzYywgI2MwMzkyYik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAucHJvcGVydHktbG9jYXRpb24ge1xyXG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgIGJvdHRvbTogMTVweDtcclxuICAgICAgbGVmdDogMTVweDtcclxuICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpO1xyXG4gICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgIHBhZGRpbmc6IDVweCAxMHB4O1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiA1cHg7XHJcblxyXG4gICAgICBpIHtcclxuICAgICAgICBmb250LXNpemU6IDAuN3JlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLnByb3BlcnR5LWNvbnRlbnQge1xyXG4gICAgcGFkZGluZzogMjBweDtcclxuXHJcbiAgICAucHJvcGVydHktdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDEuM3JlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgY29sb3I6ICMyYzNlNTA7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuMztcclxuICAgIH1cclxuXHJcbiAgICAucHJvcGVydHktZGVzY3JpcHRpb24ge1xyXG4gICAgICBjb2xvcjogIzdmOGM4ZDtcclxuICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgICB9XHJcblxyXG4gICAgLnByb3BlcnR5LXByaWNlIHtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDtcclxuXHJcbiAgICAgIC5wcmljZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjRyZW07XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgICBjb2xvcjogIzM0OThkYjtcclxuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTtcclxuICAgICAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcclxuICAgICAgICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnByb3BlcnR5LXJhdGluZyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMDtcclxuXHJcbiAgICAgIC5zdGFycyB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBnYXA6IDJweDtcclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICBjb2xvcjogI2YzOWMxMjtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnJhdGluZy10ZXh0IHtcclxuICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIGNvbG9yOiAjMmMzZTUwO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgIG1hcmdpbi1yaWdodDogMTVweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5wcm9wZXJ0eS1hY3Rpb25zIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZ2FwOiAxMHB4O1xyXG5cclxuICAgICAgLmJ0biB7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgICAgICAgY29sb3I6ICM3ZjhjOGQ7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBib3JkZXItY29sb3I6ICMzNDk4ZGI7XHJcbiAgICAgICAgICBjb2xvcjogIzM0OThkYjtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjEpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBSYXRpbmcgYW5kIEFjdGlvbnMgb24gc2FtZSBsaW5lXHJcbiAgICAucHJvcGVydHktcmF0aW5nLWFjdGlvbnMge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDA7XHJcblxyXG4gICAgICAucHJvcGVydHktcmF0aW5nIHtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wcm9wZXJ0eS1hY3Rpb25zIHtcclxuICAgICAgICBmbGV4OiAwIDAgYXV0bztcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gUmVzcG9uc2l2ZSBEZXNpZ24gZm9yIFByb3BlcnRpZXNcclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnByb3BlcnRpZXMtc2VjdGlvbiB7XHJcbiAgICBwYWRkaW5nOiA2MHB4IDA7XHJcblxyXG4gICAgLnNlY3Rpb24tdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAucHJvcGVydHktY2FyZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG5cclxuICAgIC5wcm9wZXJ0eS1pbWFnZSB7XHJcbiAgICAgIGhlaWdodDogMTgwcHg7XHJcbiAgICB9XHJcblxyXG4gICAgLnByb3BlcnR5LWNvbnRlbnQge1xyXG4gICAgICBwYWRkaW5nOiAxNXB4O1xyXG5cclxuICAgICAgLnByb3BlcnR5LXRpdGxlIHtcclxuICAgICAgICBmb250LXNpemU6IDEuMnJlbTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLnByb3BlcnR5LXByaWNlIC5wcmljZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjJyZW07XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xyXG4gIC5wcm9wZXJ0aWVzLXNlY3Rpb24ge1xyXG4gICAgcGFkZGluZzogNDBweCAwO1xyXG5cclxuICAgIC5zZWN0aW9uLXRpdGxlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjhyZW07XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5wcm9wZXJ0eS1jYXJkIHtcclxuICAgIC5wcm9wZXJ0eS1pbWFnZSB7XHJcbiAgICAgIGhlaWdodDogMTYwcHg7XHJcblxyXG4gICAgICAucHJvcGVydHktYmFkZ2Uge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC43cmVtO1xyXG4gICAgICAgIHBhZGRpbmc6IDRweCA4cHg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wcm9wZXJ0eS1sb2NhdGlvbiB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjdyZW07XHJcbiAgICAgICAgcGFkZGluZzogNHB4IDhweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5wcm9wZXJ0eS1jb250ZW50IHtcclxuICAgICAgcGFkZGluZzogMTJweDtcclxuXHJcbiAgICAgIC5wcm9wZXJ0eS10aXRsZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wcm9wZXJ0eS1kZXNjcmlwdGlvbiB7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wcm9wZXJ0eS1wcmljZSAucHJpY2Uge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAucHJvcGVydHktcmF0aW5nIHtcclxuICAgICAgICAuc3RhcnMgaSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5yYXRpbmctdGV4dCB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wcm9wZXJ0eS1hY3Rpb25zIHtcclxuICAgICAgICAuYnRuIHtcclxuICAgICAgICAgIHBhZGRpbmc6IDZweCAxMHB4O1xyXG5cclxuICAgICAgICAgIGkge1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEhvcml6b250YWwgQ2Fyb3VzZWwgU2VjdGlvblxyXG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjhGOEY4O1xyXG4gIG1hcmdpbi10b3A6IDkwcHg7XHJcblxyXG4gIC5zZWN0aW9uLXRpdGxlIHtcclxuICAgIGZvbnQtc2l6ZTogMi41cmVtO1xyXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgIGNvbG9yOiAjMmMzZTUwO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogM3JlbTtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcblxyXG4gICAgJjo6YWZ0ZXIge1xyXG4gICAgICBjb250ZW50OiAnJztcclxuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICBib3R0b206IC0xNXB4O1xyXG4gICAgICBsZWZ0OiA1MCU7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcclxuICAgICAgd2lkdGg6IDgwcHg7XHJcbiAgICAgIGhlaWdodDogNHB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMwMDdiZmYsICMwMDU2YjMpO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcblxyXG5cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jYXJvdXNlbC1jb250YWluZXIge1xyXG4gICAgbWFyZ2luOiAwIDMwcHg7XHJcbiAgICBtYXJnaW4tdG9wOiAzMHB4O1xyXG5cclxuXHJcbiAgICAuY2Fyb3VzZWwge1xyXG4gICAgICAuY2Fyb3VzZWwtaW5uZXIge1xyXG4gICAgICAgIC5jYXJvdXNlbC1pdGVtIHtcclxuICAgICAgICAgIC5sb2NhdGlvbi1jYXJkIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDIwMHB4O1xyXG5cclxuXHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAxNXB4IDM1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuXHJcbiAgICAgICAgICAgICAgaW1nIHtcclxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAubG9jYXRpb24tb3ZlcmxheSB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNyk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpbWcge1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAubG9jYXRpb24tb3ZlcmxheSB7XHJcbiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICAgIGJvdHRvbTogMDtcclxuICAgICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTtcclxuICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgICAgcGFkZGluZzogMTVweDtcclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG5cclxuICAgICAgICAgICAgICAubG9jYXRpb24taW5mbyB7XHJcbiAgICAgICAgICAgICAgICBoNSB7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICAgICAgICAgICAgdGV4dC1zaGFkb3c6IDFweCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC41KTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBwIHtcclxuICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg1cmVtO1xyXG4gICAgICAgICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuOTtcclxuICAgICAgICAgICAgICAgICAgdGV4dC1zaGFkb3c6IDFweCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC41KTtcclxuXHJcbiAgICAgICAgICAgICAgICAgIGkge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNXB4O1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZmZkNzAwO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDdXN0b20gQ2Fyb3VzZWwgQ29udHJvbHNcclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgIHRvcDogNTAlO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTtcclxuICAgICAgICB3aWR0aDogNTBweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGhlaWdodDogNTBweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICMwMzE3NTI7XHJcbiAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICBvcGFjaXR5OiAxO1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDMwLCA1OCwgMTM4LCAwLjMpO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICMxZTQwYWY7XHJcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSkgc2NhbGUoMS4xKTtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgzMCwgNTgsIDEzOCwgMC40KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5jYXJvdXNlbC1jb250cm9sLXByZXYtaWNvbixcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0LWljb24ge1xyXG4gICAgICAgICAgd2lkdGg6IDIwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IDIwcHggMjBweDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jYXJvdXNlbC1jb250cm9sLXByZXYge1xyXG4gICAgICAgIGxlZnQ6IDFweDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgcmlnaHQ6IDFweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gRXh0cmEgTGFyZ2Ugc2NyZWVucyAoMTQwMHB4IGFuZCB1cClcclxuQG1lZGlhIChtaW4td2lkdGg6IDE0MDBweCkge1xyXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24ge1xyXG4gICAgLmNhcm91c2VsLWNvbnRhaW5lciB7XHJcbiAgICAgIG1hcmdpbjogMCA1MHB4O1xyXG5cclxuICAgICAgLmNhcm91c2VsIHtcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxyXG4gICAgICAgIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xyXG4gICAgICAgICAgd2lkdGg6IDYwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDYwcHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAubG9jYXRpb24tY2FyZCB7XHJcbiAgICAgICAgaGVpZ2h0OiAyMjBweDtcclxuXHJcbiAgICAgICAgLmxvY2F0aW9uLW92ZXJsYXkge1xyXG4gICAgICAgICAgLmxvY2F0aW9uLWluZm8ge1xyXG4gICAgICAgICAgICBoNSB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxLjNyZW07XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIHAge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gTGFyZ2Ugc2NyZWVucyAoOTkycHggdG8gMTM5OXB4KVxyXG5AbWVkaWEgKG1pbi13aWR0aDogOTkycHgpIGFuZCAobWF4LXdpZHRoOiAxMzk5cHgpIHtcclxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIHtcclxuICAgIC5jYXJvdXNlbC1jb250YWluZXIge1xyXG4gICAgICBtYXJnaW46IDAgNDBweDtcclxuXHJcbiAgICAgIC5sb2NhdGlvbi1jYXJkIHtcclxuICAgICAgICBoZWlnaHQ6IDIwMHB4O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBNZWRpdW0gc2NyZWVucyAoNzY4cHggdG8gOTkxcHgpXHJcbkBtZWRpYSAobWluLXdpZHRoOiA3NjhweCkgYW5kIChtYXgtd2lkdGg6IDk5MXB4KSB7XHJcbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiB7XHJcbiAgICAuc2VjdGlvbi10aXRsZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMi4ycmVtO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAyLjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLmNhcm91c2VsLWNvbnRhaW5lciB7XHJcbiAgICAgIG1hcmdpbjogMCAzMHB4O1xyXG5cclxuICAgICAgLmNhcm91c2VsIHtcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxyXG4gICAgICAgIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xyXG4gICAgICAgICAgd2lkdGg6IDQ1cHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDQ1cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcclxuICAgICAgICAgIGxlZnQ6IC01cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcclxuICAgICAgICAgIHJpZ2h0OiAtNXB4O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmxvY2F0aW9uLWNhcmQge1xyXG4gICAgICAgIGhlaWdodDogMTgwcHg7XHJcblxyXG4gICAgICAgIC5sb2NhdGlvbi1vdmVybGF5IHtcclxuICAgICAgICAgIHBhZGRpbmc6IDEycHg7XHJcblxyXG4gICAgICAgICAgLmxvY2F0aW9uLWluZm8ge1xyXG4gICAgICAgICAgICBoNSB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgcCB7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFNtYWxsIHNjcmVlbnMgKDU3NnB4IHRvIDc2N3B4KVxyXG5AbWVkaWEgKG1pbi13aWR0aDogNTc2cHgpIGFuZCAobWF4LXdpZHRoOiA3NjdweCkge1xyXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24ge1xyXG4gICAgLnNlY3Rpb24tdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLmNhcm91c2VsLWNvbnRhaW5lciB7XHJcbiAgICAgIG1hcmdpbjogMCAyMHB4O1xyXG5cclxuICAgICAgLmNhcm91c2VsIHtcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxyXG4gICAgICAgIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xyXG4gICAgICAgICAgd2lkdGg6IDQwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDQwcHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcclxuICAgICAgICAgIGxlZnQ6IC0xMHB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgICByaWdodDogLTEwcHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAubG9jYXRpb24tY2FyZCB7XHJcbiAgICAgICAgaGVpZ2h0OiAxNjBweDtcclxuXHJcbiAgICAgICAgLmxvY2F0aW9uLW92ZXJsYXkge1xyXG4gICAgICAgICAgcGFkZGluZzogMTBweDtcclxuXHJcbiAgICAgICAgICAubG9jYXRpb24taW5mbyB7XHJcbiAgICAgICAgICAgIGg1IHtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuOTVyZW07XHJcbiAgICAgICAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBwIHtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEV4dHJhIFNtYWxsIHNjcmVlbnMgKHVwIHRvIDU3NXB4KVxyXG5AbWVkaWEgKG1heC13aWR0aDogNTc1cHgpIHtcclxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIHtcclxuICAgIHBhZGRpbmc6IDQwcHggMDtcclxuXHJcbiAgICAuc2VjdGlvbi10aXRsZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS44cmVtO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLmNhcm91c2VsLWNvbnRhaW5lciB7XHJcbiAgICAgIG1hcmdpbjogMCAxNXB4O1xyXG5cclxuICAgICAgLmNhcm91c2VsIHtcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxyXG4gICAgICAgIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xyXG4gICAgICAgICAgd2lkdGg6IDM1cHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDM1cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcclxuICAgICAgICAgIGxlZnQ6IC0xNXB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgICByaWdodDogLTE1cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LWljb24sXHJcbiAgICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uIHtcclxuICAgICAgICAgIHdpZHRoOiAxNnB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiAxNnB4O1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1zaXplOiAxNnB4IDE2cHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAubG9jYXRpb24tY2FyZCB7XHJcbiAgICAgICAgaGVpZ2h0OiAxNDBweDtcclxuXHJcbiAgICAgICAgLmxvY2F0aW9uLW92ZXJsYXkge1xyXG4gICAgICAgICAgcGFkZGluZzogOHB4O1xyXG5cclxuICAgICAgICAgIC5sb2NhdGlvbi1pbmZvIHtcclxuICAgICAgICAgICAgaDUge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcclxuICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogM3B4O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBwIHtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuN3JlbTtcclxuXHJcbiAgICAgICAgICAgICAgaSB7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDNweDtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFZlcnkgU21hbGwgc2NyZWVucyAodXAgdG8gNDAwcHgpXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA0MDBweCkge1xyXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24ge1xyXG4gICAgLmNhcm91c2VsLWNvbnRhaW5lciB7XHJcbiAgICAgIG1hcmdpbjogMCAxMHB4O1xyXG5cclxuICAgICAgLmNhcm91c2VsIHtcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcclxuICAgICAgICAgIGxlZnQ6IC0yMHB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgICByaWdodDogLTIwcHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAubG9jYXRpb24tY2FyZCB7XHJcbiAgICAgICAgaGVpZ2h0OiAxMjBweDtcclxuXHJcbiAgICAgICAgLmxvY2F0aW9uLW92ZXJsYXkge1xyXG4gICAgICAgICAgcGFkZGluZzogNnB4O1xyXG5cclxuICAgICAgICAgIC5sb2NhdGlvbi1pbmZvIHtcclxuICAgICAgICAgICAgaDUge1xyXG4gICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBwIHtcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDAuNjVyZW07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEFydGljbGVzIFNlY3Rpb25cclxuLmFydGljbGVzLXNlY3Rpb24ge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XHJcbiAgcGFkZGluZzogMTAwcHggMDtcclxuICBtYXJnaW4tdG9wOiA4MHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDUwcHg7XHJcblxyXG4gIC5zZWN0aW9uLWhlYWRlciB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiA2MHB4O1xyXG5cclxuICAgIC5sZWZ0LWNvbnRyb2xzIHtcclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtYnRuIHtcclxuICAgICAgICB3aWR0aDogNTBweDtcclxuICAgICAgICBoZWlnaHQ6IDUwcHg7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjMWUzYThhO1xyXG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMzAsIDU4LCAxMzgsIDAuMjUpO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICMxZTQwYWY7XHJcbiAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xyXG4gICAgICAgICAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDMwLCA1OCwgMTM4LCAwLjM1KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGkge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxOHB4O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5hcnRpY2xlcy10aXRsZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMi44cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogODAwO1xyXG4gICAgICBjb2xvcjogIzFlM2E4YTtcclxuICAgICAgdGV4dC1zaGFkb3c6IDJweCAycHggNHB4IHJnYmEoMzAsIDU4LCAxMzgsIDAuMSk7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgbGV0dGVyLXNwYWNpbmc6IC0wLjVweDtcclxuICAgICAgZGlyZWN0aW9uOiBsdHI7XHJcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgbWFyZ2luLWxlZnQ6IDIwMHB4O1xyXG4gICAgfVxyXG5cclxuICAgIC5yaWdodC1saW5rIHtcclxuICAgICAgLnZpZXctYWxsLWxpbmsge1xyXG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgICAgICBmb250LXNpemU6IDEuNHJlbTtcclxuICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgICBkaXJlY3Rpb246IGx0cjtcclxuXHJcbiAgICAgICAgLnRleHQtc3VjY2VzcyB7XHJcbiAgICAgICAgICBjb2xvcjogIzI4YTc0NSAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmdyZWVuLXVuZGVybGluZSB7XHJcbiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICBib3R0b206IC01cHg7XHJcbiAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICBoZWlnaHQ6IDNweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICMyOGE3NDU7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNXB4KTtcclxuXHJcbiAgICAgICAgICAudGV4dC1zdWNjZXNzIHtcclxuICAgICAgICAgICAgY29sb3I6ICMxZTdlMzQgIWltcG9ydGFudDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuZ3JlZW4tdW5kZXJsaW5lIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogIzFlN2UzNDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBpIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0zcHgpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgI2FydGljbGVzQ2Fyb3VzZWwge1xyXG4gICAgLmNhcm91c2VsLWlubmVyIHtcclxuICAgICAgLmNhcm91c2VsLWl0ZW0ge1xyXG4gICAgICAgIC5hcnRpY2xlLWNhcmQge1xyXG4gICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMjBweDtcclxuICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuMjUsIDAuNDYsIDAuNDUsIDAuOTQpO1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgaGVpZ2h0OiA0MDBweDtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpO1xyXG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG5cclxuICAgICAgICAgICAgLmFydGljbGUtaW1hZ2UgaW1nIHtcclxuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDMpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuYXJ0aWNsZS1vdmVybGF5IHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodHJhbnNwYXJlbnQsIHJnYmEoMCwgMCwgMCwgMC45KSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuYXJ0aWNsZS1pbWFnZSB7XHJcbiAgICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG5cclxuICAgICAgICAgICAgaW1nIHtcclxuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7XHJcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuNnMgY3ViaWMtYmV6aWVyKDAuMjUsIDAuNDYsIDAuNDUsIDAuOTQpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuYXJ0aWNsZS1vdmVybGF5IHtcclxuICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgICAgYm90dG9tOiAwO1xyXG4gICAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgICAgcmlnaHQ6IDA7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRyYW5zcGFyZW50LCByZ2JhKDAsIDAsIDAsIDAuODUpKTtcclxuICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgICAgcGFkZGluZzogNDBweCAyNXB4IDI1cHg7XHJcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuMjUsIDAuNDYsIDAuNDUsIDAuOTQpO1xyXG5cclxuICAgICAgICAgICAgICAuYXJ0aWNsZS1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgIGRpcmVjdGlvbjogbHRyO1xyXG4gICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogbGVmdDtcclxuXHJcbiAgICAgICAgICAgICAgICBoNCB7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS40cmVtO1xyXG4gICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4O1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgICAgICAgIHRleHQtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjMpO1xyXG4gICAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS4zO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIHAge1xyXG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC45NTtcclxuICAgICAgICAgICAgICAgICAgdGV4dC1zaGFkb3c6IDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMyk7XHJcbiAgICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ3VzdG9tIENhcm91c2VsIENvbnRyb2xzIGZvciBBcnRpY2xlc1xyXG4gICAgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcclxuICAgIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xyXG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgIHRvcDogNTAlO1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XHJcbiAgICAgIHdpZHRoOiA1MHB4O1xyXG4gICAgICBoZWlnaHQ6IDUwcHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICMxZTNhOGE7XHJcbiAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICBvcGFjaXR5OiAxO1xyXG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMzAsIDU4LCAxMzgsIDAuMyk7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjMWU0MGFmO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKSBzY2FsZSgxLjEpO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgzMCwgNTgsIDEzOCwgMC40KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtcHJldi1pY29uLFxyXG4gICAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0LWljb24ge1xyXG4gICAgICAgIHdpZHRoOiAyMHB4O1xyXG4gICAgICAgIGhlaWdodDogMjBweDtcclxuICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IDIwcHggMjBweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5jYXJvdXNlbC1jb250cm9sLXByZXYge1xyXG4gICAgICBsZWZ0OiAtMjVweDtcclxuICAgIH1cclxuXHJcbiAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcclxuICAgICAgcmlnaHQ6IC0yNXB4O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEN1c3RvbSBJbmRpY2F0b3JzXHJcbiAgICAuY2Fyb3VzZWwtaW5kaWNhdG9ycyB7XHJcbiAgICAgIGJvdHRvbTogLTUwcHg7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDA7XHJcblxyXG4gICAgICBidXR0b24ge1xyXG4gICAgICAgIHdpZHRoOiAxMnB4O1xyXG4gICAgICAgIGhlaWdodDogMTJweDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMzAsIDU4LCAxMzgsIDAuMyk7XHJcbiAgICAgICAgbWFyZ2luOiAwIDZweDtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG5cclxuICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMWUzYThhO1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMWU0MGFmO1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gUmVzcG9uc2l2ZSBEZXNpZ24gZm9yIEFydGljbGVzIFNlY3Rpb25cclxuQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkge1xyXG4gIC5hcnRpY2xlcy1zZWN0aW9uIHtcclxuICAgICNhcnRpY2xlc0Nhcm91c2VsIHtcclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtcHJldiB7XHJcbiAgICAgICAgbGVmdDogLTE1cHg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xyXG4gICAgICAgIHJpZ2h0OiAtMTVweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDk5MnB4KSB7XHJcbiAgLmFydGljbGVzLXNlY3Rpb24ge1xyXG4gICAgLnNlY3Rpb24taGVhZGVyIHtcclxuICAgICAgLmFydGljbGVzLXRpdGxlIHtcclxuICAgICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC52aWV3LWFsbC1saW5rIGEge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgI2FydGljbGVzQ2Fyb3VzZWwge1xyXG4gICAgICAuY2Fyb3VzZWwtaW5uZXIge1xyXG4gICAgICAgIC5jYXJvdXNlbC1pdGVtIHtcclxuICAgICAgICAgIC5hcnRpY2xlLWNhcmQge1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDI4MHB4O1xyXG5cclxuICAgICAgICAgICAgLmFydGljbGUtaW1hZ2Uge1xyXG4gICAgICAgICAgICAgIC5hcnRpY2xlLW92ZXJsYXkge1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMjVweDtcclxuXHJcbiAgICAgICAgICAgICAgICAuYXJ0aWNsZS1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICBwIHtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgd2lkdGg6IDQ1cHg7XHJcbiAgICAgICAgaGVpZ2h0OiA0NXB4O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuYXJ0aWNsZXMtc2VjdGlvbiB7XHJcbiAgICAuc2VjdGlvbi1oZWFkZXIge1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydCAhaW1wb3J0YW50O1xyXG4gICAgICBnYXA6IDE1cHg7XHJcblxyXG4gICAgICAuYXJ0aWNsZXMtdGl0bGUge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMS44cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgI2FydGljbGVzQ2Fyb3VzZWwge1xyXG4gICAgICAuY2Fyb3VzZWwtaW5uZXIge1xyXG4gICAgICAgIC5jYXJvdXNlbC1pdGVtIHtcclxuICAgICAgICAgIC5hcnRpY2xlLWNhcmQge1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDI1MHB4O1xyXG5cclxuICAgICAgICAgICAgLmFydGljbGUtaW1hZ2Uge1xyXG4gICAgICAgICAgICAgIC5hcnRpY2xlLW92ZXJsYXkge1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMjBweDtcclxuXHJcbiAgICAgICAgICAgICAgICAuYXJ0aWNsZS1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgICAgaDQge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgIHAge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcclxuICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XHJcbiAgICAgICAgd2lkdGg6IDQwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiA0MHB4O1xyXG4gICAgICAgIHRvcDogNDUlO1xyXG5cclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LWljb24sXHJcbiAgICAgICAgLmNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uIHtcclxuICAgICAgICAgIHdpZHRoOiAxNnB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiAxNnB4O1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1zaXplOiAxNnB4IDE2cHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcclxuICAgICAgICBsZWZ0OiAxMHB4O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcclxuICAgICAgICByaWdodDogMTBweDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmNhcm91c2VsLWluZGljYXRvcnMge1xyXG4gICAgICAgIGJvdHRvbTogLTQwcHg7XHJcblxyXG4gICAgICAgIGJ1dHRvbiB7XHJcbiAgICAgICAgICB3aWR0aDogMTBweDtcclxuICAgICAgICAgIGhlaWdodDogMTBweDtcclxuICAgICAgICAgIG1hcmdpbjogMCA0cHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcclxuICAuYXJ0aWNsZXMtc2VjdGlvbiB7XHJcbiAgICAuc2VjdGlvbi1oZWFkZXIge1xyXG4gICAgICAuYXJ0aWNsZXMtdGl0bGUge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMS42cmVtO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAudmlldy1hbGwtbGluayBhIHtcclxuICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgICNhcnRpY2xlc0Nhcm91c2VsIHtcclxuICAgICAgLmNhcm91c2VsLWlubmVyIHtcclxuICAgICAgICAuY2Fyb3VzZWwtaXRlbSB7XHJcbiAgICAgICAgICAuYXJ0aWNsZS1jYXJkIHtcclxuICAgICAgICAgICAgaGVpZ2h0OiAyMjBweDtcclxuXHJcbiAgICAgICAgICAgIC5hcnRpY2xlLWltYWdlIHtcclxuICAgICAgICAgICAgICAuYXJ0aWNsZS1vdmVybGF5IHtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDE1cHg7XHJcblxyXG4gICAgICAgICAgICAgICAgLmFydGljbGUtY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICAgIGg0IHtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICBwIHtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcclxuICAgICAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS40O1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxyXG4gICAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcclxuICAgICAgICB3aWR0aDogMzVweDtcclxuICAgICAgICBoZWlnaHQ6IDM1cHg7XHJcblxyXG4gICAgICAgIC5jYXJvdXNlbC1jb250cm9sLXByZXYtaWNvbixcclxuICAgICAgICAuY2Fyb3VzZWwtY29udHJvbC1uZXh0LWljb24ge1xyXG4gICAgICAgICAgd2lkdGg6IDE0cHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDE0cHg7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IDE0cHggMTRweDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jYXJvdXNlbC1pbmRpY2F0b3JzIHtcclxuICAgICAgICBib3R0b206IC0zNXB4O1xyXG5cclxuICAgICAgICBidXR0b24ge1xyXG4gICAgICAgICAgd2lkdGg6IDhweDtcclxuICAgICAgICAgIGhlaWdodDogOHB4O1xyXG4gICAgICAgICAgbWFyZ2luOiAwIDNweDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcblxyXG4iLCIuaG9tZS1wYWdlIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50O1xufVxuXG4uaG9tZS1oZWFkZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBtaW4taGVpZ2h0OiAxMjB2aDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxMDtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICBwYWRkaW5nOiAxcmVtIDA7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDEwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLmNvbnRhaW5lci1mbHVpZCB7XG4gIG1heC13aWR0aDogMTQwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLm5hdmJhci1icmFuZCAubG9nby1pbWcge1xuICBoZWlnaHQ6IDUwcHg7XG4gIHdpZHRoOiBhdXRvO1xuICBvYmplY3QtZml0OiBjb250YWluO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLm5hdi1saXN0IHtcbiAgbGlzdC1zdHlsZTogbm9uZTtcbiAgZ2FwOiAycmVtO1xuICBtYXJnaW46IDA7XG4gIHBhZGRpbmc6IDA7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAubmF2LWxpc3QgLm5hdi1pdGVtIC5uYXYtbGluayB7XG4gIGNvbG9yOiAjMmMzZTUwO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgcGFkZGluZzogMC41cmVtIDFyZW07XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgZGlyZWN0aW9uOiBydGw7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAubmF2LWxpc3QgLm5hdi1pdGVtIC5uYXYtbGluazpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG4gIGNvbG9yOiAjMjdhZTYwO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAubmF2LWxpc3QgLm5hdi1pdGVtIC5uYXYtbGluay51c2VyLWxpbmsge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjdhZTYwLCAjMmVjYzcxKTtcbiAgY29sb3I6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAyNXB4O1xuICBwYWRkaW5nOiAwLjdyZW0gMS41cmVtO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLm5hdi1saXN0IC5uYXYtaXRlbSAubmF2LWxpbmsudXNlci1saW5rOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzIyOTk1NCwgIzI3YWU2MCk7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDM5LCAxNzQsIDk2LCAwLjMpO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLm5hdi1saXN0IC5uYXYtaXRlbSAubmF2LWxpbmsudXNlci1saW5rIGkge1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWxpbmsge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjdhZTYwLCAjMmVjYzcxKTtcbiAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7XG4gIGJvcmRlci1yYWRpdXM6IDI1cHg7XG4gIHBhZGRpbmc6IDAuN3JlbSAxLjVyZW07XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWxpbms6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMjI5OTU0LCAjMjdhZTYwKTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMzksIDE3NCwgOTYsIDAuMyk7XG4gIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50O1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1saW5rIGkge1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLXByb2ZpbGUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gIGJvcmRlci1yYWRpdXM6IDI1cHg7XG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1MCwgMjUwLCAyNTAsIDAuMyk7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGN1cnNvcjogcG9pbnRlcjtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLXByb2ZpbGU6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIGJvcmRlci1jb2xvcjogcmdiYSgzOSwgMTc0LCA5NiwgMC41KTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMzksIDE3NCwgOTYsIDAuMik7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1wcm9maWxlIC51c2VyLWF2YXRhciB7XG4gIHdpZHRoOiAzNXB4O1xuICBoZWlnaHQ6IDM1cHg7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG4gIGJvcmRlcjogMnB4IHNvbGlkICMyN2FlNjA7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1wcm9maWxlIC51c2VyLW5hbWUge1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzJjM2U1MDtcbiAgZm9udC1zaXplOiAwLjk1cmVtO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItcHJvZmlsZSBpLmZhLWNoZXZyb24tZG93biB7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBjb2xvcjogIzI3YWU2MDtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZTtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWRyb3Bkb3duIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDEwMCU7XG4gIHJpZ2h0OiAwO1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDAsIDAsIDAsIDAuMTIpO1xuICBtaW4td2lkdGg6IDIyMHB4O1xuICB6LWluZGV4OiAxMDAwO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBhbmltYXRpb246IGRyb3Bkb3duRmFkZUluIDAuM3MgZWFzZTtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWRyb3Bkb3duIC5kcm9wZG93bi1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMTBweCAxNHB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW06aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTNweCk7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSBpLCAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSAubWVudS1pY29uIHtcbiAgd2lkdGg6IDE4cHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSAubWVudS1pY29uIHN2ZyB7XG4gIHdpZHRoOiAxNnB4O1xuICBoZWlnaHQ6IDE2cHg7XG59XG4uaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSBzcGFuIHtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY29sb3I6ICMyYzNlNTA7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0ubG9nb3V0LWl0ZW06aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmNWY1O1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0ubG9nb3V0LWl0ZW0gc3BhbiB7XG4gIGNvbG9yOiAjZTc0YzNjO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0ubmV3LXJlcXVlc3QtaXRlbSB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlOGY1ZTgsICNmMGY4ZjApO1xuICBib3JkZXItdG9wOiAycHggc29saWQgIzI3YWU2MDtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWRyb3Bkb3duIC5kcm9wZG93bi1pdGVtLm5ldy1yZXF1ZXN0LWl0ZW06aG92ZXIge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZDRmNGQ0LCAjZThmNWU4KTtcbn1cbi5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWRyb3Bkb3duIC5kcm9wZG93bi1pdGVtLm5ldy1yZXF1ZXN0LWl0ZW0gc3BhbiB7XG4gIGNvbG9yOiAjMjdhZTYwO1xuICBmb250LXdlaWdodDogNjAwO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHdpZHRoOiAxMDAlO1xufVxuLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItZHJvcGRvd24gLmRyb3Bkb3duLWRpdmlkZXIge1xuICBoZWlnaHQ6IDFweDtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBtYXJnaW46IDA7XG59XG5Aa2V5ZnJhbWVzIGRyb3Bkb3duRmFkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpO1xuICB9XG4gIHRvIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgfVxufVxuLmhvbWUtaGVhZGVyIC5oZXJvLXNlY3Rpb24ge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGhlaWdodDogY2FsYygxMDB2aCAtIDgwcHgpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgbWFyZ2luLXRvcDogMjBweDtcbiAgYm94LXNoYWRvdzogMCAyMHB4IDYwcHggcmdiYSgwLCAwLCAwLCAwLjE1KSwgMCA4cHggMjVweCByZ2JhKDAsIDAsIDAsIDAuMSksIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWJhY2tncm91bmQge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgei1pbmRleDogMTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWJhY2tncm91bmQgLmhlcm8tYmctaW1hZ2Uge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgb2JqZWN0LXBvc2l0aW9uOiBjZW50ZXI7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XG59XG4uaG9tZS1oZWFkZXIgLmhlcm8tc2VjdGlvbiAuaGVyby1iYWNrZ3JvdW5kOmhvdmVyIC5oZXJvLWJnLWltYWdlIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWJhY2tncm91bmQgLmhlcm8tb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDM4LCA4MywgMTQ3LCAwLjUxNSkgMCUsIHJnYmEoNTIsIDczLCA5NCwgMC41KSAzMCUsIHJnYmEoMzksIDE3NCwgOTUsIDAuNTE4KSA3MCUsIHJnYmEoNDYsIDIwNCwgMTEzLCAwLjgpIDEwMCUpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMnB4KTtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHotaW5kZXg6IDU7XG4gIHdpZHRoOiAxMDAlO1xufVxuLmhvbWUtaGVhZGVyIC5oZXJvLXNlY3Rpb24gLmhlcm8tY29udGVudCAuaGVyby10ZXh0LWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgd2lkdGg6IDEwMCU7XG4gIHBhZGRpbmc6IDAgNHJlbTtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtIHtcbiAgYW5pbWF0aW9uOiBmYWRlSW5VcCAxcyBlYXNlLW91dDtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtOm50aC1jaGlsZCgxKSB7XG4gIHRleHQtYWxpZ246IHJpZ2h0O1xuICBhbmltYXRpb24tZGVsYXk6IDAuMnM7XG59XG4uaG9tZS1oZWFkZXIgLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXRleHQtY29udGFpbmVyIC5oZXJvLXRleHQtaXRlbTpudGgtY2hpbGQoMikge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGFuaW1hdGlvbi1kZWxheTogMC40cztcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtOm50aC1jaGlsZCgzKSB7XG4gIHRleHQtYWxpZ246IGxlZnQ7XG4gIGFuaW1hdGlvbi1kZWxheTogMC42cztcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtIC5oZXJvLXRleHQge1xuICBmb250LXNpemU6IDNyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgdGV4dC1zaGFkb3c6IDAgMCAyMHB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSwgMnB4IDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjcpLCA0cHggNHB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjQpO1xuICBtYXJnaW46IDA7XG4gIHBhZGRpbmc6IDEuNXJlbSAyLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSkgMTAwJSk7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxNXB4KTtcbiAgYm9yZGVyOiAycHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4yKSwgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMSksIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4xNzUsIDAuODg1LCAwLjMyLCAxLjI3NSk7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cbi5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtIC5oZXJvLXRleHQ6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAtMTAwJTtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCB0cmFuc3BhcmVudCwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpLCB0cmFuc3BhcmVudCk7XG4gIHRyYW5zaXRpb246IGxlZnQgMC42cyBlYXNlO1xufVxuLmhvbWUtaGVhZGVyIC5oZXJvLXNlY3Rpb24gLmhlcm8tY29udGVudCAuaGVyby10ZXh0LWNvbnRhaW5lciAuaGVyby10ZXh0LWl0ZW0gLmhlcm8tdGV4dDpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtOHB4KSBzY2FsZSgxLjA4KTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjI1KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIDEwMCUpO1xuICBib3gtc2hhZG93OiAwIDIwcHggNjBweCByZ2JhKDAsIDAsIDAsIDAuMyksIDAgMTBweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4yKSwgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNCk7XG4gIHRleHQtc2hhZG93OiAwIDAgMzBweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCksIDJweCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuOCksIDRweCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuNSk7XG59XG4uaG9tZS1oZWFkZXIgLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXRleHQtY29udGFpbmVyIC5oZXJvLXRleHQtaXRlbSAuaGVyby10ZXh0OmhvdmVyOjpiZWZvcmUge1xuICBsZWZ0OiAxMDAlO1xufVxuXG5Aa2V5ZnJhbWVzIGZhZGVJblVwIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMzBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5AbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIHtcbiAgICBtYXJnaW4tbGVmdDogMjBweDtcbiAgICBtYXJnaW4tcmlnaHQ6IDIwcHg7XG4gICAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXRleHQtY29udGFpbmVyIHtcbiAgICBwYWRkaW5nOiAwIDJyZW07XG4gIH1cbiAgLmhvbWUtaGVhZGVyIC5oZXJvLXNlY3Rpb24gLmhlcm8tY29udGVudCAuaGVyby10ZXh0LWNvbnRhaW5lciAuaGVyby10ZXh0LWl0ZW0gLmhlcm8tdGV4dCB7XG4gICAgZm9udC1zaXplOiAyLjVyZW07XG4gICAgcGFkZGluZzogMS4ycmVtIDJyZW07XG4gIH1cbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAuY29udGFpbmVyLWZsdWlkIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMXJlbTtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAubmF2LWxpc3Qge1xuICAgIGdhcDogMXJlbTtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAubmF2LWxpc3QgLm5hdi1pdGVtIC5uYXYtbGluayB7XG4gICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgcGFkZGluZzogMC40cmVtIDAuOHJlbTtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1saW5rIHtcbiAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1wcm9maWxlIHtcbiAgICBwYWRkaW5nOiAwLjRyZW0gMC44cmVtO1xuICB9XG4gIC5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLXByb2ZpbGUgLnVzZXItYXZhdGFyIHtcbiAgICB3aWR0aDogMzBweDtcbiAgICBoZWlnaHQ6IDMwcHg7XG4gIH1cbiAgLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItcHJvZmlsZSAudXNlci1uYW1lIHtcbiAgICBmb250LXNpemU6IDAuODVyZW07XG4gIH1cbiAgLmhvbWUtaGVhZGVyIC5uYXZiYXIgLnVzZXItZHJvcGRvd24ge1xuICAgIG1pbi13aWR0aDogMjAwcHg7XG4gICAgcmlnaHQ6IC0xNXB4O1xuICB9XG4gIC5ob21lLWhlYWRlciAubmF2YmFyIC51c2VyLWRyb3Bkb3duIC5kcm9wZG93bi1pdGVtIHtcbiAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSBzcGFuIHtcbiAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSBpLCAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSAubWVudS1pY29uIHtcbiAgICB3aWR0aDogMTZweDtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAudXNlci1kcm9wZG93biAuZHJvcGRvd24taXRlbSAubWVudS1pY29uIHN2ZyB7XG4gICAgd2lkdGg6IDE0cHg7XG4gICAgaGVpZ2h0OiAxNHB4O1xuICB9XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIHtcbiAgICBtYXJnaW4tbGVmdDogMTVweDtcbiAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7XG4gICAgbWFyZ2luLXRvcDogMTVweDtcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICB9XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAxLjVyZW07XG4gICAgcGFkZGluZzogMCAxcmVtO1xuICB9XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtIHtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXRleHQtY29udGFpbmVyIC5oZXJvLXRleHQtaXRlbSAuaGVyby10ZXh0IHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gICAgcGFkZGluZzogMXJlbSAxLjVyZW07XG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4yKSwgMCAzcHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIH1cbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAuaG9tZS1oZWFkZXIgLm5hdmJhciAubmF2YmFyLWJyYW5kIC5sb2dvLWltZyB7XG4gICAgaGVpZ2h0OiA0MHB4O1xuICB9XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIHtcbiAgICBtYXJnaW4tbGVmdDogMTBweDtcbiAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7XG4gICAgbWFyZ2luLXRvcDogMTBweDtcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICB9XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIge1xuICAgIHBhZGRpbmc6IDAgMC41cmVtO1xuICB9XG4gIC5ob21lLWhlYWRlciAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGV4dC1jb250YWluZXIgLmhlcm8tdGV4dC1pdGVtIC5oZXJvLXRleHQge1xuICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgIHBhZGRpbmc6IDAuOHJlbSAxLjJyZW07XG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4yKSwgMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgfVxufVxuLm5hdmJhci10b2dnbGVyIHtcbiAgYm9yZGVyOiBub25lO1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgcGFkZGluZzogOHB4IDEycHg7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cbi5uYXZiYXItdG9nZ2xlcjpob3ZlciB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMTIzLCAyNTUsIDAuMSk7XG59XG4ubmF2YmFyLXRvZ2dsZXI6Zm9jdXMge1xuICBib3gtc2hhZG93OiBub25lO1xuICBvdXRsaW5lOiBub25lO1xufVxuLm5hdmJhci10b2dnbGVyIC5uYXZiYXItdG9nZ2xlci1pY29uIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG59XG4ubmF2YmFyLXRvZ2dsZXIgLm5hdmJhci10b2dnbGVyLWljb24gaSB7XG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuICBjb2xvcjogIzMzMztcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuLm1vYmlsZS1uYXYtZHJvcGRvd24ge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogY2FsYygxMDAlIC0gMS44cmVtKSAhaW1wb3J0YW50O1xuICBsZWZ0OiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcbiAgbWFyZ2luLWxlZnQ6IC0yNXB4ICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiAyMDBweDtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHotaW5kZXg6IDk5O1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBhbmltYXRpb246IGRyb3Bkb3duRmFkZUluIDAuM3MgZWFzZTtcbiAgbWFyZ2luLXRvcDogMDtcbn1cbi5tb2JpbGUtbmF2LWRyb3Bkb3duIC5kcm9wZG93bi1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogOHB4IDEycHg7XG4gIGNvbG9yOiAjMzMzO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgMC4wNSk7XG59XG4ubW9iaWxlLW5hdi1kcm9wZG93biAuZHJvcGRvd24taXRlbTpsYXN0LWNoaWxkIHtcbiAgYm9yZGVyLWJvdHRvbTogbm9uZTtcbn1cbi5tb2JpbGUtbmF2LWRyb3Bkb3duIC5kcm9wZG93bi1pdGVtOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAxMjMsIDI1NSwgMC4wNSk7XG4gIGNvbG9yOiAjMDA3YmZmO1xufVxuLm1vYmlsZS1uYXYtZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0gLm1lbnUtaWNvbiB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICB3aWR0aDogMThweDtcbiAgaGVpZ2h0OiAxOHB4O1xufVxuLm1vYmlsZS1uYXYtZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0gLm1lbnUtaWNvbiBpIHtcbiAgZm9udC1zaXplOiAwLjhyZW07XG59XG4ubW9iaWxlLW5hdi1kcm9wZG93biAuZHJvcGRvd24taXRlbSBzcGFuIHtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xuICAubW9iaWxlLW5hdi1kcm9wZG93biB7XG4gICAgd2lkdGg6IDE4MHB4ICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm1vYmlsZS1uYXYtZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0ge1xuICAgIHBhZGRpbmc6IDZweCAxMHB4ICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm1vYmlsZS1uYXYtZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0gLm1lbnUtaWNvbiB7XG4gICAgd2lkdGg6IDE2cHggIWltcG9ydGFudDtcbiAgICBoZWlnaHQ6IDE2cHggIWltcG9ydGFudDtcbiAgfVxuICAubW9iaWxlLW5hdi1kcm9wZG93biAuZHJvcGRvd24taXRlbSAubWVudS1pY29uIGkge1xuICAgIGZvbnQtc2l6ZTogMC43cmVtICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm1vYmlsZS1uYXYtZHJvcGRvd24gLmRyb3Bkb3duLWl0ZW0gc3BhbiB7XG4gICAgZm9udC1zaXplOiAwLjc1cmVtICFpbXBvcnRhbnQ7XG4gIH1cbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA5OTEuOThweCkge1xuICAubmF2YmFyIC5jb250YWluZXItZmx1aWQge1xuICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW4gIWltcG9ydGFudDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgZmxleC13cmFwOiBub3dyYXAgIWltcG9ydGFudDtcbiAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50O1xuICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtICFpbXBvcnRhbnQ7XG4gICAgbWluLWhlaWdodDogYXV0byAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1icmFuZCB7XG4gICAgZmxleDogMCAwIGF1dG8gIWltcG9ydGFudDtcbiAgICBtYXJnaW46IDAgIWltcG9ydGFudDtcbiAgfVxuICAubmF2YmFyIC5uYXZiYXItYnJhbmQgaW1nIHtcbiAgICBoZWlnaHQ6IDM1cHggIWltcG9ydGFudDtcbiAgICB3aWR0aDogYXV0byAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci10b2dnbGVyIHtcbiAgICBmbGV4OiAwIDAgYXV0byAhaW1wb3J0YW50O1xuICAgIG1hcmdpbjogMCAhaW1wb3J0YW50O1xuICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtICFpbXBvcnRhbnQ7XG4gICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcbiAgICBvcmRlcjogMCAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYge1xuICAgIGZsZXg6IDAgMCBhdXRvICFpbXBvcnRhbnQ7XG4gICAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLW5hdi5wb3NpdGlvbi1yZWxhdGl2ZSB7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLW5hdiAudXNlci1wcm9maWxlIHtcbiAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbSAhaW1wb3J0YW50O1xuICAgIGZvbnQtc2l6ZTogMC44NXJlbSAhaW1wb3J0YW50O1xuICAgIHdoaXRlLXNwYWNlOiBub3dyYXAgIWltcG9ydGFudDtcbiAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYgLnVzZXItcHJvZmlsZSAudXNlci1hdmF0YXIge1xuICAgIHdpZHRoOiAyOHB4ICFpbXBvcnRhbnQ7XG4gICAgaGVpZ2h0OiAyOHB4ICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLW5hdiAudXNlci1wcm9maWxlIC51c2VyLW5hbWUge1xuICAgIGRpc3BsYXk6IGlubGluZSAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYgLnVzZXItcHJvZmlsZSAuZmFzLmZhLWNoZXZyb24tZG93biB7XG4gICAgZm9udC1zaXplOiAwLjdyZW0gIWltcG9ydGFudDtcbiAgICBtYXJnaW4tbGVmdDogMC4yNXJlbSAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYgLnVzZXItbGluayB7XG4gICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW0gIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuODVyZW0gIWltcG9ydGFudDtcbiAgICB3aGl0ZS1zcGFjZTogbm93cmFwICFpbXBvcnRhbnQ7XG4gICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDtcbiAgfVxuICAubmF2YmFyIC5uYXZiYXItbmF2IC51c2VyLWxpbmsgaSB7XG4gICAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLW5hdi5teC1hdXRvIHtcbiAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gIH1cbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xuICAubmF2YmFyIC5jb250YWluZXItZmx1aWQge1xuICAgIHBhZGRpbmc6IDAuNHJlbSAwLjc1cmVtICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLWJyYW5kIGltZyB7XG4gICAgaGVpZ2h0OiAzMHB4ICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLXRvZ2dsZXIge1xuICAgIHBhZGRpbmc6IDAuMnJlbSAwLjRyZW0gIWltcG9ydGFudDtcbiAgfVxuICAubmF2YmFyIC5uYXZiYXItdG9nZ2xlciBpIHtcbiAgICBmb250LXNpemU6IDFyZW0gIWltcG9ydGFudDtcbiAgfVxuICAubmF2YmFyIC5uYXZiYXItbmF2IC51c2VyLXByb2ZpbGUge1xuICAgIHBhZGRpbmc6IDAuMnJlbSAwLjRyZW0gIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDAuOHJlbSAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYgLnVzZXItcHJvZmlsZSAudXNlci1hdmF0YXIge1xuICAgIHdpZHRoOiAyNHB4ICFpbXBvcnRhbnQ7XG4gICAgaGVpZ2h0OiAyNHB4ICFpbXBvcnRhbnQ7XG4gIH1cbiAgLm5hdmJhciAubmF2YmFyLW5hdiAudXNlci1saW5rIHtcbiAgICBwYWRkaW5nOiAwLjJyZW0gMC40cmVtICFpbXBvcnRhbnQ7XG4gICAgZm9udC1zaXplOiAwLjhyZW0gIWltcG9ydGFudDtcbiAgfVxuICAubmF2YmFyIC5uYXZiYXItbmF2IC51c2VyLWxpbmsgc3BhbiB7XG4gICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xuICB9XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYgLnVzZXItbGluayBpIHtcbiAgICBtYXJnaW4tcmlnaHQ6IDAgIWltcG9ydGFudDtcbiAgICBmb250LXNpemU6IDFyZW0gIWltcG9ydGFudDtcbiAgfVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDk5MS45OHB4KSB7XG4gIC5ob21lLWhlYWRlciAubmF2YmFyIC5jb250YWluZXItZmx1aWQge1xuICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93ICFpbXBvcnRhbnQ7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuICFpbXBvcnRhbnQ7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgIGZsZXgtd3JhcDogbm93cmFwICFpbXBvcnRhbnQ7XG4gICAgb3ZlcmZsb3c6IHZpc2libGUgIWltcG9ydGFudDtcbiAgfVxuICAuaG9tZS1oZWFkZXIgLm5hdmJhciA+IC5jb250YWluZXItZmx1aWQgPiAqIHtcbiAgICBmbGV4LXNocmluazogMCAhaW1wb3J0YW50O1xuICAgIHdoaXRlLXNwYWNlOiBub3dyYXAgIWltcG9ydGFudDtcbiAgfVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDMxOXB4KSB7XG4gIC5uYXZiYXIgLm5hdmJhci1uYXYgLnVzZXItcHJvZmlsZSAudXNlci1uYW1lIHtcbiAgICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XG4gIH1cbn1cbi5wcm9wZXJ0aWVzLXNlY3Rpb24ge1xuICBwYWRkaW5nOiA1MHB4IDA7XG59XG4ucHJvcGVydGllcy1zZWN0aW9uIC5zZWN0aW9uLXRpdGxlIHtcbiAgZm9udC1zaXplOiAyLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGNvbG9yOiAjMmMzZTUwO1xuICBtYXJnaW4tYm90dG9tOiA0cmVtO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG59XG4ucHJvcGVydGllcy1zZWN0aW9uIC5zZWN0aW9uLXRpdGxlOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYm90dG9tOiAtMTVweDtcbiAgbGVmdDogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XG4gIHdpZHRoOiA4MHB4O1xuICBoZWlnaHQ6IDRweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjMzQ5OGRiLCAjMjk4MGI5KTtcbiAgYm9yZGVyLXJhZGl1czogMnB4O1xufVxuXG4ucHJvcGVydHktY2FyZCB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGhlaWdodDogMTAwJTtcbiAgbWFyZ2luLXRvcDogMzBweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xufVxuLnByb3BlcnR5LWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpO1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWltYWdlIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBoZWlnaHQ6IDIwMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWltYWdlIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWltYWdlOmhvdmVyIGltZyB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbn1cbi5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1pbWFnZSAucHJvcGVydHktYmFkZ2Uge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMTVweDtcbiAgbGVmdDogMTVweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI3YWU2MCwgIzJlY2M3MSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcGFkZGluZzogNXB4IDEycHg7XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICBsZXR0ZXItc3BhY2luZzogMC41cHg7XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktaW1hZ2UgLnByb3BlcnR5LWJhZGdlLnByb3BlcnR5LWJhZGdlLXJlbnQge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZTc0YzNjLCAjYzAzOTJiKTtcbn1cbi5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1pbWFnZSAucHJvcGVydHktbG9jYXRpb24ge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogMTVweDtcbiAgbGVmdDogMTVweDtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDVweCAxMHB4O1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA1cHg7XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktaW1hZ2UgLnByb3BlcnR5LWxvY2F0aW9uIGkge1xuICBmb250LXNpemU6IDAuN3JlbTtcbn1cbi5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IHtcbiAgcGFkZGluZzogMjBweDtcbn1cbi5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4zcmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzJjM2U1MDtcbiAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICBsaW5lLWhlaWdodDogMS4zO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LWRlc2NyaXB0aW9uIHtcbiAgY29sb3I6ICM3ZjhjOGQ7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICBsaW5lLWhlaWdodDogMS41O1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXByaWNlIHtcbiAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cbi5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS1wcmljZSAucHJpY2Uge1xuICBmb250LXNpemU6IDEuNHJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6ICMzNDk4ZGI7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNDk4ZGIsICMyOTgwYjkpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcmF0aW5nIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBtYXJnaW4tYm90dG9tOiAwO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXJhdGluZyAuc3RhcnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDJweDtcbn1cbi5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS1yYXRpbmcgLnN0YXJzIGkge1xuICBjb2xvcjogI2YzOWMxMjtcbiAgZm9udC1zaXplOiAwLjlyZW07XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcmF0aW5nIC5yYXRpbmctdGV4dCB7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMmMzZTUwO1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgbWFyZ2luLXJpZ2h0OiAxNXB4O1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LWFjdGlvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDEwcHg7XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktYWN0aW9ucyAuYnRuIHtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBwYWRkaW5nOiA4cHggMTJweDtcbiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGNvbG9yOiAjN2Y4YzhkO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LWFjdGlvbnMgLmJ0bjpob3ZlciB7XG4gIGJvcmRlci1jb2xvcjogIzM0OThkYjtcbiAgY29sb3I6ICMzNDk4ZGI7XG4gIGJhY2tncm91bmQ6IHJnYmEoNTIsIDE1MiwgMjE5LCAwLjEpO1xufVxuLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LWFjdGlvbnMgLmJ0biBpIHtcbiAgZm9udC1zaXplOiAwLjlyZW07XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcmF0aW5nLWFjdGlvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIG1hcmdpbi1ib3R0b206IDA7XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcmF0aW5nLWFjdGlvbnMgLnByb3BlcnR5LXJhdGluZyB7XG4gIG1hcmdpbi1ib3R0b206IDA7XG4gIGZsZXg6IDE7XG59XG4ucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcmF0aW5nLWFjdGlvbnMgLnByb3BlcnR5LWFjdGlvbnMge1xuICBmbGV4OiAwIDAgYXV0bztcbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5wcm9wZXJ0aWVzLXNlY3Rpb24ge1xuICAgIHBhZGRpbmc6IDYwcHggMDtcbiAgfVxuICAucHJvcGVydGllcy1zZWN0aW9uIC5zZWN0aW9uLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgfVxuICAucHJvcGVydHktY2FyZCB7XG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgfVxuICAucHJvcGVydHktY2FyZCAucHJvcGVydHktaW1hZ2Uge1xuICAgIGhlaWdodDogMTgwcHg7XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDE1cHg7XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXRpdGxlIHtcbiAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgfVxuICAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcHJpY2UgLnByaWNlIHtcbiAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgfVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDU3NnB4KSB7XG4gIC5wcm9wZXJ0aWVzLXNlY3Rpb24ge1xuICAgIHBhZGRpbmc6IDQwcHggMDtcbiAgfVxuICAucHJvcGVydGllcy1zZWN0aW9uIC5zZWN0aW9uLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDEuOHJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWltYWdlIHtcbiAgICBoZWlnaHQ6IDE2MHB4O1xuICB9XG4gIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1pbWFnZSAucHJvcGVydHktYmFkZ2Uge1xuICAgIGZvbnQtc2l6ZTogMC43cmVtO1xuICAgIHBhZGRpbmc6IDRweCA4cHg7XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWltYWdlIC5wcm9wZXJ0eS1sb2NhdGlvbiB7XG4gICAgZm9udC1zaXplOiAwLjdyZW07XG4gICAgcGFkZGluZzogNHB4IDhweDtcbiAgfVxuICAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCB7XG4gICAgcGFkZGluZzogMTJweDtcbiAgfVxuICAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICB9XG4gIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS1kZXNjcmlwdGlvbiB7XG4gICAgZm9udC1zaXplOiAwLjhyZW07XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXByaWNlIC5wcmljZSB7XG4gICAgZm9udC1zaXplOiAxLjFyZW07XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXJhdGluZyAuc3RhcnMgaSB7XG4gICAgZm9udC1zaXplOiAwLjhyZW07XG4gIH1cbiAgLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXJhdGluZyAucmF0aW5nLXRleHQge1xuICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICB9XG4gIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS1hY3Rpb25zIC5idG4ge1xuICAgIHBhZGRpbmc6IDZweCAxMHB4O1xuICB9XG4gIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS1hY3Rpb25zIC5idG4gaSB7XG4gICAgZm9udC1zaXplOiAwLjhyZW07XG4gIH1cbn1cbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24ge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjhGOEY4O1xuICBtYXJnaW4tdG9wOiA5MHB4O1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuc2VjdGlvbi10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMi41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzJjM2U1MDtcbiAgbWFyZ2luLWJvdHRvbTogM3JlbTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuc2VjdGlvbi10aXRsZTo6YWZ0ZXIge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogLTE1cHg7XG4gIGxlZnQ6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xuICB3aWR0aDogODBweDtcbiAgaGVpZ2h0OiA0cHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzAwN2JmZiwgIzAwNTZiMyk7XG4gIGJvcmRlci1yYWRpdXM6IDJweDtcbn1cbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciB7XG4gIG1hcmdpbjogMCAzMHB4O1xuICBtYXJnaW4tdG9wOiAzMHB4O1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmxvY2F0aW9uLWNhcmQge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIGhlaWdodDogMjAwcHg7XG59XG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAubG9jYXRpb24tY2FyZDpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcbiAgYm94LXNoYWRvdzogMCAxNXB4IDM1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbn1cbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5sb2NhdGlvbi1jYXJkOmhvdmVyIGltZyB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG59XG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAubG9jYXRpb24tY2FyZDpob3ZlciAubG9jYXRpb24tb3ZlcmxheSB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTtcbn1cbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5sb2NhdGlvbi1jYXJkIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmxvY2F0aW9uLWNhcmQgLmxvY2F0aW9uLW92ZXJsYXkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGJvdHRvbTogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAxNXB4O1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmxvY2F0aW9uLWNhcmQgLmxvY2F0aW9uLW92ZXJsYXkgLmxvY2F0aW9uLWluZm8gaDUge1xuICBmb250LXNpemU6IDEuMXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luLWJvdHRvbTogNXB4O1xuICBjb2xvcjogd2hpdGU7XG4gIHRleHQtc2hhZG93OiAxcHggMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuNSk7XG59XG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSAubG9jYXRpb24taW5mbyBwIHtcbiAgZm9udC1zaXplOiAwLjg1cmVtO1xuICBtYXJnaW46IDA7XG4gIG9wYWNpdHk6IDAuOTtcbiAgdGV4dC1zaGFkb3c6IDFweCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC41KTtcbn1cbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5sb2NhdGlvbi1jYXJkIC5sb2NhdGlvbi1vdmVybGF5IC5sb2NhdGlvbi1pbmZvIHAgaSB7XG4gIG1hcmdpbi1yaWdodDogNXB4O1xuICBjb2xvcjogI2ZmZDcwMDtcbn1cbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTtcbiAgd2lkdGg6IDUwcHggIWltcG9ydGFudDtcbiAgaGVpZ2h0OiA1MHB4ICFpbXBvcnRhbnQ7XG4gIGJhY2tncm91bmQ6ICMwMzE3NTI7XG4gIGJvcmRlcjogbm9uZTtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBvcGFjaXR5OiAxO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMzAsIDU4LCAxMzgsIDAuMyk7XG59XG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXY6aG92ZXIsXG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQ6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjMWU0MGFmO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSkgc2NhbGUoMS4xKTtcbiAgYm94LXNoYWRvdzogMCA2cHggMjBweCByZ2JhKDMwLCA1OCwgMTM4LCAwLjQpO1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IC5jYXJvdXNlbC1jb250cm9sLXByZXYtaWNvbixcbi5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldiAuY2Fyb3VzZWwtY29udHJvbC1uZXh0LWljb24sXG4uaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQgLmNhcm91c2VsLWNvbnRyb2wtcHJldi1pY29uLFxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IC5jYXJvdXNlbC1jb250cm9sLW5leHQtaWNvbiB7XG4gIHdpZHRoOiAyMHB4O1xuICBoZWlnaHQ6IDIwcHg7XG4gIGJhY2tncm91bmQtc2l6ZTogMjBweCAyMHB4O1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcbiAgbGVmdDogMXB4O1xufVxuLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgcmlnaHQ6IDFweDtcbn1cblxuQG1lZGlhIChtaW4td2lkdGg6IDE0MDBweCkge1xuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIge1xuICAgIG1hcmdpbjogMCA1MHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgICB3aWR0aDogNjBweDtcbiAgICBoZWlnaHQ6IDYwcHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIHtcbiAgICBoZWlnaHQ6IDIyMHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSAubG9jYXRpb24taW5mbyBoNSB7XG4gICAgZm9udC1zaXplOiAxLjNyZW07XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIC5sb2NhdGlvbi1vdmVybGF5IC5sb2NhdGlvbi1pbmZvIHAge1xuICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgfVxufVxuQG1lZGlhIChtaW4td2lkdGg6IDk5MnB4KSBhbmQgKG1heC13aWR0aDogMTM5OXB4KSB7XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciB7XG4gICAgbWFyZ2luOiAwIDQwcHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIHtcbiAgICBoZWlnaHQ6IDIwMHB4O1xuICB9XG59XG5AbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIGFuZCAobWF4LXdpZHRoOiA5OTFweCkge1xuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5zZWN0aW9uLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDIuMnJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAyLjVyZW07XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIHtcbiAgICBtYXJnaW46IDAgMzBweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYsXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gICAgd2lkdGg6IDQ1cHg7XG4gICAgaGVpZ2h0OiA0NXB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldiB7XG4gICAgbGVmdDogLTVweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xuICAgIHJpZ2h0OiAtNXB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCB7XG4gICAgaGVpZ2h0OiAxODBweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmxvY2F0aW9uLWNhcmQgLmxvY2F0aW9uLW92ZXJsYXkge1xuICAgIHBhZGRpbmc6IDEycHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIC5sb2NhdGlvbi1vdmVybGF5IC5sb2NhdGlvbi1pbmZvIGg1IHtcbiAgICBmb250LXNpemU6IDFyZW07XG4gICAgY29sb3I6IHdoaXRlO1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSAubG9jYXRpb24taW5mbyBwIHtcbiAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgfVxufVxuQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSBhbmQgKG1heC13aWR0aDogNzY3cHgpIHtcbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuc2VjdGlvbi10aXRsZSB7XG4gICAgZm9udC1zaXplOiAycmVtO1xuICAgIG1hcmdpbi1ib3R0b206IDJyZW07XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIHtcbiAgICBtYXJnaW46IDAgMjBweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYsXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gICAgd2lkdGg6IDQwcHg7XG4gICAgaGVpZ2h0OiA0MHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldiB7XG4gICAgbGVmdDogLTEwcHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgICByaWdodDogLTEwcHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIHtcbiAgICBoZWlnaHQ6IDE2MHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSB7XG4gICAgcGFkZGluZzogMTBweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmxvY2F0aW9uLWNhcmQgLmxvY2F0aW9uLW92ZXJsYXkgLmxvY2F0aW9uLWluZm8gaDUge1xuICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgICBjb2xvcjogd2hpdGU7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIC5sb2NhdGlvbi1vdmVybGF5IC5sb2NhdGlvbi1pbmZvIHAge1xuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgfVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDU3NXB4KSB7XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24ge1xuICAgIHBhZGRpbmc6IDQwcHggMDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5zZWN0aW9uLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDEuOHJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIHtcbiAgICBtYXJnaW46IDAgMTVweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYsXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gICAgd2lkdGg6IDM1cHg7XG4gICAgaGVpZ2h0OiAzNXB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldiB7XG4gICAgbGVmdDogLTE1cHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgICByaWdodDogLTE1cHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5jYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LWljb24sXG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uIHtcbiAgICB3aWR0aDogMTZweDtcbiAgICBoZWlnaHQ6IDE2cHg7XG4gICAgYmFja2dyb3VuZC1zaXplOiAxNnB4IDE2cHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIHtcbiAgICBoZWlnaHQ6IDE0MHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSB7XG4gICAgcGFkZGluZzogOHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSAubG9jYXRpb24taW5mbyBoNSB7XG4gICAgZm9udC1zaXplOiAwLjg1cmVtO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBtYXJnaW4tYm90dG9tOiAzcHg7XG4gIH1cbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIC5sb2NhdGlvbi1jYXJkIC5sb2NhdGlvbi1vdmVybGF5IC5sb2NhdGlvbi1pbmZvIHAge1xuICAgIGZvbnQtc2l6ZTogMC43cmVtO1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSAubG9jYXRpb24taW5mbyBwIGkge1xuICAgIG1hcmdpbi1yaWdodDogM3B4O1xuICB9XG59XG5AbWVkaWEgKG1heC13aWR0aDogNDAwcHgpIHtcbiAgLmhvcml6b250YWwtY2Fyb3VzZWwtc2VjdGlvbiAuY2Fyb3VzZWwtY29udGFpbmVyIHtcbiAgICBtYXJnaW46IDAgMTBweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmNhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYge1xuICAgIGxlZnQ6IC0yMHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAuY2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gICAgcmlnaHQ6IC0yMHB4O1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCB7XG4gICAgaGVpZ2h0OiAxMjBweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmxvY2F0aW9uLWNhcmQgLmxvY2F0aW9uLW92ZXJsYXkge1xuICAgIHBhZGRpbmc6IDZweDtcbiAgfVxuICAuaG9yaXpvbnRhbC1jYXJvdXNlbC1zZWN0aW9uIC5jYXJvdXNlbC1jb250YWluZXIgLmxvY2F0aW9uLWNhcmQgLmxvY2F0aW9uLW92ZXJsYXkgLmxvY2F0aW9uLWluZm8gaDUge1xuICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICB9XG4gIC5ob3Jpem9udGFsLWNhcm91c2VsLXNlY3Rpb24gLmNhcm91c2VsLWNvbnRhaW5lciAubG9jYXRpb24tY2FyZCAubG9jYXRpb24tb3ZlcmxheSAubG9jYXRpb24taW5mbyBwIHtcbiAgICBmb250LXNpemU6IDAuNjVyZW07XG4gIH1cbn1cbi5hcnRpY2xlcy1zZWN0aW9uIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcbiAgcGFkZGluZzogMTAwcHggMDtcbiAgbWFyZ2luLXRvcDogODBweDtcbiAgbWFyZ2luLWJvdHRvbTogNTBweDtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciB7XG4gIG1hcmdpbi1ib3R0b206IDYwcHg7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLmxlZnQtY29udHJvbHMgLmNhcm91c2VsLWNvbnRyb2wtYnRuIHtcbiAgd2lkdGg6IDUwcHg7XG4gIGhlaWdodDogNTBweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBib3JkZXI6IG5vbmU7XG4gIGJhY2tncm91bmQ6ICMxZTNhOGE7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgzMCwgNTgsIDEzOCwgMC4yNSk7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLmxlZnQtY29udHJvbHMgLmNhcm91c2VsLWNvbnRyb2wtYnRuOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogIzFlNDBhZjtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbiAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDMwLCA1OCwgMTM4LCAwLjM1KTtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAubGVmdC1jb250cm9scyAuY2Fyb3VzZWwtY29udHJvbC1idG4gaSB7XG4gIGZvbnQtc2l6ZTogMThweDtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAuYXJ0aWNsZXMtdGl0bGUge1xuICBmb250LXNpemU6IDIuOHJlbTtcbiAgZm9udC13ZWlnaHQ6IDgwMDtcbiAgY29sb3I6ICMxZTNhOGE7XG4gIHRleHQtc2hhZG93OiAycHggMnB4IDRweCByZ2JhKDMwLCA1OCwgMTM4LCAwLjEpO1xuICBtYXJnaW46IDA7XG4gIGxldHRlci1zcGFjaW5nOiAtMC41cHg7XG4gIGRpcmVjdGlvbjogbHRyO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi1sZWZ0OiAyMDBweDtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAucmlnaHQtbGluayAudmlldy1hbGwtbGluayB7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgZm9udC1zaXplOiAxLjRyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBkaXJlY3Rpb246IGx0cjtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAucmlnaHQtbGluayAudmlldy1hbGwtbGluayAudGV4dC1zdWNjZXNzIHtcbiAgY29sb3I6ICMyOGE3NDUgIWltcG9ydGFudDtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAucmlnaHQtbGluayAudmlldy1hbGwtbGluayAuZ3JlZW4tdW5kZXJsaW5lIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBib3R0b206IC01cHg7XG4gIGxlZnQ6IDA7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDNweDtcbiAgYmFja2dyb3VuZDogIzI4YTc0NTtcbiAgYm9yZGVyLXJhZGl1czogMnB4O1xufVxuLmFydGljbGVzLXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIC5yaWdodC1saW5rIC52aWV3LWFsbC1saW5rOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01cHgpO1xufVxuLmFydGljbGVzLXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIC5yaWdodC1saW5rIC52aWV3LWFsbC1saW5rOmhvdmVyIC50ZXh0LXN1Y2Nlc3Mge1xuICBjb2xvcjogIzFlN2UzNCAhaW1wb3J0YW50O1xufVxuLmFydGljbGVzLXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIC5yaWdodC1saW5rIC52aWV3LWFsbC1saW5rOmhvdmVyIC5ncmVlbi11bmRlcmxpbmUge1xuICBiYWNrZ3JvdW5kOiAjMWU3ZTM0O1xufVxuLmFydGljbGVzLXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIC5yaWdodC1saW5rIC52aWV3LWFsbC1saW5rOmhvdmVyIGkge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTNweCk7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLnJpZ2h0LWxpbmsgLnZpZXctYWxsLWxpbmsgaSB7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZCB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYm94LXNoYWRvdzogMCAxMHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuMjUsIDAuNDYsIDAuNDUsIDAuOTQpO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIGhlaWdodDogNDAwcHg7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZDpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtOHB4KTtcbiAgYm94LXNoYWRvdzogMCAyMHB4IDQwcHggcmdiYSgwLCAwLCAwLCAwLjI1KTtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAuYXJ0aWNsZS1jYXJkOmhvdmVyIC5hcnRpY2xlLWltYWdlIGltZyB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wMyk7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZDpob3ZlciAuYXJ0aWNsZS1vdmVybGF5IHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRyYW5zcGFyZW50LCByZ2JhKDAsIDAsIDAsIDAuOSkpO1xufVxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2Uge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGhlaWdodDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWltYWdlIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC42cyBjdWJpYy1iZXppZXIoMC4yNSwgMC40NiwgMC40NSwgMC45NCk7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1pbWFnZSAuYXJ0aWNsZS1vdmVybGF5IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBib3R0b206IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodHJhbnNwYXJlbnQsIHJnYmEoMCwgMCwgMCwgMC44NSkpO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDQwcHggMjVweCAyNXB4O1xuICB0cmFuc2l0aW9uOiBhbGwgMC40cyBjdWJpYy1iZXppZXIoMC4yNSwgMC40NiwgMC40NSwgMC45NCk7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1pbWFnZSAuYXJ0aWNsZS1vdmVybGF5IC5hcnRpY2xlLWNvbnRlbnQge1xuICBkaXJlY3Rpb246IGx0cjtcbiAgdGV4dC1hbGlnbjogbGVmdDtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWltYWdlIC5hcnRpY2xlLW92ZXJsYXkgLmFydGljbGUtY29udGVudCBoNCB7XG4gIGZvbnQtc2l6ZTogMS40cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBtYXJnaW4tYm90dG9tOiAxMnB4O1xuICBjb2xvcjogd2hpdGU7XG4gIHRleHQtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjMpO1xuICBsaW5lLWhlaWdodDogMS4zO1xufVxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtb3ZlcmxheSAuYXJ0aWNsZS1jb250ZW50IHAge1xuICBmb250LXNpemU6IDFyZW07XG4gIG1hcmdpbjogMDtcbiAgb3BhY2l0eTogMC45NTtcbiAgdGV4dC1zaGFkb3c6IDAgMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gIGxpbmUtaGVpZ2h0OiAxLjY7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTtcbiAgd2lkdGg6IDUwcHg7XG4gIGhlaWdodDogNTBweDtcbiAgYmFja2dyb3VuZDogIzFlM2E4YTtcbiAgYm9yZGVyOiBub25lO1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIG9wYWNpdHk6IDE7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSgzMCwgNTgsIDEzOCwgMC4zKTtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXY6aG92ZXIsXG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0OmhvdmVyIHtcbiAgYmFja2dyb3VuZDogIzFlNDBhZjtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpIHNjYWxlKDEuMSk7XG4gIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgzMCwgNTgsIDEzOCwgMC40KTtcbn1cbi5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYgLmNhcm91c2VsLWNvbnRyb2wtcHJldi1pY29uLFxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldiAuY2Fyb3VzZWwtY29udHJvbC1uZXh0LWljb24sXG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IC5jYXJvdXNlbC1jb250cm9sLXByZXYtaWNvbixcbi5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQgLmNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uIHtcbiAgd2lkdGg6IDIwcHg7XG4gIGhlaWdodDogMjBweDtcbiAgYmFja2dyb3VuZC1zaXplOiAyMHB4IDIwcHg7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcbiAgbGVmdDogLTI1cHg7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgcmlnaHQ6IC0yNXB4O1xufVxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWluZGljYXRvcnMge1xuICBib3R0b206IC01MHB4O1xuICBtYXJnaW4tYm90dG9tOiAwO1xufVxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWluZGljYXRvcnMgYnV0dG9uIHtcbiAgd2lkdGg6IDEycHg7XG4gIGhlaWdodDogMTJweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBib3JkZXI6IG5vbmU7XG4gIGJhY2tncm91bmQ6IHJnYmEoMzAsIDU4LCAxMzgsIDAuMyk7XG4gIG1hcmdpbjogMCA2cHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG4uYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5kaWNhdG9ycyBidXR0b24uYWN0aXZlIHtcbiAgYmFja2dyb3VuZDogIzFlM2E4YTtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpO1xufVxuLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWluZGljYXRvcnMgYnV0dG9uOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogIzFlNDBhZjtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYge1xuICAgIGxlZnQ6IC0xNXB4O1xuICB9XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xuICAgIHJpZ2h0OiAtMTVweDtcbiAgfVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDk5MnB4KSB7XG4gIC5hcnRpY2xlcy1zZWN0aW9uIC5zZWN0aW9uLWhlYWRlciAuYXJ0aWNsZXMtdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLnZpZXctYWxsLWxpbmsgYSB7XG4gICAgZm9udC1zaXplOiAxLjJyZW07XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQge1xuICAgIGhlaWdodDogMjgwcHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtb3ZlcmxheSB7XG4gICAgcGFkZGluZzogMjVweDtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1pbWFnZSAuYXJ0aWNsZS1vdmVybGF5IC5hcnRpY2xlLWNvbnRlbnQgaDQge1xuICAgIGZvbnQtc2l6ZTogMS4ycmVtO1xuICB9XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1pbm5lciAuY2Fyb3VzZWwtaXRlbSAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWltYWdlIC5hcnRpY2xlLW92ZXJsYXkgLmFydGljbGUtY29udGVudCBwIHtcbiAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgICB3aWR0aDogNDVweDtcbiAgICBoZWlnaHQ6IDQ1cHg7XG4gIH1cbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQgIWltcG9ydGFudDtcbiAgICBnYXA6IDE1cHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gLnNlY3Rpb24taGVhZGVyIC5hcnRpY2xlcy10aXRsZSB7XG4gICAgZm9udC1zaXplOiAxLjhyZW07XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQge1xuICAgIGhlaWdodDogMjUwcHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtb3ZlcmxheSB7XG4gICAgcGFkZGluZzogMjBweDtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1pbWFnZSAuYXJ0aWNsZS1vdmVybGF5IC5hcnRpY2xlLWNvbnRlbnQgaDQge1xuICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtb3ZlcmxheSAuYXJ0aWNsZS1jb250ZW50IHAge1xuICAgIGZvbnQtc2l6ZTogMC44NXJlbTtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LFxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IHtcbiAgICB3aWR0aDogNDBweDtcbiAgICBoZWlnaHQ6IDQwcHg7XG4gICAgdG9wOiA0NSU7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldiAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LWljb24sXG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYgLmNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uLFxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0IC5jYXJvdXNlbC1jb250cm9sLXByZXYtaWNvbixcbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCAuY2Fyb3VzZWwtY29udHJvbC1uZXh0LWljb24ge1xuICAgIHdpZHRoOiAxNnB4O1xuICAgIGhlaWdodDogMTZweDtcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDE2cHggMTZweDtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IHtcbiAgICBsZWZ0OiAxMHB4O1xuICB9XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQge1xuICAgIHJpZ2h0OiAxMHB4O1xuICB9XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1pbmRpY2F0b3JzIHtcbiAgICBib3R0b206IC00MHB4O1xuICB9XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1pbmRpY2F0b3JzIGJ1dHRvbiB7XG4gICAgd2lkdGg6IDEwcHg7XG4gICAgaGVpZ2h0OiAxMHB4O1xuICAgIG1hcmdpbjogMCA0cHg7XG4gIH1cbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xuICAuYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLmFydGljbGVzLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDEuNnJlbTtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAuc2VjdGlvbi1oZWFkZXIgLnZpZXctYWxsLWxpbmsgYSB7XG4gICAgZm9udC1zaXplOiAxLjFyZW07XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQge1xuICAgIGhlaWdodDogMjIwcHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtb3ZlcmxheSB7XG4gICAgcGFkZGluZzogMTVweDtcbiAgfVxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtaW5uZXIgLmNhcm91c2VsLWl0ZW0gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1pbWFnZSAuYXJ0aWNsZS1vdmVybGF5IC5hcnRpY2xlLWNvbnRlbnQgaDQge1xuICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWlubmVyIC5jYXJvdXNlbC1pdGVtIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtb3ZlcmxheSAuYXJ0aWNsZS1jb250ZW50IHAge1xuICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtcHJldixcbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCB7XG4gICAgd2lkdGg6IDM1cHg7XG4gICAgaGVpZ2h0OiAzNXB4O1xuICB9XG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLXByZXYgLmNhcm91c2VsLWNvbnRyb2wtcHJldi1pY29uLFxuICAuYXJ0aWNsZXMtc2VjdGlvbiAjYXJ0aWNsZXNDYXJvdXNlbCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2IC5jYXJvdXNlbC1jb250cm9sLW5leHQtaWNvbixcbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWNvbnRyb2wtbmV4dCAuY2Fyb3VzZWwtY29udHJvbC1wcmV2LWljb24sXG4gIC5hcnRpY2xlcy1zZWN0aW9uICNhcnRpY2xlc0Nhcm91c2VsIC5jYXJvdXNlbC1jb250cm9sLW5leHQgLmNhcm91c2VsLWNvbnRyb2wtbmV4dC1pY29uIHtcbiAgICB3aWR0aDogMTRweDtcbiAgICBoZWlnaHQ6IDE0cHg7XG4gICAgYmFja2dyb3VuZC1zaXplOiAxNHB4IDE0cHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWluZGljYXRvcnMge1xuICAgIGJvdHRvbTogLTM1cHg7XG4gIH1cbiAgLmFydGljbGVzLXNlY3Rpb24gI2FydGljbGVzQ2Fyb3VzZWwgLmNhcm91c2VsLWluZGljYXRvcnMgYnV0dG9uIHtcbiAgICB3aWR0aDogOHB4O1xuICAgIGhlaWdodDogOHB4O1xuICAgIG1hcmdpbjogMCAzcHg7XG4gIH1cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "HomeComponent_div_27_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleUserDropdown", "ɵɵadvance", "ɵɵproperty", "getUserProfileImage", "ɵɵsanitizeUrl", "getUserDisplayName", "ɵɵtextInterpolate", "HomeComponent_div_28_Template_div_click_1_listener", "_r3", "closeUserDropdown", "HomeComponent_div_28_Template_div_click_13_listener", "HomeComponent_div_28_Template_div_click_18_listener", "HomeComponent_div_28_Template_div_click_23_listener", "HomeComponent_div_28_Template_div_click_28_listener", "HomeComponent_div_28_Template_div_click_34_listener", "logout", "HomeComponent_div_28_Template_div_click_40_listener", "HomeComponent_div_196_div_2_Template_div_click_1_listener", "location_r5", "_r4", "$implicit", "onLocationClick", "image", "name", "ɵɵtextInterpolate1", "propertyCount", "ɵɵtemplate", "HomeComponent_div_196_div_2_Template", "ɵɵclassProp", "i_r7", "slide_r6", "HomeComponent", "authService", "currentUser", "isLoggedIn", "showUserDropdown", "showMobileMenu", "locationSlides", "currentSlideIndex", "carouselI<PERSON>val", "locations", "id", "constructor", "ngOnInit", "checkUserSession", "initializeLocationSlides", "ngAfterViewInit", "setTimeout", "initializeCarousel", "ngOnDestroy", "clearInterval", "authToken", "localStorage", "getItem", "JSON", "parse", "error", "fullName", "removeItem", "toggleMobileMenu", "closeMobileMenu", "onDocumentClick", "event", "target", "userProfile", "closest", "userDropdown", "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "mobileNavDropdown", "itemsPerSlide", "i", "length", "push", "slice", "carouselElement", "document", "getElementById", "bootstrap", "carousel", "Carousel", "interval", "ride", "wrap", "keyboard", "pause", "console", "log", "startManualCarousel", "setInterval", "nextSlide", "totalSlides", "updateCarouselDisplay", "prevSlide", "carouselItems", "querySelectorAll", "for<PERSON>ach", "item", "index", "classList", "add", "remove", "location", "loadMoreLocations", "ɵɵdirectiveInject", "i1", "AuthenticationService", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "HomeComponent_Template_button_click_6_listener", "HomeComponent_div_25_Template", "HomeComponent_div_27_Template", "HomeComponent_div_28_Template", "HomeComponent_a_29_Template", "HomeComponent_div_196_Template", "HomeComponent_Template_button_click_197_listener", "HomeComponent_Template_button_click_201_listener", "HomeComponent_Template_button_click_207_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener, After<PERSON>iewInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\r\nimport { AuthenticationService } from '../authentication/services/authentication.service';\r\n\r\ndeclare var bootstrap: any;\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  currentUser: any = null;\r\n  isLoggedIn: boolean = false;\r\n  showUserDropdown: boolean = false;\r\n  showMobileMenu: boolean = false;\r\n\r\n  // Location Carousel Data\r\n  locationSlides: any[][] = [];\r\n  currentSlideIndex: number = 0;\r\n  carouselInterval: any;\r\n  locations: any[] = [\r\n    {\r\n      id: 1,\r\n      name: ' New Cairo',\r\n      image: './assets/media/stock/600x400/img-10.jpg',\r\n      propertyCount: 2341\r\n    },\r\n    {\r\n      id: 2,\r\n      name: '  <PERSON><PERSON>',\r\n      image: './assets/media/stock/600x400/img-20.jpg',\r\n      propertyCount: 1234\r\n    },\r\n    {\r\n      id: 3,\r\n      name: ' <PERSON>  ',\r\n      image: './assets/media/stock/600x400/img-30.jpg',\r\n      propertyCount: 3421\r\n    },\r\n    {\r\n      id: 4,\r\n      name: '   Heliopolis',\r\n      image: './assets/media/stock/600x400/img-40.jpg',\r\n      propertyCount: 2341\r\n    },\r\n    {\r\n      id: 5,\r\n      name: '   Nasr City',\r\n      image: './assets/media/stock/600x400/img-50.jpg',\r\n      propertyCount: 987\r\n    },\r\n    {\r\n      id: 6,\r\n      name: '  6 October',\r\n      image: './assets/media/stock/600x400/img-60.jpg',\r\n      propertyCount: 1543\r\n    },\r\n    {\r\n      id: 7,\r\n      name: '  Maadi',\r\n      image: './assets/media/stock/600x400/img-70.jpg',\r\n      propertyCount: 876\r\n    },\r\n    {\r\n      id: 8,\r\n      name: '  Zamalek',\r\n      image: './assets/media/stock/600x400/img-80.jpg',\r\n      propertyCount: 654\r\n    },\r\n    {\r\n      id: 9,\r\n      name: '  New Cairo',\r\n      image: './assets/media/stock/600x400/img-90.jpg',\r\n      propertyCount: 1098\r\n    },\r\n    {\r\n      id: 10,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    },\r\n    {\r\n      id: 11,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    },\r\n    {\r\n      id: 12,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    }\r\n  ];\r\n\r\n  constructor(private authService: AuthenticationService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.checkUserSession();\r\n    this.initializeLocationSlides();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Initialize Bootstrap carousel after view is loaded\r\n    setTimeout(() => {\r\n      this.initializeCarousel();\r\n    }, 100);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up interval when component is destroyed\r\n    if (this.carouselInterval) {\r\n      clearInterval(this.carouselInterval);\r\n    }\r\n  }\r\n\r\n  checkUserSession(): void {\r\n    // Check if user is logged in by checking localStorage\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      try {\r\n        this.currentUser = JSON.parse(currentUser);\r\n        this.isLoggedIn = true;\r\n      } catch (error) {\r\n        // If parsing fails, user is not logged in\r\n        this.isLoggedIn = false;\r\n        this.currentUser = null;\r\n      }\r\n    } else {\r\n      this.isLoggedIn = false;\r\n      this.currentUser = null;\r\n    }\r\n  }\r\n\r\n  getUserDisplayName(): string {\r\n    if (this.currentUser) {\r\n      return this.currentUser.fullName  || 'User';\r\n    }\r\n    return 'Guest';\r\n  }\r\n\r\n  getUserProfileImage(): string {\r\n    if (this.currentUser && this.currentUser.image) {\r\n      return this.currentUser.image;\r\n    }\r\n    // Return default avatar if no profile image\r\n    return 'assets/media/avatars/blank.png';\r\n  }\r\n\r\n  toggleUserDropdown(): void {\r\n    this.showUserDropdown = !this.showUserDropdown;\r\n  }\r\n\r\n  closeUserDropdown(): void {\r\n    this.showUserDropdown = false;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    localStorage.removeItem('currentUser');\r\n    this.isLoggedIn = false;\r\n    this.currentUser = null;\r\n    this.showUserDropdown = false;\r\n    // Optionally redirect to login page\r\n    // this.router.navigate(['/authentication/login']);\r\n  }\r\n\r\n  toggleMobileMenu(): void {\r\n    this.showMobileMenu = !this.showMobileMenu;\r\n    // Close user dropdown when mobile menu is toggled\r\n    if (this.showMobileMenu) {\r\n      this.showUserDropdown = false;\r\n    }\r\n  }\r\n\r\n  closeMobileMenu(): void {\r\n    this.showMobileMenu = false;\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const userProfile = target.closest('.user-profile');\r\n    const userDropdown = target.closest('.user-dropdown');\r\n    const navbarToggler = target.closest('.navbar-toggler');\r\n    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');\r\n\r\n    // Close user dropdown if clicked outside of user profile and dropdown\r\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\r\n      this.showUserDropdown = false;\r\n    }\r\n\r\n    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown\r\n    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {\r\n      this.showMobileMenu = false;\r\n    }\r\n  }\r\n\r\n  // Location Carousel Methods\r\n  initializeLocationSlides(): void {\r\n    // Split locations into slides of 5 items each\r\n    const itemsPerSlide = 5;\r\n    this.locationSlides = [];\r\n\r\n    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {\r\n      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));\r\n    }\r\n  }\r\n\r\n  initializeCarousel(): void {\r\n    try {\r\n      const carouselElement = document.getElementById('horizontalCarousel');\r\n      if (carouselElement) {\r\n        // Try Bootstrap first\r\n        if (typeof bootstrap !== 'undefined') {\r\n          const carousel = new bootstrap.Carousel(carouselElement, {\r\n            interval: 5000,\r\n            ride: 'carousel',\r\n            wrap: true,\r\n            keyboard: true,\r\n            pause: 'hover'\r\n          });\r\n          console.log('Bootstrap carousel initialized');\r\n        } else {\r\n          // Fallback: Manual carousel control\r\n          this.startManualCarousel();\r\n          console.log('Manual carousel initialized');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error initializing carousel:', error);\r\n      // Fallback to manual carousel\r\n      this.startManualCarousel();\r\n    }\r\n  }\r\n\r\n  startManualCarousel(): void {\r\n    // Clear any existing interval\r\n    if (this.carouselInterval) {\r\n      clearInterval(this.carouselInterval);\r\n    }\r\n\r\n    // Start auto-play\r\n    this.carouselInterval = setInterval(() => {\r\n      this.nextSlide();\r\n    }, 5000);\r\n  }\r\n\r\n  nextSlide(): void {\r\n    const totalSlides = this.locationSlides.length;\r\n    if (totalSlides > 0) {\r\n      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;\r\n      this.updateCarouselDisplay();\r\n    }\r\n  }\r\n\r\n  prevSlide(): void {\r\n    const totalSlides = this.locationSlides.length;\r\n    if (totalSlides > 0) {\r\n      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;\r\n      this.updateCarouselDisplay();\r\n    }\r\n  }\r\n\r\n  updateCarouselDisplay(): void {\r\n    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');\r\n    carouselItems.forEach((item, index) => {\r\n      if (index === this.currentSlideIndex) {\r\n        item.classList.add('active');\r\n      } else {\r\n        item.classList.remove('active');\r\n      }\r\n    });\r\n  }\r\n\r\n  onLocationClick(location: any): void {\r\n    console.log('Location clicked:', location);\r\n    // Add your navigation logic here\r\n    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });\r\n  }\r\n\r\n  // Method to load locations from API (for future integration)\r\n  // loadLocations(): void {\r\n  //   // Replace with actual API call\r\n  //   // this.locationService.getLocations().subscribe(data => {\r\n  //   //   this.locations = data;\r\n  //   //   this.initializeLocationSlides();\r\n  //   //   // Re-initialize carousel after data loads\r\n  //   //   setTimeout(() => this.initializeCarousel(), 100);\r\n  //   // });\r\n  // }\r\n\r\n  loadMoreLocations(){\r\n\r\n  }\r\n\r\n}\r\n", "<div class=\"home-page\">\r\n\r\n  <!-- Header Section -->\r\n  <header class=\"home-header\">\r\n    <!-- Navigation Bar -->\r\n    <nav class=\"navbar navbar-expand-lg\">\r\n      <div class=\"container-fluid px-4\">\r\n        <!-- Logo -->\r\n        <div class=\"navbar-brand\">\r\n          <img alt=\"Logo\" src=\"./assets/media/easydeallogos/loading-logo.png\" class=\"h-40px app-sidebar-logo-default\" />\r\n        </div>\r\n\r\n        <!-- Mobile Menu Toggle Button -->\r\n        <button class=\"navbar-toggler d-lg-none\" type=\"button\" (click)=\"toggleMobileMenu()\"\r\n          [attr.aria-expanded]=\"showMobileMenu\" aria-label=\"Toggle navigation\">\r\n          <span class=\"navbar-toggler-icon\">\r\n          </span>\r\n        </button>\r\n\r\n        <!-- Navigation Menu -->\r\n        <div class=\"navbar-nav mx-auto d-none d-lg-flex\">\r\n          <ul class=\"nav-list d-flex align-items-center mb-0\">\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Home </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> About EasyDeal </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> New Projects </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Advertisements </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Contact Us </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <!-- Mobile Navigation Dropdown -->\r\n        <div *ngIf=\"showMobileMenu\" class=\"mobile-nav-dropdown d-lg-none\">\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-home fs-6 text-primary\"></i>\r\n            </span>\r\n            <span>Home</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-info-circle fs-6 text-info\"></i>\r\n            </span>\r\n            <span>About EasyDeal</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-building fs-6 text-success\"></i>\r\n            </span>\r\n            <span>New Projects</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-bullhorn fs-6 text-warning\"></i>\r\n            </span>\r\n            <span>Advertisements</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-phone fs-6 text-gray-600\"></i>\r\n            </span>\r\n            <span>Contact Us</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- User Registration Link / User Profile -->\r\n        <div class=\"navbar-nav position-relative\">\r\n          <!-- If user is logged in, show user profile -->\r\n          <div *ngIf=\"isLoggedIn\" class=\"nav-link user-profile\" (click)=\"toggleUserDropdown()\">\r\n            <img [src]=\"getUserProfileImage()\" [alt]=\"getUserDisplayName()\" class=\"user-avatar me-2\">\r\n            <span class=\"user-name\">{{ getUserDisplayName() }}</span>\r\n            <i class=\"fas fa-chevron-down ms-2\"></i>\r\n          </div>\r\n\r\n          <!-- User Dropdown Menu -->\r\n          <div *ngIf=\"isLoggedIn && showUserDropdown\" class=\"user-dropdown\">\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <svg width=\"19\" height=\"19\" viewBox=\"0 0 19 19\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <g clip-path=\"url(#clip0_24_2533)\">\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\" />\r\n                  </g>\r\n                  <defs>\r\n                    <clipPath id=\"clip0_24_2533\">\r\n                      <rect width=\"19\" height=\"19\" fill=\"white\" />\r\n                    </clipPath>\r\n                  </defs>\r\n                </svg>\r\n              </span>\r\n              <span>Requests</span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"user\" class=\"fs-5 text-primary\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> My Profile </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"messages\" class=\"fs-5 text-info\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Messages </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fa-regular fa-circle-question fs-6 text-warning\"></i>\r\n              </span>\r\n              <span> Help </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"notification-on\" class=\"fs-5 text-gray-600\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Notifications </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item logout-item\" (click)=\"logout()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fas fa-sign-out-alt fs-6 text-danger\"></i>\r\n              </span>\r\n              <span> Logout </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item new-request-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"text-success\"> New Request </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- If user is not logged in, show register button -->\r\n          <a *ngIf=\"!isLoggedIn\" href=\"#\" class=\"nav-link user-link\">\r\n            <i class=\"fas fa-user me-2\"></i>\r\n            Register Guest\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n\r\n    <!-- Hero Section -->\r\n    <div class=\"hero-section\">\r\n      <div class=\"hero-background\">\r\n        <img\r\n          src=\"./assets/media/home/<USER>\"\r\n          alt=\"Hero Background\" class=\"hero-bg-image\">\r\n        <div class=\"hero-overlay\"></div>\r\n      </div>\r\n\r\n      <div class=\"hero-content\">\r\n        <div class=\"container\">\r\n          <div class=\"row justify-content-center\">\r\n            <div class=\"col-12\">\r\n              <div class=\"hero-text-container\">\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Easy</h2>\r\n                </div>\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Speed </h2>\r\n                </div>\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Reliability </h2>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </header>\r\n\r\n  <!-- Properties Section -->\r\n  <section class=\"properties-section\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <h2 class=\"section-title text-center mb-5\">Featured Properties</h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row g-4\">\r\n        <!-- Property Card 1 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-1.jpg\" alt=\"Property 1\" class=\"img-fluid\">\r\n              <div class=\"property-badge\">For Sale</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>New Cairo</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Luxury Apartment</h4>\r\n              <p class=\"property-description\">3 Bedrooms • 2 Bathrooms • 150 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">2.5M EGP</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 2 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-15.jpg\" alt=\"Property 2\" class=\"img-fluid\">\r\n              <div class=\"property-badge property-badge-rent\">For Rent</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Maadi</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Modern Villa</h4>\r\n              <p class=\"property-description\">4 Bedrooms • 3 Bathrooms • 250 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">25K EGP/month</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 3 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-25.jpg\" alt=\"Property 3\" class=\"img-fluid\">\r\n              <div class=\"property-badge\">For Sale</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Zamalek</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Penthouse Suite</h4>\r\n              <p class=\"property-description\">5 Bedrooms • 4 Bathrooms • 300 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">8.5M EGP</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 4 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-35.jpg\" alt=\"Property 4\" class=\"img-fluid\">\r\n              <div class=\"property-badge property-badge-rent\">For Rent</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Heliopolis</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Family House</h4>\r\n              <p class=\"property-description\">3 Bedrooms • 2 Bathrooms • 180 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">18K EGP/month</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Explore Locations Section -->\r\n  <section class=\"horizontal-carousel-section py-5\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <h2 class=\"section-title text-center mb-5\">Explore Locations</h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"carousel-container position-relative\">\r\n        <!-- Bootstrap Carousel -->\r\n        <div id=\"horizontalCarousel\" class=\"carousel slide\" data-bs-ride=\"carousel\" data-bs-interval=\"5000\">\r\n          <div class=\"carousel-inner\">\r\n            <!-- Dynamic Slides -->\r\n            <div class=\"carousel-item \" *ngFor=\"let slide of locationSlides; let i = index\" [class.active]=\"i === 0\">\r\n              <div class=\"row justify-content-center g-2 g-md-3 mt-5\">\r\n                <div class=\"col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6\" *ngFor=\"let location of slide\">\r\n                  <div class=\"location-card\" (click)=\"onLocationClick(location)\">\r\n                    <img [src]=\"location.image || 'assets/media/auth/404-error.png'\" [alt]=\"location.name\"\r\n                      class=\"img-fluid\">\r\n                    <div class=\"location-overlay\">\r\n                      <div class=\"location-info\">\r\n                        <h5>{{ location.name }}</h5>\r\n                        <p><i class=\"fas fa-map-marker-alt\"></i> {{ location.propertyCount }} Properties Available</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Carousel Controls -->\r\n          <button class=\"carousel-control-prev\" type=\"button\" data-bs-target=\"#horizontalCarousel\" data-bs-slide=\"prev\"\r\n            (click)=\"prevSlide()\">\r\n            <span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"></span>\r\n            <span class=\"visually-hidden\">Previous</span>\r\n          </button>\r\n          <button class=\"carousel-control-next\" type=\"button\" data-bs-target=\"#horizontalCarousel\" data-bs-slide=\"next\"\r\n            (click)=\"nextSlide()\">\r\n            <span class=\"carousel-control-next-icon\" aria-hidden=\"true\"></span>\r\n            <span class=\"visually-hidden\">Next</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row justify-content-center mt-5\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-secondary btn-lg\" (click)=\"loadMoreLocations()\">\r\n            Load More Locations\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Articles Section -->\r\n  <section class=\"articles-section py-5\">\r\n    <div class=\"container\">\r\n      <!-- Section Header -->\r\n      <div class=\"row mb-5\">\r\n        <div class=\"col-12\">\r\n          <div class=\"section-header d-flex justify-content-between align-items-center\">\r\n            <div class=\"left-controls d-flex align-items-center\">\r\n              <button class=\"carousel-control-btn prev-btn\" type=\"button\" data-bs-target=\"#articlesCarousel\"\r\n                data-bs-slide=\"prev\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n              <button class=\"carousel-control-btn next-btn ms-2\" type=\"button\" data-bs-target=\"#articlesCarousel\"\r\n                data-bs-slide=\"next\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <h1 class=\"articles-title text-center flex-grow-1  \">Articles That Interest You</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Bootstrap Carousel for Articles with All Articles Link -->\r\n      <div class=\"row\">\r\n        <!-- All Articles Link on the Left -->\r\n        <div class=\"col-lg-2 col-md-3 d-flex align-items-center justify-content-center\">\r\n          <div class=\"right-link\">\r\n            <a href=\"#\" class=\"view-all-link\">\r\n              <i class=\"fas fa-arrow-left me-2 text-success fs-4\"></i>\r\n              <span class=\"text-success fs-2\">All Articles</span>\r\n              <div class=\"green-underline\"></div>\r\n            </a>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Carousel on the Right -->\r\n        <div class=\"col-lg-10 col-md-9\">\r\n          <div id=\"articlesCarousel\" class=\"carousel slide\" data-bs-ride=\"carousel\" data-bs-interval=\"6000\">\r\n            <div class=\"carousel-inner\">\r\n              <!-- Slide 1 -->\r\n              <div class=\"carousel-item active\">\r\n                <div class=\"row g-4 justify-content-center\">\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-10.jpg\" alt=\"Article 1\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Modern Finishing Materials - Shop with the Best</h4>\r\n                            <p>A very quiet area away from the noise and hustle of the city, suitable for large and\r\n                              small\r\n                              families, spacious area with a private garden.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-20.jpg\" alt=\"Article 2\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Invest Your Money with Hotel Property</h4>\r\n                            <p>Excellent investment opportunity in the heart of the city, guaranteed returns and\r\n                              integrated\r\n                              management, strategic location near the airport and commercial centers.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-30.jpg\" alt=\"Article 3\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Villa 10 October April 2019</h4>\r\n                            <p>Latest international finishing materials, high quality and competitive prices,\r\n                              specialized\r\n                              team to implement finishing works to the highest standards.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Slide 2 -->\r\n              <div class=\"carousel-item\">\r\n                <div class=\"row g-4 justify-content-center\">\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-40.jpg\" alt=\"Article 4\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Apartment in New Cairo</h4>\r\n                            <p>Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated\r\n                              facilities, close to universities and international schools.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-50.jpg\" alt=\"Article 5\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>North Coast Properties</h4>\r\n                            <p>Residential units directly on the sea, wonderful panoramic view, integrated recreational\r\n                              facilities and suitable for summer investment.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-60.jpg\" alt=\"Article 6\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Administrative Offices Downtown</h4>\r\n                            <p>Modern office spaces in the heart of Cairo, suitable for companies and institutions,\r\n                              parking\r\n                              and integrated service facilities.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Carousel Indicators -->\r\n            <div class=\"carousel-indicators\">\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"0\" class=\"active\"></button>\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"1\"></button>\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"2\"></button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n</div>"], "mappings": ";;;;;;IC2CYA,EAFJ,CAAAC,cAAA,eAAkE,eACrC,gBACI;IAC3BD,EAAA,CAAAE,SAAA,aAA6C;IAC/CF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,WAAI;IACZJ,EADY,CAAAG,YAAA,EAAO,EACb;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACI;IAC3BD,EAAA,CAAAE,SAAA,aAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACtBJ,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACtBJ,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IAEpBJ,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;;;;;;IAKJH,EAAA,CAAAC,cAAA,eAAqF;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,mDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAClFZ,EAAA,CAAAE,SAAA,eAAyF;IACzFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAE,SAAA,aAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;IAHCH,EAAA,CAAAa,SAAA,EAA6B;IAACb,EAA9B,CAAAc,UAAA,QAAAL,MAAA,CAAAM,mBAAA,IAAAf,EAAA,CAAAgB,aAAA,CAA6B,QAAAP,MAAA,CAAAQ,kBAAA,GAA6B;IACvCjB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAQ,kBAAA,GAA0B;;;;;;IAMlDjB,EADF,CAAAC,cAAA,eAAkE,eACP;IAA9BD,EAAA,CAAAK,UAAA,mBAAAc,mDAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;;IAEzBD,EADF,CAAAC,cAAA,eAA+F,aAC1D;IAKjCD,EAJA,CAAAE,SAAA,gBACmN,gBAEuZ,gBAE4O;IACx1BF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,WAAM,oBACyB;IAC3BD,EAAA,CAAAE,SAAA,iBAA4C;IAIpDF,EAHM,CAAAG,YAAA,EAAW,EACN,EACH,EACD;;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAChBJ,EADgB,CAAAG,YAAA,EAAO,EACjB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAiB,oDAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAAkF;IACpFF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,oBAAW;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAkB,oDAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAAmF;IACrFF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,kBAAS;IAClBJ,EADkB,CAAAG,YAAA,EAAO,EACnB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAmB,oDAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA+D;IACjEF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,cAAK;IACdJ,EADc,CAAAG,YAAA,EAAO,EACf;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAoB,oDAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAA8F;IAChGF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,uBAAc;IACvBJ,EADuB,CAAAG,YAAA,EAAO,EACxB;IACNH,EAAA,CAAAE,SAAA,gBAAoC;IACpCF,EAAA,CAAAC,cAAA,gBAA0D;IAAnBD,EAAA,CAAAK,UAAA,mBAAAqB,oDAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IACvD3B,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAAoD;IACtDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,gBAAO;IAChBJ,EADgB,CAAAG,YAAA,EAAO,EACjB;IACNH,EAAA,CAAAE,SAAA,gBAAoC;IACpCF,EAAA,CAAAC,cAAA,gBAA0E;IAA9BD,EAAA,CAAAK,UAAA,mBAAAuB,oDAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACvErB,EAAA,CAAAC,cAAA,iBAA2B;IAACD,EAAA,CAAAI,MAAA,qBAAY;IAE5CJ,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAsOIH,EADF,CAAAC,cAAA,eAAsF,eACrB;IAApCD,EAAA,CAAAK,UAAA,mBAAAwB,0DAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA,EAAAC,SAAA;MAAA,MAAAvB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwB,eAAA,CAAAH,WAAA,CAAyB;IAAA,EAAC;IAC5D9B,EAAA,CAAAE,SAAA,eACoB;IAGhBF,EAFJ,CAAAC,cAAA,eAA8B,eACD,SACrB;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,SAAA,YAAqC;IAACF,EAAA,CAAAI,MAAA,GAAiD;IAIlGJ,EAJkG,CAAAG,YAAA,EAAI,EAC1F,EACF,EACF,EACF;;;;IATGH,EAAA,CAAAa,SAAA,GAA2D;IAACb,EAA5D,CAAAc,UAAA,QAAAgB,WAAA,CAAAI,KAAA,uCAAAlC,EAAA,CAAAgB,aAAA,CAA2D,QAAAc,WAAA,CAAAK,IAAA,CAAsB;IAI9EnC,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAkB,iBAAA,CAAAY,WAAA,CAAAK,IAAA,CAAmB;IACkBnC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAoC,kBAAA,MAAAN,WAAA,CAAAO,aAAA,0BAAiD;;;;;IARpGrC,EADF,CAAAC,cAAA,cAAyG,eAC/C;IACtDD,EAAA,CAAAsC,UAAA,IAAAC,oCAAA,oBAAsF;IAa1FvC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAf0EH,EAAA,CAAAwC,WAAA,WAAAC,IAAA,OAAwB;IAExBzC,EAAA,CAAAa,SAAA,GAAQ;IAARb,EAAA,CAAAc,UAAA,YAAA4B,QAAA,CAAQ;;;AD7WpG,OAAM,MAAOC,aAAa;EAqFJC,WAAA;EApFpBC,WAAW,GAAQ,IAAI;EACvBC,UAAU,GAAY,KAAK;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,cAAc,GAAY,KAAK;EAE/B;EACAC,cAAc,GAAY,EAAE;EAC5BC,iBAAiB,GAAW,CAAC;EAC7BC,gBAAgB;EAChBC,SAAS,GAAU,CACjB;IACEC,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,YAAY;IAClBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,SAAS;IACfD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,iBAAiB;IACvBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,eAAe;IACrBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,cAAc;IACpBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,SAAS;IACfD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,WAAW;IACjBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,CACF;EAEDiB,YAAoBV,WAAkC;IAAlC,KAAAA,WAAW,GAAXA,WAAW;EAA2B;EAE1DW,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACV,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACtC;EACF;EAEAK,gBAAgBA,CAAA;IACd;IACA,MAAMO,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMpB,WAAW,GAAGmB,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,SAAS,IAAIlB,WAAW,EAAE;MAC5B,IAAI;QACF,IAAI,CAACA,WAAW,GAAGqB,IAAI,CAACC,KAAK,CAACtB,WAAW,CAAC;QAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACd;QACA,IAAI,CAACtB,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACzB;EACF;EAEA5B,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC4B,WAAW,EAAE;MACpB,OAAO,IAAI,CAACA,WAAW,CAACwB,QAAQ,IAAK,MAAM;IAC7C;IACA,OAAO,OAAO;EAChB;EAEAtD,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC8B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACX,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACW,WAAW,CAACX,KAAK;IAC/B;IACA;IACA,OAAO,gCAAgC;EACzC;EAEAtB,kBAAkBA,CAAA;IAChB,IAAI,CAACmC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA1B,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,gBAAgB,GAAG,KAAK;EAC/B;EAEApB,MAAMA,CAAA;IACJqC,YAAY,CAACM,UAAU,CAAC,WAAW,CAAC;IACpCN,YAAY,CAACM,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACxB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B;IACA;EACF;EAEAwB,gBAAgBA,CAAA;IACd,IAAI,CAACvB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C;IACA,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEAyB,eAAeA,CAAA;IACb,IAAI,CAACxB,cAAc,GAAG,KAAK;EAC7B;EAGAyB,eAAeA,CAACC,KAAY;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;IACnD,MAAMC,YAAY,GAAGH,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAC;IACrD,MAAME,aAAa,GAAGJ,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;IACvD,MAAMG,iBAAiB,GAAGL,MAAM,CAACE,OAAO,CAAC,sBAAsB,CAAC;IAEhE;IACA,IAAI,CAACD,WAAW,IAAI,CAACE,YAAY,IAAI,IAAI,CAAC/B,gBAAgB,EAAE;MAC1D,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC/B;IAEA;IACA,IAAI,CAACgC,aAAa,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAAChC,cAAc,EAAE;MAC/D,IAAI,CAACA,cAAc,GAAG,KAAK;IAC7B;EACF;EAEA;EACAS,wBAAwBA,CAAA;IACtB;IACA,MAAMwB,aAAa,GAAG,CAAC;IACvB,IAAI,CAAChC,cAAc,GAAG,EAAE;IAExB,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,MAAM,EAAED,CAAC,IAAID,aAAa,EAAE;MAC7D,IAAI,CAAChC,cAAc,CAACmC,IAAI,CAAC,IAAI,CAAChC,SAAS,CAACiC,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGD,aAAa,CAAC,CAAC;IACtE;EACF;EAEArB,kBAAkBA,CAAA;IAChB,IAAI;MACF,MAAM0B,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MACrE,IAAIF,eAAe,EAAE;QACnB;QACA,IAAI,OAAOG,SAAS,KAAK,WAAW,EAAE;UACpC,MAAMC,QAAQ,GAAG,IAAID,SAAS,CAACE,QAAQ,CAACL,eAAe,EAAE;YACvDM,QAAQ,EAAE,IAAI;YACdC,IAAI,EAAE,UAAU;YAChBC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC/C,CAAC,MAAM;UACL;UACA,IAAI,CAACC,mBAAmB,EAAE;UAC1BF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;MACF;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACd6B,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACA,IAAI,CAAC+B,mBAAmB,EAAE;IAC5B;EACF;EAEAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,IAAI,CAAChD,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACtC;IAEA;IACA,IAAI,CAACA,gBAAgB,GAAGiD,WAAW,CAAC,MAAK;MACvC,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAACrD,cAAc,CAACkC,MAAM;IAC9C,IAAImB,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB,GAAG,CAAC,IAAIoD,WAAW;MACnE,IAAI,CAACC,qBAAqB,EAAE;IAC9B;EACF;EAEAC,SAASA,CAAA;IACP,MAAMF,WAAW,GAAG,IAAI,CAACrD,cAAc,CAACkC,MAAM;IAC9C,IAAImB,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,KAAK,CAAC,GAAGoD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACpD,iBAAiB,GAAG,CAAC;MACpG,IAAI,CAACqD,qBAAqB,EAAE;IAC9B;EACF;EAEAA,qBAAqBA,CAAA;IACnB,MAAME,aAAa,GAAGlB,QAAQ,CAACmB,gBAAgB,CAAC,oCAAoC,CAAC;IACrFD,aAAa,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACpC,IAAIA,KAAK,KAAK,IAAI,CAAC3D,iBAAiB,EAAE;QACpC0D,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLH,IAAI,CAACE,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC;EACJ;EAEA/E,eAAeA,CAACgF,QAAa;IAC3BhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,QAAQ,CAAC;IAC1C;IACA;EACF;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,iBAAiBA,CAAA,GAEjB;;qCA9RWvE,aAAa,EAAA3C,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,qBAAA;EAAA;;UAAb1E,aAAa;IAAA2E,SAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAbzH,EAAA,CAAAK,UAAA,mBAAAsH,uCAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAjD,eAAA,CAAAmD,MAAA,CAAuB;QAAA,UAAA5H,EAAA,CAAA6H,iBAAA,CAAV;;;;;;;;QCFlB7H,EARR,CAAAC,cAAA,aAAuB,gBAGO,aAEW,aACD,aAEN;QACxBD,EAAA,CAAAE,SAAA,aAA8G;QAChHF,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,gBACuE;QADhBD,EAAA,CAAAK,UAAA,mBAAAyH,+CAAA;UAAA,OAASJ,GAAA,CAAAnD,gBAAA,EAAkB;QAAA,EAAC;QAEjFvE,EAAA,CAAAE,SAAA,cACO;QACTF,EAAA,CAAAG,YAAA,EAAS;QAMHH,EAHN,CAAAC,cAAA,aAAiD,YACK,cAC7B,aACU;QAACD,EAAA,CAAAI,MAAA,cAAK;QACrCJ,EADqC,CAAAG,YAAA,EAAI,EACpC;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,wBAAe;QAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC9C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,sBAAa;QAC7CJ,EAD6C,CAAAG,YAAA,EAAI,EAC5C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,wBAAe;QAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC9C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,oBAAW;QAG/CJ,EAH+C,CAAAG,YAAA,EAAI,EAC1C,EACF,EACD;QAGNH,EAAA,CAAAsC,UAAA,KAAAyF,6BAAA,mBAAkE;QAkClE/H,EAAA,CAAAC,cAAA,eAA0C;QAoExCD,EAlEA,CAAAsC,UAAA,KAAA0F,6BAAA,kBAAqF,KAAAC,6BAAA,mBAOnB,KAAAC,2BAAA,gBA2DP;QAMjElI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;QAIJH,EADF,CAAAC,cAAA,eAA0B,eACK;QAI3BD,EAHA,CAAAE,SAAA,eAE8C,eACd;QAClCF,EAAA,CAAAG,YAAA,EAAM;QAQMH,EANZ,CAAAC,cAAA,eAA0B,eACD,eACmB,eAClB,eACe,eACH,cACJ;QAACD,EAAA,CAAAI,MAAA,aAAI;QAC7BJ,EAD6B,CAAAG,YAAA,EAAK,EAC5B;QAEJH,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAI,MAAA,eAAM;QAC/BJ,EAD+B,CAAAG,YAAA,EAAK,EAC9B;QAEJH,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAI,MAAA,qBAAY;QAQnDJ,EARmD,CAAAG,YAAA,EAAK,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACC;QAODH,EAJR,CAAAC,cAAA,mBAAoC,eACX,eACJ,eACK,cACyB;QAAAD,EAAA,CAAAI,MAAA,2BAAmB;QAElEJ,EAFkE,CAAAG,YAAA,EAAK,EAC/D,EACF;QAMAH,EAJN,CAAAC,cAAA,eAAqB,eAEsB,eACZ,eACG;QAC1BD,EAAA,CAAAE,SAAA,eAAqF;QACrFF,EAAA,CAAAC,cAAA,eAA4B;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1CH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAE,SAAA,aAAqC;QACrCF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAI,MAAA,iBAAS;QAEnBJ,EAFmB,CAAAG,YAAA,EAAO,EAClB,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8B,cACD;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAChDH,EAAA,CAAAC,cAAA,aAAgC;QAAAD,EAAA,CAAAI,MAAA,oDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,eAA4B,gBACN;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;QAGFH,EAFJ,CAAAC,cAAA,eAAqC,eACN,eACR;QAKjBD,EAJA,CAAAE,SAAA,aAA2B,aACA,aACA,aACA,aACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,gBAA0B;QAAAD,EAAA,CAAAI,MAAA,WAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,eAA8B,kBACqB;QAC/CD,EAAA,CAAAE,SAAA,aAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAiD;QAC/CD,EAAA,CAAAE,SAAA,aAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,eAAyC,eACZ,eACG;QAC1BD,EAAA,CAAAE,SAAA,eAAsF;QACtFF,EAAA,CAAAC,cAAA,eAAgD;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC9DH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAE,SAAA,aAAqC;QACrCF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAEfJ,EAFe,CAAAG,YAAA,EAAO,EACd,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8B,cACD;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,gBAAyC,gBACZ,gBACG;QAC1BD,EAAA,CAAAE,SAAA,gBAAsF;QACtFF,EAAA,CAAAC,cAAA,gBAA4B;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1CH,EAAA,CAAAC,cAAA,gBAA+B;QAC7BD,EAAA,CAAAE,SAAA,cAAqC;QACrCF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,gBAAO;QAEjBJ,EAFiB,CAAAG,YAAA,EAAO,EAChB,EACF;QAEJH,EADF,CAAAC,cAAA,gBAA8B,eACD;QAAAD,EAAA,CAAAI,MAAA,wBAAe;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,gBAAyC,gBACZ,gBACG;QAC1BD,EAAA,CAAAE,SAAA,gBAAsF;QACtFF,EAAA,CAAAC,cAAA,gBAAgD;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC9DH,EAAA,CAAAC,cAAA,gBAA+B;QAC7BD,EAAA,CAAAE,SAAA,cAAqC;QACrCF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,mBAAU;QAEpBJ,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;QAEJH,EADF,CAAAC,cAAA,gBAA8B,eACD;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QASlDF,EARgB,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF,EACF,EACE;QAOFH,EAJR,CAAAC,cAAA,oBAAkD,gBACzB,gBACJ,gBACK,eACyB;QAAAD,EAAA,CAAAI,MAAA,0BAAiB;QAEhEJ,EAFgE,CAAAG,YAAA,EAAK,EAC7D,EACF;QAKFH,EAHJ,CAAAC,cAAA,gBAAkD,gBAEoD,gBACtE;QAE1BD,EAAA,CAAAsC,UAAA,MAAA6F,8BAAA,kBAAyG;QAgB3GnI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,mBACwB;QAAtBD,EAAA,CAAAK,UAAA,mBAAA+H,iDAAA;UAAA,OAASV,GAAA,CAAAlB,SAAA,EAAW;QAAA,EAAC;QACrBxG,EAAA,CAAAE,SAAA,iBAAmE;QACnEF,EAAA,CAAAC,cAAA,iBAA8B;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QACxCJ,EADwC,CAAAG,YAAA,EAAO,EACtC;QACTH,EAAA,CAAAC,cAAA,mBACwB;QAAtBD,EAAA,CAAAK,UAAA,mBAAAgI,iDAAA;UAAA,OAASX,GAAA,CAAArB,SAAA,EAAW;QAAA,EAAC;QACrBrG,EAAA,CAAAE,SAAA,iBAAmE;QACnEF,EAAA,CAAAC,cAAA,iBAA8B;QAAAD,EAAA,CAAAI,MAAA,aAAI;QAGxCJ,EAHwC,CAAAG,YAAA,EAAO,EAClC,EACL,EACF;QAIFH,EAFJ,CAAAC,cAAA,gBAA6C,gBACX,mBACyC;QAA9BD,EAAA,CAAAK,UAAA,mBAAAiI,iDAAA;UAAA,OAASZ,GAAA,CAAAR,iBAAA,EAAmB;QAAA,EAAC;QACpElH,EAAA,CAAAI,MAAA,8BACF;QAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAUEH,EAPZ,CAAAC,cAAA,oBAAuC,gBACd,gBAEC,gBACA,gBAC4D,gBACvB,mBAE5B;QACrBD,EAAA,CAAAE,SAAA,cAAmC;QACrCF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBACuB;QACrBD,EAAA,CAAAE,SAAA,cAAoC;QAExCF,EADE,CAAAG,YAAA,EAAS,EACL;QAENH,EAAA,CAAAC,cAAA,eAAqD;QAAAD,EAAA,CAAAI,MAAA,mCAA0B;QAGrFJ,EAHqF,CAAAG,YAAA,EAAK,EAChF,EACF,EACF;QAOAH,EAJN,CAAAC,cAAA,gBAAiB,gBAEiE,gBACtD,cACY;QAChCD,EAAA,CAAAE,SAAA,cAAwD;QACxDF,EAAA,CAAAC,cAAA,iBAAgC;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QACnDH,EAAA,CAAAE,SAAA,gBAAmC;QAGzCF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAWQH,EARd,CAAAC,cAAA,gBAAgC,gBACoE,gBACpE,gBAEQ,gBACY,gBACF,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,wDAA+C;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACxDH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,kJAE6C;QAK1DJ,EAL0D,CAAAG,YAAA,EAAI,EAChD,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,8CAAqC;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC9CH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,6KAEsE;QAKnFJ,EALmF,CAAAG,YAAA,EAAI,EACzE,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,oCAA2B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,+JAE0D;QAO3EJ,EAP2E,CAAAG,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF,EACF,EACF;QAOEH,EAJR,CAAAC,cAAA,gBAA2B,gBACmB,gBACF,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,+BAAsB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/BH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,+JAC2D;QAKxEJ,EALwE,CAAAG,YAAA,EAAI,EAC9D,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,+BAAsB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/BH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,gJAC6C;QAK1DJ,EAL0D,CAAAG,YAAA,EAAI,EAChD,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,iBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,wCAA+B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACxCH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,wIAEiC;QAQpDJ,EARoD,CAAAG,YAAA,EAAI,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACF;QAGNH,EAAA,CAAAC,cAAA,iBAAiC;QAG/BD,EAFA,CAAAE,SAAA,oBAAsG,oBACf,oBACA;QAQrGF,EAPY,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACE,EAEN;;;QA1iBIH,EAAA,CAAAa,SAAA,GAAqC;;QA2BjCb,EAAA,CAAAa,SAAA,IAAoB;QAApBb,EAAA,CAAAc,UAAA,SAAA4G,GAAA,CAAA1E,cAAA,CAAoB;QAoClBhD,EAAA,CAAAa,SAAA,GAAgB;QAAhBb,EAAA,CAAAc,UAAA,SAAA4G,GAAA,CAAA5E,UAAA,CAAgB;QAOhB9C,EAAA,CAAAa,SAAA,EAAoC;QAApCb,EAAA,CAAAc,UAAA,SAAA4G,GAAA,CAAA5E,UAAA,IAAA4E,GAAA,CAAA3E,gBAAA,CAAoC;QA2DtC/C,EAAA,CAAAa,SAAA,EAAiB;QAAjBb,EAAA,CAAAc,UAAA,UAAA4G,GAAA,CAAA5E,UAAA,CAAiB;QAsO2B9C,EAAA,CAAAa,SAAA,KAAmB;QAAnBb,EAAA,CAAAc,UAAA,YAAA4G,GAAA,CAAAzE,cAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}