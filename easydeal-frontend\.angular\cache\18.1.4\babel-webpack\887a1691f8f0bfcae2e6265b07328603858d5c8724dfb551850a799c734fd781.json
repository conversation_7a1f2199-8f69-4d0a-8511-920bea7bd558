{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication/services/authentication.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../_metronic/shared/keenicon/keenicon.component\";\nfunction HomeComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106)(2, \"span\", 107);\n    i0.ɵɵelement(3, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 106)(7, \"span\", 107);\n    i0.ɵɵelement(8, \"i\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"About EasyDeal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 106)(12, \"span\", 107);\n    i0.ɵɵelement(13, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"New Projects\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 106)(17, \"span\", 107);\n    i0.ɵɵelement(18, \"i\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Advertisements\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 106)(22, \"span\", 107);\n    i0.ɵɵelement(23, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Contact Us\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_27_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserDropdown());\n    });\n    i0.ɵɵelement(1, \"img\", 114);\n    i0.ɵɵelementStart(2, \"span\", 115);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 116);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getUserProfileImage(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n  }\n}\nfunction HomeComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(2, \"span\", 107);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 119)(4, \"g\", 120);\n    i0.ɵɵelement(5, \"path\", 121)(6, \"path\", 122)(7, \"path\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"defs\")(9, \"clipPath\", 124);\n    i0.ɵɵelement(10, \"rect\", 125);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Requests\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(14, \"span\", 107);\n    i0.ɵɵelement(15, \"app-keenicon\", 126);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \" My Profile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(19, \"span\", 107);\n    i0.ɵɵelement(20, \"app-keenicon\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(24, \"span\", 107);\n    i0.ɵɵelement(25, \"i\", 128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \" Help \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 118);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(29, \"span\", 107);\n    i0.ɵɵelement(30, \"app-keenicon\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \" Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"div\", 130);\n    i0.ɵɵelementStart(34, \"div\", 131);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"span\", 107);\n    i0.ɵɵelement(36, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"div\", 130);\n    i0.ɵɵelementStart(40, \"div\", 133);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_40_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(41, \"span\", 134);\n    i0.ɵɵtext(42, \" New Request \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_a_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 135);\n    i0.ɵɵelement(1, \"i\", 136);\n    i0.ɵɵtext(2, \" Register Guest \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_196_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 139)(1, \"div\", 140);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_196_div_2_Template_div_click_1_listener() {\n      const location_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLocationClick(location_r5));\n    });\n    i0.ɵɵelement(2, \"img\", 141);\n    i0.ɵɵelementStart(3, \"div\", 142)(4, \"div\", 143)(5, \"h5\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵelement(8, \"i\", 38);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const location_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", location_r5.image || \"assets/media/auth/404-error.png\", i0.ɵɵsanitizeUrl)(\"alt\", location_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(location_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", location_r5.propertyCount, \" Properties Available\");\n  }\n}\nfunction HomeComponent_div_196_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 137);\n    i0.ɵɵtemplate(2, HomeComponent_div_196_div_2_Template, 10, 4, \"div\", 138);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const slide_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r7 === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", slide_r6);\n  }\n}\nexport class HomeComponent {\n  authService;\n  currentUser = null;\n  isLoggedIn = false;\n  showUserDropdown = false;\n  showMobileMenu = false;\n  // Location Carousel Data\n  locationSlides = [];\n  currentSlideIndex = 0;\n  carouselInterval;\n  locations = [{\n    id: 1,\n    name: ' New Cairo',\n    image: './assets/media/stock/600x400/img-10.jpg',\n    propertyCount: 2341\n  }, {\n    id: 2,\n    name: '  Maadi',\n    image: './assets/media/stock/600x400/img-20.jpg',\n    propertyCount: 1234\n  }, {\n    id: 3,\n    name: ' Sheikh Zayed  ',\n    image: './assets/media/stock/600x400/img-30.jpg',\n    propertyCount: 3421\n  }, {\n    id: 4,\n    name: '   Heliopolis',\n    image: './assets/media/stock/600x400/img-40.jpg',\n    propertyCount: 2341\n  }, {\n    id: 5,\n    name: '   Nasr City',\n    image: './assets/media/stock/600x400/img-50.jpg',\n    propertyCount: 987\n  }, {\n    id: 6,\n    name: '  6 October',\n    image: './assets/media/stock/600x400/img-60.jpg',\n    propertyCount: 1543\n  }, {\n    id: 7,\n    name: '  Maadi',\n    image: './assets/media/stock/600x400/img-70.jpg',\n    propertyCount: 876\n  }, {\n    id: 8,\n    name: '  Zamalek',\n    image: './assets/media/stock/600x400/img-80.jpg',\n    propertyCount: 654\n  }, {\n    id: 9,\n    name: '  New Cairo',\n    image: './assets/media/stock/600x400/img-90.jpg',\n    propertyCount: 1098\n  }, {\n    id: 10,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }, {\n    id: 11,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }, {\n    id: 12,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }];\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.checkUserSession();\n    this.initializeLocationSlides();\n  }\n  ngAfterViewInit() {\n    // Initialize Bootstrap carousel after view is loaded\n    setTimeout(() => {\n      this.initializeCarousel();\n    }, 100);\n  }\n  ngOnDestroy() {\n    // Clean up interval when component is destroyed\n    if (this.carouselInterval) {\n      clearInterval(this.carouselInterval);\n    }\n  }\n  checkUserSession() {\n    // Check if user is logged in by checking localStorage\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      try {\n        this.currentUser = JSON.parse(currentUser);\n        this.isLoggedIn = true;\n      } catch (error) {\n        // If parsing fails, user is not logged in\n        this.isLoggedIn = false;\n        this.currentUser = null;\n      }\n    } else {\n      this.isLoggedIn = false;\n      this.currentUser = null;\n    }\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return this.currentUser.fullName || 'User';\n    }\n    return 'Guest';\n  }\n  getUserProfileImage() {\n    if (this.currentUser && this.currentUser.image) {\n      return this.currentUser.image;\n    }\n    // Return default avatar if no profile image\n    return 'assets/media/avatars/blank.png';\n  }\n  toggleUserDropdown() {\n    this.showUserDropdown = !this.showUserDropdown;\n  }\n  closeUserDropdown() {\n    this.showUserDropdown = false;\n  }\n  logout() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('currentUser');\n    this.isLoggedIn = false;\n    this.currentUser = null;\n    this.showUserDropdown = false;\n    // Optionally redirect to login page\n    // this.router.navigate(['/authentication/login']);\n  }\n  toggleMobileMenu() {\n    this.showMobileMenu = !this.showMobileMenu;\n    // Close user dropdown when mobile menu is toggled\n    if (this.showMobileMenu) {\n      this.showUserDropdown = false;\n    }\n  }\n  closeMobileMenu() {\n    this.showMobileMenu = false;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const userProfile = target.closest('.user-profile');\n    const userDropdown = target.closest('.user-dropdown');\n    const navbarToggler = target.closest('.navbar-toggler');\n    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');\n    // Close user dropdown if clicked outside of user profile and dropdown\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\n      this.showUserDropdown = false;\n    }\n    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown\n    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {\n      this.showMobileMenu = false;\n    }\n  }\n  // Location Carousel Methods\n  initializeLocationSlides() {\n    // Split locations into slides of 5 items each\n    const itemsPerSlide = 5;\n    this.locationSlides = [];\n    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {\n      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));\n    }\n  }\n  initializeCarousel() {\n    try {\n      const carouselElement = document.getElementById('horizontalCarousel');\n      if (carouselElement) {\n        // Try Bootstrap first\n        if (typeof bootstrap !== 'undefined') {\n          const carousel = new bootstrap.Carousel(carouselElement, {\n            interval: 5000,\n            ride: 'carousel',\n            wrap: true,\n            keyboard: true,\n            pause: 'hover'\n          });\n          console.log('Bootstrap carousel initialized');\n        } else {\n          // Fallback: Manual carousel control\n          this.startManualCarousel();\n          console.log('Manual carousel initialized');\n        }\n      }\n    } catch (error) {\n      console.error('Error initializing carousel:', error);\n      // Fallback to manual carousel\n      this.startManualCarousel();\n    }\n  }\n  startManualCarousel() {\n    // Clear any existing interval\n    if (this.carouselInterval) {\n      clearInterval(this.carouselInterval);\n    }\n    // Start auto-play\n    this.carouselInterval = setInterval(() => {\n      this.nextSlide();\n    }, 5000);\n  }\n  nextSlide() {\n    const totalSlides = this.locationSlides.length;\n    if (totalSlides > 0) {\n      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;\n      this.updateCarouselDisplay();\n    }\n  }\n  prevSlide() {\n    const totalSlides = this.locationSlides.length;\n    if (totalSlides > 0) {\n      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;\n      this.updateCarouselDisplay();\n    }\n  }\n  updateCarouselDisplay() {\n    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');\n    carouselItems.forEach((item, index) => {\n      if (index === this.currentSlideIndex) {\n        item.classList.add('active');\n      } else {\n        item.classList.remove('active');\n      }\n    });\n  }\n  onLocationClick(location) {\n    console.log('Location clicked:', location);\n    // Add your navigation logic here\n    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });\n  }\n  // Method to load locations from API (for future integration)\n  // loadLocations(): void {\n  //   // Replace with actual API call\n  //   // this.locationService.getLocations().subscribe(data => {\n  //   //   this.locations = data;\n  //   //   this.initializeLocationSlides();\n  //   //   // Re-initialize carousel after data loads\n  //   //   setTimeout(() => this.initializeCarousel(), 100);\n  //   // });\n  // }\n  loadMoreLocations() {}\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function HomeComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 300,\n    vars: 6,\n    consts: [[1, \"home-page\"], [1, \"home-header\"], [1, \"navbar\", \"navbar-expand-lg\"], [1, \"container-fluid\", \"px-4\"], [1, \"navbar-brand\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/loading-logo.png\", 1, \"h-40px\", \"app-sidebar-logo-default\"], [\"type\", \"button\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\", \"d-lg-none\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"navbar-nav\", \"mx-auto\", \"d-none\", \"d-lg-flex\"], [1, \"nav-list\", \"d-flex\", \"align-items-center\", \"mb-0\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\"], [\"class\", \"mobile-nav-dropdown d-lg-none\", 4, \"ngIf\"], [1, \"navbar-nav\", \"position-relative\"], [\"class\", \"nav-link user-profile\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [\"href\", \"#\", \"class\", \"nav-link user-link\", 4, \"ngIf\"], [1, \"hero-section\"], [1, \"hero-background\"], [\"src\", \"./assets/media/home/<USER>\", \"alt\", \"Hero Background\", 1, \"hero-bg-image\"], [1, \"hero-overlay\"], [1, \"hero-content\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-12\"], [1, \"hero-text-container\"], [1, \"hero-text-item\"], [1, \"hero-text\"], [1, \"properties-section\"], [1, \"row\"], [1, \"section-title\", \"text-center\", \"mb-5\"], [1, \"row\", \"g-4\"], [1, \"col-lg-3\", \"col-md-6\", \"col-sm-12\"], [1, \"property-card\"], [1, \"property-image\"], [\"src\", \"./assets/media/stock/600x400/img-1.jpg\", \"alt\", \"Property 1\", 1, \"img-fluid\"], [1, \"property-badge\"], [1, \"property-location\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"property-content\"], [1, \"property-title\"], [1, \"property-description\"], [1, \"property-price\"], [1, \"price\"], [1, \"property-rating-actions\"], [1, \"property-rating\"], [1, \"stars\"], [1, \"fas\", \"fa-star\"], [1, \"rating-text\"], [1, \"property-actions\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"far\", \"fa-heart\"], [1, \"fas\", \"fa-share-alt\"], [\"src\", \"./assets/media/stock/600x400/img-15.jpg\", \"alt\", \"Property 2\", 1, \"img-fluid\"], [1, \"property-badge\", \"property-badge-rent\"], [\"src\", \"./assets/media/stock/600x400/img-25.jpg\", \"alt\", \"Property 3\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-35.jpg\", \"alt\", \"Property 4\", 1, \"img-fluid\"], [1, \"horizontal-carousel-section\", \"py-5\"], [1, \"carousel-container\", \"position-relative\"], [\"id\", \"horizontalCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"5000\", 1, \"carousel\", \"slide\"], [1, \"carousel-inner\"], [\"class\", \"carousel-item \", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"data-bs-target\", \"#horizontalCarousel\", \"data-bs-slide\", \"prev\", 1, \"carousel-control-prev\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"carousel-control-prev-icon\"], [1, \"visually-hidden\"], [\"type\", \"button\", \"data-bs-target\", \"#horizontalCarousel\", \"data-bs-slide\", \"next\", 1, \"carousel-control-next\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"carousel-control-next-icon\"], [1, \"row\", \"justify-content-center\", \"mt-5\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-secondary\", \"btn-lg\", 3, \"click\"], [1, \"articles-section\", \"py-5\"], [1, \"row\", \"mb-5\"], [1, \"section-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"left-controls\", \"d-flex\", \"align-items-center\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide\", \"prev\", 1, \"carousel-control-btn\", \"prev-btn\"], [1, \"fas\", \"fa-chevron-left\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide\", \"next\", 1, \"carousel-control-btn\", \"next-btn\", \"ms-2\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"articles-title\", \"text-center\", \"flex-grow-1\"], [1, \"col-lg-2\", \"col-md-3\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"right-link\"], [\"href\", \"#\", 1, \"view-all-link\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\", \"text-success\", \"fs-4\"], [1, \"text-success\", \"fs-4\"], [1, \"green-underline\"], [1, \"col-lg-10\", \"col-md-9\"], [\"id\", \"articlesCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"6000\", 1, \"carousel\", \"slide\"], [1, \"carousel-item\", \"active\"], [1, \"row\", \"g-4\", \"justify-content-center\"], [1, \"col-lg-4\", \"col-md-6\", \"col-sm-8\"], [1, \"article-card\"], [1, \"article-image\"], [\"src\", \"./assets/media/stock/600x400/img-10.jpg\", \"alt\", \"Article 1\", 1, \"img-fluid\"], [1, \"article-overlay\"], [1, \"article-content\"], [\"src\", \"./assets/media/stock/600x400/img-20.jpg\", \"alt\", \"Article 2\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-30.jpg\", \"alt\", \"Article 3\", 1, \"img-fluid\"], [1, \"carousel-item\"], [\"src\", \"./assets/media/stock/600x400/img-40.jpg\", \"alt\", \"Article 4\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-50.jpg\", \"alt\", \"Article 5\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-60.jpg\", \"alt\", \"Article 6\", 1, \"img-fluid\"], [1, \"carousel-indicators\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"0\", 1, \"active\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"1\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"2\"], [1, \"mobile-nav-dropdown\", \"d-lg-none\"], [1, \"dropdown-item\"], [1, \"menu-icon\", \"me-2\"], [1, \"fas\", \"fa-home\", \"fs-6\", \"text-primary\"], [1, \"fas\", \"fa-info-circle\", \"fs-6\", \"text-info\"], [1, \"fas\", \"fa-building\", \"fs-6\", \"text-success\"], [1, \"fas\", \"fa-bullhorn\", \"fs-6\", \"text-warning\"], [1, \"fas\", \"fa-phone\", \"fs-6\", \"text-gray-600\"], [1, \"nav-link\", \"user-profile\", 3, \"click\"], [1, \"user-avatar\", \"me-2\", 3, \"src\", \"alt\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\", \"ms-2\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [\"width\", \"19\", \"height\", \"19\", \"viewBox\", \"0 0 19 19\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"clip-path\", \"url(#clip0_24_2533)\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"], [\"id\", \"clip0_24_2533\"], [\"width\", \"19\", \"height\", \"19\", \"fill\", \"white\"], [\"name\", \"user\", \"type\", \"outline\", 1, \"fs-5\", \"text-primary\"], [\"name\", \"messages\", \"type\", \"outline\", 1, \"fs-5\", \"text-info\"], [1, \"fa-regular\", \"fa-circle-question\", \"fs-6\", \"text-warning\"], [\"name\", \"notification-on\", \"type\", \"outline\", 1, \"fs-5\", \"text-gray-600\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"logout-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"fs-6\", \"text-danger\"], [1, \"dropdown-item\", \"new-request-item\", 3, \"click\"], [1, \"text-success\"], [\"href\", \"#\", 1, \"nav-link\", \"user-link\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"row\", \"justify-content-center\", \"g-2\", \"g-md-3\", \"mt-5\"], [\"class\", \"col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-3\", \"col-sm-4\", \"col-6\"], [1, \"location-card\", 3, \"click\"], [1, \"img-fluid\", 3, \"src\", \"alt\"], [1, \"location-overlay\"], [1, \"location-info\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"nav\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_6_listener() {\n          return ctx.toggleMobileMenu();\n        });\n        i0.ɵɵelement(7, \"span\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\", 10)(11, \"a\", 11);\n        i0.ɵɵtext(12, \" Home \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"li\", 10)(14, \"a\", 11);\n        i0.ɵɵtext(15, \" About EasyDeal \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 10)(17, \"a\", 11);\n        i0.ɵɵtext(18, \" New Projects \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"li\", 10)(20, \"a\", 11);\n        i0.ɵɵtext(21, \" Advertisements \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"li\", 10)(23, \"a\", 11);\n        i0.ɵɵtext(24, \" Contact Us \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(25, HomeComponent_div_25_Template, 26, 0, \"div\", 12);\n        i0.ɵɵelementStart(26, \"div\", 13);\n        i0.ɵɵtemplate(27, HomeComponent_div_27_Template, 5, 3, \"div\", 14)(28, HomeComponent_div_28_Template, 43, 0, \"div\", 15)(29, HomeComponent_a_29_Template, 3, 0, \"a\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18);\n        i0.ɵɵelement(32, \"img\", 19)(33, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 21)(35, \"div\", 22)(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 25)(39, \"div\", 26)(40, \"h2\", 27);\n        i0.ɵɵtext(41, \" Easy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 26)(43, \"h2\", 27);\n        i0.ɵɵtext(44, \" Speed \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 26)(46, \"h2\", 27);\n        i0.ɵɵtext(47, \" Reliability \");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(48, \"section\", 28)(49, \"div\", 22)(50, \"div\", 29)(51, \"div\", 24)(52, \"h2\", 30);\n        i0.ɵɵtext(53, \"Featured Properties\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(54, \"div\", 31)(55, \"div\", 32)(56, \"div\", 33)(57, \"div\", 34);\n        i0.ɵɵelement(58, \"img\", 35);\n        i0.ɵɵelementStart(59, \"div\", 36);\n        i0.ɵɵtext(60, \"For Sale\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"div\", 37);\n        i0.ɵɵelement(62, \"i\", 38);\n        i0.ɵɵelementStart(63, \"span\");\n        i0.ɵɵtext(64, \"New Cairo\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(65, \"div\", 39)(66, \"h4\", 40);\n        i0.ɵɵtext(67, \"Luxury Apartment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"p\", 41);\n        i0.ɵɵtext(69, \"3 Bedrooms \\u2022 2 Bathrooms \\u2022 150 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 42)(71, \"span\", 43);\n        i0.ɵɵtext(72, \"2.5M EGP\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(73, \"div\", 44)(74, \"div\", 45)(75, \"div\", 46);\n        i0.ɵɵelement(76, \"i\", 47)(77, \"i\", 47)(78, \"i\", 47)(79, \"i\", 47)(80, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"span\", 48);\n        i0.ɵɵtext(82, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"div\", 49)(84, \"button\", 50);\n        i0.ɵɵelement(85, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(86, \"button\", 50);\n        i0.ɵɵelement(87, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(88, \"div\", 32)(89, \"div\", 33)(90, \"div\", 34);\n        i0.ɵɵelement(91, \"img\", 53);\n        i0.ɵɵelementStart(92, \"div\", 54);\n        i0.ɵɵtext(93, \"For Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"div\", 37);\n        i0.ɵɵelement(95, \"i\", 38);\n        i0.ɵɵelementStart(96, \"span\");\n        i0.ɵɵtext(97, \"Maadi\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(98, \"div\", 39)(99, \"h4\", 40);\n        i0.ɵɵtext(100, \"Modern Villa\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(101, \"p\", 41);\n        i0.ɵɵtext(102, \"4 Bedrooms \\u2022 3 Bathrooms \\u2022 250 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"div\", 42)(104, \"span\", 43);\n        i0.ɵɵtext(105, \"25K EGP/month\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(106, \"div\", 44)(107, \"div\", 45)(108, \"div\", 46);\n        i0.ɵɵelement(109, \"i\", 47)(110, \"i\", 47)(111, \"i\", 47)(112, \"i\", 47)(113, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"span\", 48);\n        i0.ɵɵtext(115, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(116, \"div\", 49)(117, \"button\", 50);\n        i0.ɵɵelement(118, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"button\", 50);\n        i0.ɵɵelement(120, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(121, \"div\", 32)(122, \"div\", 33)(123, \"div\", 34);\n        i0.ɵɵelement(124, \"img\", 55);\n        i0.ɵɵelementStart(125, \"div\", 36);\n        i0.ɵɵtext(126, \"For Sale\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"div\", 37);\n        i0.ɵɵelement(128, \"i\", 38);\n        i0.ɵɵelementStart(129, \"span\");\n        i0.ɵɵtext(130, \"Zamalek\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(131, \"div\", 39)(132, \"h4\", 40);\n        i0.ɵɵtext(133, \"Penthouse Suite\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(134, \"p\", 41);\n        i0.ɵɵtext(135, \"5 Bedrooms \\u2022 4 Bathrooms \\u2022 300 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(136, \"div\", 42)(137, \"span\", 43);\n        i0.ɵɵtext(138, \"8.5M EGP\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(139, \"div\", 44)(140, \"div\", 45)(141, \"div\", 46);\n        i0.ɵɵelement(142, \"i\", 47)(143, \"i\", 47)(144, \"i\", 47)(145, \"i\", 47)(146, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(147, \"span\", 48);\n        i0.ɵɵtext(148, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(149, \"div\", 49)(150, \"button\", 50);\n        i0.ɵɵelement(151, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(152, \"button\", 50);\n        i0.ɵɵelement(153, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(154, \"div\", 32)(155, \"div\", 33)(156, \"div\", 34);\n        i0.ɵɵelement(157, \"img\", 56);\n        i0.ɵɵelementStart(158, \"div\", 54);\n        i0.ɵɵtext(159, \"For Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"div\", 37);\n        i0.ɵɵelement(161, \"i\", 38);\n        i0.ɵɵelementStart(162, \"span\");\n        i0.ɵɵtext(163, \"Heliopolis\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(164, \"div\", 39)(165, \"h4\", 40);\n        i0.ɵɵtext(166, \"Family House\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(167, \"p\", 41);\n        i0.ɵɵtext(168, \"3 Bedrooms \\u2022 2 Bathrooms \\u2022 180 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(169, \"div\", 42)(170, \"span\", 43);\n        i0.ɵɵtext(171, \"18K EGP/month\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(172, \"div\", 44)(173, \"div\", 45)(174, \"div\", 46);\n        i0.ɵɵelement(175, \"i\", 47)(176, \"i\", 47)(177, \"i\", 47)(178, \"i\", 47)(179, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(180, \"span\", 48);\n        i0.ɵɵtext(181, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(182, \"div\", 49)(183, \"button\", 50);\n        i0.ɵɵelement(184, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(185, \"button\", 50);\n        i0.ɵɵelement(186, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(187, \"section\", 57)(188, \"div\", 22)(189, \"div\", 29)(190, \"div\", 24)(191, \"h2\", 30);\n        i0.ɵɵtext(192, \"Explore Locations\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(193, \"div\", 58)(194, \"div\", 59)(195, \"div\", 60);\n        i0.ɵɵtemplate(196, HomeComponent_div_196_Template, 3, 3, \"div\", 61);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(197, \"button\", 62);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_197_listener() {\n          return ctx.prevSlide();\n        });\n        i0.ɵɵelement(198, \"span\", 63);\n        i0.ɵɵelementStart(199, \"span\", 64);\n        i0.ɵɵtext(200, \"Previous\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(201, \"button\", 65);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_201_listener() {\n          return ctx.nextSlide();\n        });\n        i0.ɵɵelement(202, \"span\", 66);\n        i0.ɵɵelementStart(203, \"span\", 64);\n        i0.ɵɵtext(204, \"Next\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(205, \"div\", 67)(206, \"div\", 68)(207, \"button\", 69);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_207_listener() {\n          return ctx.loadMoreLocations();\n        });\n        i0.ɵɵtext(208, \" Load More Locations \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(209, \"section\", 70)(210, \"div\", 22)(211, \"div\", 71)(212, \"div\", 24)(213, \"div\", 72)(214, \"div\", 73)(215, \"button\", 74);\n        i0.ɵɵelement(216, \"i\", 75);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(217, \"button\", 76);\n        i0.ɵɵelement(218, \"i\", 77);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(219, \"h1\", 78);\n        i0.ɵɵtext(220, \"Articles That Interest You\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(221, \"div\", 29)(222, \"div\", 79)(223, \"div\", 80)(224, \"a\", 81);\n        i0.ɵɵelement(225, \"i\", 82);\n        i0.ɵɵelementStart(226, \"span\", 83);\n        i0.ɵɵtext(227, \"All Articles\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(228, \"div\", 84);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(229, \"div\", 85)(230, \"div\", 86)(231, \"div\", 60)(232, \"div\", 87)(233, \"div\", 88)(234, \"div\", 89)(235, \"div\", 90)(236, \"div\", 91);\n        i0.ɵɵelement(237, \"img\", 92);\n        i0.ɵɵelementStart(238, \"div\", 93)(239, \"div\", 94)(240, \"h4\");\n        i0.ɵɵtext(241, \"Modern Finishing Materials - Shop with the Best\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(242, \"p\");\n        i0.ɵɵtext(243, \"A very quiet area away from the noise and hustle of the city, suitable for large and small families, spacious area with a private garden.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(244, \"div\", 89)(245, \"div\", 90)(246, \"div\", 91);\n        i0.ɵɵelement(247, \"img\", 95);\n        i0.ɵɵelementStart(248, \"div\", 93)(249, \"div\", 94)(250, \"h4\");\n        i0.ɵɵtext(251, \"Invest Your Money with Hotel Property\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(252, \"p\");\n        i0.ɵɵtext(253, \"Excellent investment opportunity in the heart of the city, guaranteed returns and integrated management, strategic location near the airport and commercial centers.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(254, \"div\", 89)(255, \"div\", 90)(256, \"div\", 91);\n        i0.ɵɵelement(257, \"img\", 96);\n        i0.ɵɵelementStart(258, \"div\", 93)(259, \"div\", 94)(260, \"h4\");\n        i0.ɵɵtext(261, \"Villa 10 October April 2019\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(262, \"p\");\n        i0.ɵɵtext(263, \"Latest international finishing materials, high quality and competitive prices, specialized team to implement finishing works to the highest standards.\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(264, \"div\", 97)(265, \"div\", 88)(266, \"div\", 89)(267, \"div\", 90)(268, \"div\", 91);\n        i0.ɵɵelement(269, \"img\", 98);\n        i0.ɵɵelementStart(270, \"div\", 93)(271, \"div\", 94)(272, \"h4\");\n        i0.ɵɵtext(273, \"Apartment in New Cairo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(274, \"p\");\n        i0.ɵɵtext(275, \"Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated facilities, close to universities and international schools.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(276, \"div\", 89)(277, \"div\", 90)(278, \"div\", 91);\n        i0.ɵɵelement(279, \"img\", 99);\n        i0.ɵɵelementStart(280, \"div\", 93)(281, \"div\", 94)(282, \"h4\");\n        i0.ɵɵtext(283, \"North Coast Properties\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(284, \"p\");\n        i0.ɵɵtext(285, \"Residential units directly on the sea, wonderful panoramic view, integrated recreational facilities and suitable for summer investment.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(286, \"div\", 89)(287, \"div\", 90)(288, \"div\", 91);\n        i0.ɵɵelement(289, \"img\", 100);\n        i0.ɵɵelementStart(290, \"div\", 93)(291, \"div\", 94)(292, \"h4\");\n        i0.ɵɵtext(293, \"Administrative Offices Downtown\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(294, \"p\");\n        i0.ɵɵtext(295, \"Modern office spaces in the heart of Cairo, suitable for companies and institutions, parking and integrated service facilities.\");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(296, \"div\", 101);\n        i0.ɵɵelement(297, \"button\", 102)(298, \"button\", 103)(299, \"button\", 104);\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵattribute(\"aria-expanded\", ctx.showMobileMenu);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngIf\", ctx.showMobileMenu);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn && ctx.showUserDropdown);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n        i0.ɵɵadvance(167);\n        i0.ɵɵproperty(\"ngForOf\", ctx.locationSlides);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.KeeniconComponent],\n    styles: [\".home-page[_ngcontent-%COMP%] {\\n  background-color: #ffffff !important;\\n}\\n\\n.home-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  min-height: 120vh;\\n  overflow: hidden;\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 1rem 0;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  gap: 2rem;\\n  margin: 0;\\n  padding: 0;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 1rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  direction: rtl;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  color: #27ae60;\\n  transform: translateY(-2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white !important;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n  color: white !important;\\n  text-decoration: none;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 25px;\\n  padding: 0.5rem 1rem;\\n  border: 1px solid rgba(250, 250, 250, 0.3);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(39, 174, 96, 0.5);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.95rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #27ae60;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\\n  min-width: 220px;\\n  z-index: 1000;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(-3px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  width: 18px;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fff5f5;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\\n  border-top: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #d4f4d4, #e8f5e8);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-weight: 600;\\n  text-align: center;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 0;\\n}\\n@keyframes _ngcontent-%COMP%_dropdownFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: calc(100vh - 80px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  margin-top: 20px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-bg-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]:hover   .hero-bg-image[_ngcontent-%COMP%] {\\n  transform: scale(1.02);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(38, 83, 147, 0.515) 0%, rgba(52, 73, 94, 0.5) 30%, rgba(39, 174, 95, 0.518) 70%, rgba(46, 204, 113, 0.8) 100%);\\n  -webkit-backdrop-filter: blur(2px);\\n          backdrop-filter: blur(2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0 4rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 1s ease-out;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(1) {\\n  text-align: right;\\n  animation-delay: 0.2s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(2) {\\n  text-align: center;\\n  animation-delay: 0.4s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(3) {\\n  text-align: left;\\n  animation-delay: 0.6s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 700;\\n  color: white;\\n  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5), 2px 2px 8px rgba(0, 0, 0, 0.7), 4px 4px 15px rgba(0, 0, 0, 0.4);\\n  margin: 0;\\n  padding: 1.5rem 2.5rem;\\n  border-radius: 20px;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);\\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.6s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px) scale(1.08);\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 10px 30px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.4);\\n  text-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 2px 2px 10px rgba(0, 0, 0, 0.8), 4px 4px 20px rgba(0, 0, 0, 0.5);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 20px;\\n    margin-right: 20px;\\n    border-radius: 20px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    padding: 0 2rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n    padding: 1.2rem 2rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.5rem 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    right: -15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 15px;\\n    margin-right: 15px;\\n    margin-top: 15px;\\n    border-radius: 15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n    padding: 0 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n    text-align: center !important;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    padding: 1rem 1.5rem;\\n    border-radius: 15px;\\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n    height: 40px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 10px;\\n    margin-right: 10px;\\n    margin-top: 10px;\\n    border-radius: 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n    padding: 0.8rem 1.2rem;\\n    border-radius: 12px;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n.navbar-toggler[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.1);\\n}\\n.navbar-toggler[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n\\n.mobile-nav-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% - 1.8rem) !important;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  margin-left: -25px !important;\\n  width: 200px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  z-index: 99;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n  margin-top: 0;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  color: #333;\\n  text-decoration: none;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.05);\\n  color: #007bff;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 18px;\\n  height: 18px;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 576px) {\\n  .mobile-nav-dropdown[_ngcontent-%COMP%] {\\n    width: 180px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 6px 10px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px !important;\\n    height: 16px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.7rem !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.75rem !important;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    justify-content: space-between !important;\\n    align-items: center !important;\\n    flex-wrap: nowrap !important;\\n    width: 100% !important;\\n    padding: 0.5rem 1rem !important;\\n    min-height: auto !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 35px !important;\\n    width: auto !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n    padding: 0.25rem 0.5rem !important;\\n    border: none !important;\\n    background: transparent !important;\\n    order: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav.position-relative[_ngcontent-%COMP%] {\\n    position: relative !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem !important;\\n    font-size: 0.85rem !important;\\n    white-space: nowrap !important;\\n    display: flex !important;\\n    align-items: center !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 28px !important;\\n    height: 28px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: inline !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .fas.fa-chevron-down[_ngcontent-%COMP%] {\\n    font-size: 0.7rem !important;\\n    margin-left: 0.25rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem !important;\\n    font-size: 0.85rem !important;\\n    white-space: nowrap !important;\\n    display: flex !important;\\n    align-items: center !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 0.25rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav.mx-auto[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.75rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 30px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 24px !important;\\n    height: 24px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 0 !important;\\n    font-size: 1rem !important;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    flex-direction: row !important;\\n    justify-content: space-between !important;\\n    align-items: center !important;\\n    flex-wrap: nowrap !important;\\n    overflow: visible !important;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]    > .container-fluid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    flex-shrink: 0 !important;\\n    white-space: nowrap !important;\\n  }\\n}\\n@media (max-width: 319px) {\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n.properties-section[_ngcontent-%COMP%] {\\n  padding: 50px 0;\\n}\\n.properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 4rem;\\n  position: relative;\\n}\\n.properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -15px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #3498db, #2980b9);\\n  border-radius: 2px;\\n}\\n\\n.property-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  height: 100%;\\n  margin-top: 30px;\\n  cursor: pointer;\\n}\\n.property-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  left: 15px;\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  padding: 5px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge.property-badge-rent[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c, #c0392b);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 15px;\\n  left: 15px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 8px;\\n  line-height: 1.3;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-description[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n  margin-bottom: 15px;\\n  line-height: 1.5;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  color: #3498db;\\n  background: linear-gradient(135deg, #3498db, #2980b9);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  font-size: 0.9rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  color: #7f8c8d;\\n  transition: all 0.3s ease;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3498db;\\n  color: #3498db;\\n  background: rgba(52, 152, 219, 0.1);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  flex: 1;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n}\\n\\n@media (max-width: 768px) {\\n  .properties-section[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n  .property-card[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .properties-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n    margin-bottom: 1.5rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 4px 8px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 4px 8px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%] {\\n  background-color: #F8F8F8;\\n  margin-top: 90px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 3rem;\\n  position: relative;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -15px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #007bff, #0056b3);\\n  border-radius: 2px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n  margin: 0 30px;\\n  margin-top: 30px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 200px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover   .location-overlay[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  padding: 15px;\\n  transition: all 0.3s ease;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  color: white;\\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin: 0;\\n  opacity: 0.9;\\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n  color: #ffd700;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 50px !important;\\n  height: 50px !important;\\n  background: #031752;\\n  border: none;\\n  border-radius: 50%;\\n  opacity: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]:hover, \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-size: 20px 20px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n  left: 1px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  right: 1px;\\n}\\n\\n@media (min-width: 1400px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 50px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n}\\n@media (min-width: 992px) and (max-width: 1399px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 40px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n}\\n@media (min-width: 768px) and (max-width: 991px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2.2rem;\\n    margin-bottom: 2.5rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 30px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -5px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -5px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    color: white;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n@media (min-width: 576px) and (max-width: 767px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n    color: white;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n@media (max-width: 575px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n    margin-bottom: 1.5rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    background-size: 16px 16px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 140px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n    color: white;\\n    margin-bottom: 3px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 3px;\\n  }\\n}\\n@media (max-width: 400px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n}\\n.articles-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 100px 0;\\n  margin-top: 80px;\\n  margin-bottom: 50px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #1e3a8a;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.25);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.35);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n  font-size: 2.8rem;\\n  font-weight: 800;\\n  color: #1e3a8a;\\n  text-shadow: 2px 2px 4px rgba(30, 58, 138, 0.1);\\n  margin: 0;\\n  letter-spacing: -0.5px;\\n  direction: ltr;\\n  text-align: center;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  position: relative;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n  direction: ltr;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   .green-underline[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 100%;\\n  height: 3px;\\n  background: #28a745;\\n  border-radius: 2px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover {\\n  transform: translateX(-5px);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   .text-success[_ngcontent-%COMP%] {\\n  color: #1e7e34 !important;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   .green-underline[_ngcontent-%COMP%] {\\n  background: #1e7e34;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(-3px);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  cursor: pointer;\\n  height: 400px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.03);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover   .article-overlay[_ngcontent-%COMP%] {\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));\\n  color: white;\\n  padding: 40px 25px 25px;\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%] {\\n  direction: ltr;\\n  text-align: left;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  margin-bottom: 12px;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  line-height: 1.3;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n  opacity: 0.95;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n  line-height: 1.6;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 50px;\\n  height: 50px;\\n  background: #1e3a8a;\\n  border: none;\\n  border-radius: 50%;\\n  opacity: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]:hover, \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-size: 20px 20px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n  left: -25px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  right: -25px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n  bottom: -50px;\\n  margin-bottom: 0;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(30, 58, 138, 0.3);\\n  margin: 0 6px;\\n  transition: all 0.3s ease;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background: #1e3a8a;\\n  transform: scale(1.2);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: scale(1.1);\\n}\\n\\n@media (max-width: 1200px) {\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n    gap: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n    margin-bottom: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    top: 45%;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    background-size: 16px 16px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -40px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n    margin: 0 4px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.6rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-bottom: 8px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    line-height: 1.4;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n    background-size: 14px 14px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 8px;\\n    height: 8px;\\n    margin: 0 3px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "HomeComponent_div_27_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleUserDropdown", "ɵɵadvance", "ɵɵproperty", "getUserProfileImage", "ɵɵsanitizeUrl", "getUserDisplayName", "ɵɵtextInterpolate", "HomeComponent_div_28_Template_div_click_1_listener", "_r3", "closeUserDropdown", "HomeComponent_div_28_Template_div_click_13_listener", "HomeComponent_div_28_Template_div_click_18_listener", "HomeComponent_div_28_Template_div_click_23_listener", "HomeComponent_div_28_Template_div_click_28_listener", "HomeComponent_div_28_Template_div_click_34_listener", "logout", "HomeComponent_div_28_Template_div_click_40_listener", "HomeComponent_div_196_div_2_Template_div_click_1_listener", "location_r5", "_r4", "$implicit", "onLocationClick", "image", "name", "ɵɵtextInterpolate1", "propertyCount", "ɵɵtemplate", "HomeComponent_div_196_div_2_Template", "ɵɵclassProp", "i_r7", "slide_r6", "HomeComponent", "authService", "currentUser", "isLoggedIn", "showUserDropdown", "showMobileMenu", "locationSlides", "currentSlideIndex", "carouselI<PERSON>val", "locations", "id", "constructor", "ngOnInit", "checkUserSession", "initializeLocationSlides", "ngAfterViewInit", "setTimeout", "initializeCarousel", "ngOnDestroy", "clearInterval", "authToken", "localStorage", "getItem", "JSON", "parse", "error", "fullName", "removeItem", "toggleMobileMenu", "closeMobileMenu", "onDocumentClick", "event", "target", "userProfile", "closest", "userDropdown", "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "mobileNavDropdown", "itemsPerSlide", "i", "length", "push", "slice", "carouselElement", "document", "getElementById", "bootstrap", "carousel", "Carousel", "interval", "ride", "wrap", "keyboard", "pause", "console", "log", "startManualCarousel", "setInterval", "nextSlide", "totalSlides", "updateCarouselDisplay", "prevSlide", "carouselItems", "querySelectorAll", "for<PERSON>ach", "item", "index", "classList", "add", "remove", "location", "loadMoreLocations", "ɵɵdirectiveInject", "i1", "AuthenticationService", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "HomeComponent_Template_button_click_6_listener", "HomeComponent_div_25_Template", "HomeComponent_div_27_Template", "HomeComponent_div_28_Template", "HomeComponent_a_29_Template", "HomeComponent_div_196_Template", "HomeComponent_Template_button_click_197_listener", "HomeComponent_Template_button_click_201_listener", "HomeComponent_Template_button_click_207_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener, After<PERSON>iewInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\r\nimport { AuthenticationService } from '../authentication/services/authentication.service';\r\n\r\ndeclare var bootstrap: any;\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  currentUser: any = null;\r\n  isLoggedIn: boolean = false;\r\n  showUserDropdown: boolean = false;\r\n  showMobileMenu: boolean = false;\r\n\r\n  // Location Carousel Data\r\n  locationSlides: any[][] = [];\r\n  currentSlideIndex: number = 0;\r\n  carouselInterval: any;\r\n  locations: any[] = [\r\n    {\r\n      id: 1,\r\n      name: ' New Cairo',\r\n      image: './assets/media/stock/600x400/img-10.jpg',\r\n      propertyCount: 2341\r\n    },\r\n    {\r\n      id: 2,\r\n      name: '  <PERSON><PERSON>',\r\n      image: './assets/media/stock/600x400/img-20.jpg',\r\n      propertyCount: 1234\r\n    },\r\n    {\r\n      id: 3,\r\n      name: ' <PERSON>  ',\r\n      image: './assets/media/stock/600x400/img-30.jpg',\r\n      propertyCount: 3421\r\n    },\r\n    {\r\n      id: 4,\r\n      name: '   Heliopolis',\r\n      image: './assets/media/stock/600x400/img-40.jpg',\r\n      propertyCount: 2341\r\n    },\r\n    {\r\n      id: 5,\r\n      name: '   Nasr City',\r\n      image: './assets/media/stock/600x400/img-50.jpg',\r\n      propertyCount: 987\r\n    },\r\n    {\r\n      id: 6,\r\n      name: '  6 October',\r\n      image: './assets/media/stock/600x400/img-60.jpg',\r\n      propertyCount: 1543\r\n    },\r\n    {\r\n      id: 7,\r\n      name: '  Maadi',\r\n      image: './assets/media/stock/600x400/img-70.jpg',\r\n      propertyCount: 876\r\n    },\r\n    {\r\n      id: 8,\r\n      name: '  Zamalek',\r\n      image: './assets/media/stock/600x400/img-80.jpg',\r\n      propertyCount: 654\r\n    },\r\n    {\r\n      id: 9,\r\n      name: '  New Cairo',\r\n      image: './assets/media/stock/600x400/img-90.jpg',\r\n      propertyCount: 1098\r\n    },\r\n    {\r\n      id: 10,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    },\r\n    {\r\n      id: 11,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    },\r\n    {\r\n      id: 12,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    }\r\n  ];\r\n\r\n  constructor(private authService: AuthenticationService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.checkUserSession();\r\n    this.initializeLocationSlides();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Initialize Bootstrap carousel after view is loaded\r\n    setTimeout(() => {\r\n      this.initializeCarousel();\r\n    }, 100);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up interval when component is destroyed\r\n    if (this.carouselInterval) {\r\n      clearInterval(this.carouselInterval);\r\n    }\r\n  }\r\n\r\n  checkUserSession(): void {\r\n    // Check if user is logged in by checking localStorage\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      try {\r\n        this.currentUser = JSON.parse(currentUser);\r\n        this.isLoggedIn = true;\r\n      } catch (error) {\r\n        // If parsing fails, user is not logged in\r\n        this.isLoggedIn = false;\r\n        this.currentUser = null;\r\n      }\r\n    } else {\r\n      this.isLoggedIn = false;\r\n      this.currentUser = null;\r\n    }\r\n  }\r\n\r\n  getUserDisplayName(): string {\r\n    if (this.currentUser) {\r\n      return this.currentUser.fullName  || 'User';\r\n    }\r\n    return 'Guest';\r\n  }\r\n\r\n  getUserProfileImage(): string {\r\n    if (this.currentUser && this.currentUser.image) {\r\n      return this.currentUser.image;\r\n    }\r\n    // Return default avatar if no profile image\r\n    return 'assets/media/avatars/blank.png';\r\n  }\r\n\r\n  toggleUserDropdown(): void {\r\n    this.showUserDropdown = !this.showUserDropdown;\r\n  }\r\n\r\n  closeUserDropdown(): void {\r\n    this.showUserDropdown = false;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    localStorage.removeItem('currentUser');\r\n    this.isLoggedIn = false;\r\n    this.currentUser = null;\r\n    this.showUserDropdown = false;\r\n    // Optionally redirect to login page\r\n    // this.router.navigate(['/authentication/login']);\r\n  }\r\n\r\n  toggleMobileMenu(): void {\r\n    this.showMobileMenu = !this.showMobileMenu;\r\n    // Close user dropdown when mobile menu is toggled\r\n    if (this.showMobileMenu) {\r\n      this.showUserDropdown = false;\r\n    }\r\n  }\r\n\r\n  closeMobileMenu(): void {\r\n    this.showMobileMenu = false;\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const userProfile = target.closest('.user-profile');\r\n    const userDropdown = target.closest('.user-dropdown');\r\n    const navbarToggler = target.closest('.navbar-toggler');\r\n    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');\r\n\r\n    // Close user dropdown if clicked outside of user profile and dropdown\r\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\r\n      this.showUserDropdown = false;\r\n    }\r\n\r\n    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown\r\n    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {\r\n      this.showMobileMenu = false;\r\n    }\r\n  }\r\n\r\n  // Location Carousel Methods\r\n  initializeLocationSlides(): void {\r\n    // Split locations into slides of 5 items each\r\n    const itemsPerSlide = 5;\r\n    this.locationSlides = [];\r\n\r\n    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {\r\n      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));\r\n    }\r\n  }\r\n\r\n  initializeCarousel(): void {\r\n    try {\r\n      const carouselElement = document.getElementById('horizontalCarousel');\r\n      if (carouselElement) {\r\n        // Try Bootstrap first\r\n        if (typeof bootstrap !== 'undefined') {\r\n          const carousel = new bootstrap.Carousel(carouselElement, {\r\n            interval: 5000,\r\n            ride: 'carousel',\r\n            wrap: true,\r\n            keyboard: true,\r\n            pause: 'hover'\r\n          });\r\n          console.log('Bootstrap carousel initialized');\r\n        } else {\r\n          // Fallback: Manual carousel control\r\n          this.startManualCarousel();\r\n          console.log('Manual carousel initialized');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error initializing carousel:', error);\r\n      // Fallback to manual carousel\r\n      this.startManualCarousel();\r\n    }\r\n  }\r\n\r\n  startManualCarousel(): void {\r\n    // Clear any existing interval\r\n    if (this.carouselInterval) {\r\n      clearInterval(this.carouselInterval);\r\n    }\r\n\r\n    // Start auto-play\r\n    this.carouselInterval = setInterval(() => {\r\n      this.nextSlide();\r\n    }, 5000);\r\n  }\r\n\r\n  nextSlide(): void {\r\n    const totalSlides = this.locationSlides.length;\r\n    if (totalSlides > 0) {\r\n      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;\r\n      this.updateCarouselDisplay();\r\n    }\r\n  }\r\n\r\n  prevSlide(): void {\r\n    const totalSlides = this.locationSlides.length;\r\n    if (totalSlides > 0) {\r\n      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;\r\n      this.updateCarouselDisplay();\r\n    }\r\n  }\r\n\r\n  updateCarouselDisplay(): void {\r\n    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');\r\n    carouselItems.forEach((item, index) => {\r\n      if (index === this.currentSlideIndex) {\r\n        item.classList.add('active');\r\n      } else {\r\n        item.classList.remove('active');\r\n      }\r\n    });\r\n  }\r\n\r\n  onLocationClick(location: any): void {\r\n    console.log('Location clicked:', location);\r\n    // Add your navigation logic here\r\n    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });\r\n  }\r\n\r\n  // Method to load locations from API (for future integration)\r\n  // loadLocations(): void {\r\n  //   // Replace with actual API call\r\n  //   // this.locationService.getLocations().subscribe(data => {\r\n  //   //   this.locations = data;\r\n  //   //   this.initializeLocationSlides();\r\n  //   //   // Re-initialize carousel after data loads\r\n  //   //   setTimeout(() => this.initializeCarousel(), 100);\r\n  //   // });\r\n  // }\r\n\r\n  loadMoreLocations(){\r\n\r\n  }\r\n\r\n}\r\n", "<div class=\"home-page\">\r\n\r\n  <!-- Header Section -->\r\n  <header class=\"home-header\">\r\n    <!-- Navigation Bar -->\r\n    <nav class=\"navbar navbar-expand-lg\">\r\n      <div class=\"container-fluid px-4\">\r\n        <!-- Logo -->\r\n        <div class=\"navbar-brand\">\r\n          <img alt=\"Logo\" src=\"./assets/media/easydeallogos/loading-logo.png\" class=\"h-40px app-sidebar-logo-default\" />\r\n        </div>\r\n\r\n        <!-- Mobile Menu Toggle Button -->\r\n        <button class=\"navbar-toggler d-lg-none\" type=\"button\" (click)=\"toggleMobileMenu()\"\r\n          [attr.aria-expanded]=\"showMobileMenu\" aria-label=\"Toggle navigation\">\r\n          <span class=\"navbar-toggler-icon\">\r\n          </span>\r\n        </button>\r\n\r\n        <!-- Navigation Menu -->\r\n        <div class=\"navbar-nav mx-auto d-none d-lg-flex\">\r\n          <ul class=\"nav-list d-flex align-items-center mb-0\">\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Home </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> About EasyDeal </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> New Projects </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Advertisements </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Contact Us </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <!-- Mobile Navigation Dropdown -->\r\n        <div *ngIf=\"showMobileMenu\" class=\"mobile-nav-dropdown d-lg-none\">\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-home fs-6 text-primary\"></i>\r\n            </span>\r\n            <span>Home</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-info-circle fs-6 text-info\"></i>\r\n            </span>\r\n            <span>About EasyDeal</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-building fs-6 text-success\"></i>\r\n            </span>\r\n            <span>New Projects</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-bullhorn fs-6 text-warning\"></i>\r\n            </span>\r\n            <span>Advertisements</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-phone fs-6 text-gray-600\"></i>\r\n            </span>\r\n            <span>Contact Us</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- User Registration Link / User Profile -->\r\n        <div class=\"navbar-nav position-relative\">\r\n          <!-- If user is logged in, show user profile -->\r\n          <div *ngIf=\"isLoggedIn\" class=\"nav-link user-profile\" (click)=\"toggleUserDropdown()\">\r\n            <img [src]=\"getUserProfileImage()\" [alt]=\"getUserDisplayName()\" class=\"user-avatar me-2\">\r\n            <span class=\"user-name\">{{ getUserDisplayName() }}</span>\r\n            <i class=\"fas fa-chevron-down ms-2\"></i>\r\n          </div>\r\n\r\n          <!-- User Dropdown Menu -->\r\n          <div *ngIf=\"isLoggedIn && showUserDropdown\" class=\"user-dropdown\">\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <svg width=\"19\" height=\"19\" viewBox=\"0 0 19 19\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <g clip-path=\"url(#clip0_24_2533)\">\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\" />\r\n                  </g>\r\n                  <defs>\r\n                    <clipPath id=\"clip0_24_2533\">\r\n                      <rect width=\"19\" height=\"19\" fill=\"white\" />\r\n                    </clipPath>\r\n                  </defs>\r\n                </svg>\r\n              </span>\r\n              <span>Requests</span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"user\" class=\"fs-5 text-primary\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> My Profile </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"messages\" class=\"fs-5 text-info\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Messages </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fa-regular fa-circle-question fs-6 text-warning\"></i>\r\n              </span>\r\n              <span> Help </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"notification-on\" class=\"fs-5 text-gray-600\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Notifications </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item logout-item\" (click)=\"logout()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fas fa-sign-out-alt fs-6 text-danger\"></i>\r\n              </span>\r\n              <span> Logout </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item new-request-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"text-success\"> New Request </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- If user is not logged in, show register button -->\r\n          <a *ngIf=\"!isLoggedIn\" href=\"#\" class=\"nav-link user-link\">\r\n            <i class=\"fas fa-user me-2\"></i>\r\n            Register Guest\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n\r\n    <!-- Hero Section -->\r\n    <div class=\"hero-section\">\r\n      <div class=\"hero-background\">\r\n        <img\r\n          src=\"./assets/media/home/<USER>\"\r\n          alt=\"Hero Background\" class=\"hero-bg-image\">\r\n        <div class=\"hero-overlay\"></div>\r\n      </div>\r\n\r\n      <div class=\"hero-content\">\r\n        <div class=\"container\">\r\n          <div class=\"row justify-content-center\">\r\n            <div class=\"col-12\">\r\n              <div class=\"hero-text-container\">\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Easy</h2>\r\n                </div>\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Speed </h2>\r\n                </div>\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Reliability </h2>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </header>\r\n\r\n  <!-- Properties Section -->\r\n  <section class=\"properties-section\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <h2 class=\"section-title text-center mb-5\">Featured Properties</h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row g-4\">\r\n        <!-- Property Card 1 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-1.jpg\" alt=\"Property 1\" class=\"img-fluid\">\r\n              <div class=\"property-badge\">For Sale</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>New Cairo</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Luxury Apartment</h4>\r\n              <p class=\"property-description\">3 Bedrooms • 2 Bathrooms • 150 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">2.5M EGP</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 2 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-15.jpg\" alt=\"Property 2\" class=\"img-fluid\">\r\n              <div class=\"property-badge property-badge-rent\">For Rent</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Maadi</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Modern Villa</h4>\r\n              <p class=\"property-description\">4 Bedrooms • 3 Bathrooms • 250 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">25K EGP/month</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 3 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-25.jpg\" alt=\"Property 3\" class=\"img-fluid\">\r\n              <div class=\"property-badge\">For Sale</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Zamalek</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Penthouse Suite</h4>\r\n              <p class=\"property-description\">5 Bedrooms • 4 Bathrooms • 300 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">8.5M EGP</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 4 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-35.jpg\" alt=\"Property 4\" class=\"img-fluid\">\r\n              <div class=\"property-badge property-badge-rent\">For Rent</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Heliopolis</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Family House</h4>\r\n              <p class=\"property-description\">3 Bedrooms • 2 Bathrooms • 180 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">18K EGP/month</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Explore Locations Section -->\r\n  <section class=\"horizontal-carousel-section py-5\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <h2 class=\"section-title text-center mb-5\">Explore Locations</h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"carousel-container position-relative\">\r\n        <!-- Bootstrap Carousel -->\r\n        <div id=\"horizontalCarousel\" class=\"carousel slide\" data-bs-ride=\"carousel\" data-bs-interval=\"5000\">\r\n          <div class=\"carousel-inner\">\r\n            <!-- Dynamic Slides -->\r\n            <div class=\"carousel-item \" *ngFor=\"let slide of locationSlides; let i = index\" [class.active]=\"i === 0\">\r\n              <div class=\"row justify-content-center g-2 g-md-3 mt-5\">\r\n                <div class=\"col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6\" *ngFor=\"let location of slide\">\r\n                  <div class=\"location-card\" (click)=\"onLocationClick(location)\">\r\n                    <img [src]=\"location.image || 'assets/media/auth/404-error.png'\" [alt]=\"location.name\"\r\n                      class=\"img-fluid\">\r\n                    <div class=\"location-overlay\">\r\n                      <div class=\"location-info\">\r\n                        <h5>{{ location.name }}</h5>\r\n                        <p><i class=\"fas fa-map-marker-alt\"></i> {{ location.propertyCount }} Properties Available</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Carousel Controls -->\r\n          <button class=\"carousel-control-prev\" type=\"button\" data-bs-target=\"#horizontalCarousel\" data-bs-slide=\"prev\"\r\n            (click)=\"prevSlide()\">\r\n            <span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"></span>\r\n            <span class=\"visually-hidden\">Previous</span>\r\n          </button>\r\n          <button class=\"carousel-control-next\" type=\"button\" data-bs-target=\"#horizontalCarousel\" data-bs-slide=\"next\"\r\n            (click)=\"nextSlide()\">\r\n            <span class=\"carousel-control-next-icon\" aria-hidden=\"true\"></span>\r\n            <span class=\"visually-hidden\">Next</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row justify-content-center mt-5\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-secondary btn-lg\" (click)=\"loadMoreLocations()\">\r\n            Load More Locations\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Articles Section -->\r\n  <section class=\"articles-section py-5\">\r\n    <div class=\"container\">\r\n      <!-- Section Header -->\r\n      <div class=\"row mb-5\">\r\n        <div class=\"col-12\">\r\n          <div class=\"section-header d-flex justify-content-between align-items-center\">\r\n            <div class=\"left-controls d-flex align-items-center\">\r\n              <button class=\"carousel-control-btn prev-btn\" type=\"button\" data-bs-target=\"#articlesCarousel\"\r\n                data-bs-slide=\"prev\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n              <button class=\"carousel-control-btn next-btn ms-2\" type=\"button\" data-bs-target=\"#articlesCarousel\"\r\n                data-bs-slide=\"next\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <h1 class=\"articles-title text-center flex-grow-1\">Articles That Interest You</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Bootstrap Carousel for Articles with All Articles Link -->\r\n      <div class=\"row\">\r\n        <!-- All Articles Link on the Left -->\r\n        <div class=\"col-lg-2 col-md-3 d-flex align-items-center justify-content-center\">\r\n          <div class=\"right-link\">\r\n            <a href=\"#\" class=\"view-all-link\">\r\n              <i class=\"fas fa-arrow-left me-2 text-success fs-4\"></i>\r\n              <span class=\"text-success fs-4\">All Articles</span>\r\n              <div class=\"green-underline\"></div>\r\n            </a>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Carousel on the Right -->\r\n        <div class=\"col-lg-10 col-md-9\">\r\n          <div id=\"articlesCarousel\" class=\"carousel slide\" data-bs-ride=\"carousel\" data-bs-interval=\"6000\">\r\n            <div class=\"carousel-inner\">\r\n              <!-- Slide 1 -->\r\n              <div class=\"carousel-item active\">\r\n                <div class=\"row g-4 justify-content-center\">\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-10.jpg\" alt=\"Article 1\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Modern Finishing Materials - Shop with the Best</h4>\r\n                            <p>A very quiet area away from the noise and hustle of the city, suitable for large and\r\n                              small\r\n                              families, spacious area with a private garden.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-20.jpg\" alt=\"Article 2\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Invest Your Money with Hotel Property</h4>\r\n                            <p>Excellent investment opportunity in the heart of the city, guaranteed returns and\r\n                              integrated\r\n                              management, strategic location near the airport and commercial centers.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-30.jpg\" alt=\"Article 3\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Villa 10 October April 2019</h4>\r\n                            <p>Latest international finishing materials, high quality and competitive prices,\r\n                              specialized\r\n                              team to implement finishing works to the highest standards.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Slide 2 -->\r\n              <div class=\"carousel-item\">\r\n                <div class=\"row g-4 justify-content-center\">\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-40.jpg\" alt=\"Article 4\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Apartment in New Cairo</h4>\r\n                            <p>Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated\r\n                              facilities, close to universities and international schools.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-50.jpg\" alt=\"Article 5\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>North Coast Properties</h4>\r\n                            <p>Residential units directly on the sea, wonderful panoramic view, integrated recreational\r\n                              facilities and suitable for summer investment.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-60.jpg\" alt=\"Article 6\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Administrative Offices Downtown</h4>\r\n                            <p>Modern office spaces in the heart of Cairo, suitable for companies and institutions,\r\n                              parking\r\n                              and integrated service facilities.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Carousel Indicators -->\r\n            <div class=\"carousel-indicators\">\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"0\" class=\"active\"></button>\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"1\"></button>\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"2\"></button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n</div>"], "mappings": ";;;;;;IC2CYA,EAFJ,CAAAC,cAAA,eAAkE,eACrC,gBACI;IAC3BD,EAAA,CAAAE,SAAA,aAA6C;IAC/CF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,WAAI;IACZJ,EADY,CAAAG,YAAA,EAAO,EACb;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACI;IAC3BD,EAAA,CAAAE,SAAA,aAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACtBJ,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACtBJ,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IAEpBJ,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;;;;;;IAKJH,EAAA,CAAAC,cAAA,eAAqF;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,mDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAClFZ,EAAA,CAAAE,SAAA,eAAyF;IACzFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAE,SAAA,aAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;IAHCH,EAAA,CAAAa,SAAA,EAA6B;IAACb,EAA9B,CAAAc,UAAA,QAAAL,MAAA,CAAAM,mBAAA,IAAAf,EAAA,CAAAgB,aAAA,CAA6B,QAAAP,MAAA,CAAAQ,kBAAA,GAA6B;IACvCjB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAQ,kBAAA,GAA0B;;;;;;IAMlDjB,EADF,CAAAC,cAAA,eAAkE,eACP;IAA9BD,EAAA,CAAAK,UAAA,mBAAAc,mDAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;;IAEzBD,EADF,CAAAC,cAAA,eAA+F,aAC1D;IAKjCD,EAJA,CAAAE,SAAA,gBACmN,gBAEuZ,gBAE4O;IACx1BF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,WAAM,oBACyB;IAC3BD,EAAA,CAAAE,SAAA,iBAA4C;IAIpDF,EAHM,CAAAG,YAAA,EAAW,EACN,EACH,EACD;;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAChBJ,EADgB,CAAAG,YAAA,EAAO,EACjB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAiB,oDAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAAkF;IACpFF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,oBAAW;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAkB,oDAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAAmF;IACrFF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,kBAAS;IAClBJ,EADkB,CAAAG,YAAA,EAAO,EACnB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAmB,oDAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA+D;IACjEF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,cAAK;IACdJ,EADc,CAAAG,YAAA,EAAO,EACf;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAoB,oDAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAA8F;IAChGF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,uBAAc;IACvBJ,EADuB,CAAAG,YAAA,EAAO,EACxB;IACNH,EAAA,CAAAE,SAAA,gBAAoC;IACpCF,EAAA,CAAAC,cAAA,gBAA0D;IAAnBD,EAAA,CAAAK,UAAA,mBAAAqB,oDAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IACvD3B,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAAoD;IACtDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,gBAAO;IAChBJ,EADgB,CAAAG,YAAA,EAAO,EACjB;IACNH,EAAA,CAAAE,SAAA,gBAAoC;IACpCF,EAAA,CAAAC,cAAA,gBAA0E;IAA9BD,EAAA,CAAAK,UAAA,mBAAAuB,oDAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACvErB,EAAA,CAAAC,cAAA,iBAA2B;IAACD,EAAA,CAAAI,MAAA,qBAAY;IAE5CJ,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAsOIH,EADF,CAAAC,cAAA,eAAsF,eACrB;IAApCD,EAAA,CAAAK,UAAA,mBAAAwB,0DAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA,EAAAC,SAAA;MAAA,MAAAvB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwB,eAAA,CAAAH,WAAA,CAAyB;IAAA,EAAC;IAC5D9B,EAAA,CAAAE,SAAA,eACoB;IAGhBF,EAFJ,CAAAC,cAAA,eAA8B,eACD,SACrB;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,SAAA,YAAqC;IAACF,EAAA,CAAAI,MAAA,GAAiD;IAIlGJ,EAJkG,CAAAG,YAAA,EAAI,EAC1F,EACF,EACF,EACF;;;;IATGH,EAAA,CAAAa,SAAA,GAA2D;IAACb,EAA5D,CAAAc,UAAA,QAAAgB,WAAA,CAAAI,KAAA,uCAAAlC,EAAA,CAAAgB,aAAA,CAA2D,QAAAc,WAAA,CAAAK,IAAA,CAAsB;IAI9EnC,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAkB,iBAAA,CAAAY,WAAA,CAAAK,IAAA,CAAmB;IACkBnC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAoC,kBAAA,MAAAN,WAAA,CAAAO,aAAA,0BAAiD;;;;;IARpGrC,EADF,CAAAC,cAAA,cAAyG,eAC/C;IACtDD,EAAA,CAAAsC,UAAA,IAAAC,oCAAA,oBAAsF;IAa1FvC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAf0EH,EAAA,CAAAwC,WAAA,WAAAC,IAAA,OAAwB;IAExBzC,EAAA,CAAAa,SAAA,GAAQ;IAARb,EAAA,CAAAc,UAAA,YAAA4B,QAAA,CAAQ;;;AD7WpG,OAAM,MAAOC,aAAa;EAqFJC,WAAA;EApFpBC,WAAW,GAAQ,IAAI;EACvBC,UAAU,GAAY,KAAK;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,cAAc,GAAY,KAAK;EAE/B;EACAC,cAAc,GAAY,EAAE;EAC5BC,iBAAiB,GAAW,CAAC;EAC7BC,gBAAgB;EAChBC,SAAS,GAAU,CACjB;IACEC,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,YAAY;IAClBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,SAAS;IACfD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,iBAAiB;IACvBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,eAAe;IACrBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,cAAc;IACpBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,SAAS;IACfD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,WAAW;IACjBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,CACF;EAEDiB,YAAoBV,WAAkC;IAAlC,KAAAA,WAAW,GAAXA,WAAW;EAA2B;EAE1DW,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACV,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACtC;EACF;EAEAK,gBAAgBA,CAAA;IACd;IACA,MAAMO,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMpB,WAAW,GAAGmB,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,SAAS,IAAIlB,WAAW,EAAE;MAC5B,IAAI;QACF,IAAI,CAACA,WAAW,GAAGqB,IAAI,CAACC,KAAK,CAACtB,WAAW,CAAC;QAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACd;QACA,IAAI,CAACtB,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACzB;EACF;EAEA5B,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC4B,WAAW,EAAE;MACpB,OAAO,IAAI,CAACA,WAAW,CAACwB,QAAQ,IAAK,MAAM;IAC7C;IACA,OAAO,OAAO;EAChB;EAEAtD,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC8B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACX,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACW,WAAW,CAACX,KAAK;IAC/B;IACA;IACA,OAAO,gCAAgC;EACzC;EAEAtB,kBAAkBA,CAAA;IAChB,IAAI,CAACmC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA1B,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,gBAAgB,GAAG,KAAK;EAC/B;EAEApB,MAAMA,CAAA;IACJqC,YAAY,CAACM,UAAU,CAAC,WAAW,CAAC;IACpCN,YAAY,CAACM,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACxB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B;IACA;EACF;EAEAwB,gBAAgBA,CAAA;IACd,IAAI,CAACvB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C;IACA,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEAyB,eAAeA,CAAA;IACb,IAAI,CAACxB,cAAc,GAAG,KAAK;EAC7B;EAGAyB,eAAeA,CAACC,KAAY;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;IACnD,MAAMC,YAAY,GAAGH,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAC;IACrD,MAAME,aAAa,GAAGJ,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;IACvD,MAAMG,iBAAiB,GAAGL,MAAM,CAACE,OAAO,CAAC,sBAAsB,CAAC;IAEhE;IACA,IAAI,CAACD,WAAW,IAAI,CAACE,YAAY,IAAI,IAAI,CAAC/B,gBAAgB,EAAE;MAC1D,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC/B;IAEA;IACA,IAAI,CAACgC,aAAa,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAAChC,cAAc,EAAE;MAC/D,IAAI,CAACA,cAAc,GAAG,KAAK;IAC7B;EACF;EAEA;EACAS,wBAAwBA,CAAA;IACtB;IACA,MAAMwB,aAAa,GAAG,CAAC;IACvB,IAAI,CAAChC,cAAc,GAAG,EAAE;IAExB,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,MAAM,EAAED,CAAC,IAAID,aAAa,EAAE;MAC7D,IAAI,CAAChC,cAAc,CAACmC,IAAI,CAAC,IAAI,CAAChC,SAAS,CAACiC,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGD,aAAa,CAAC,CAAC;IACtE;EACF;EAEArB,kBAAkBA,CAAA;IAChB,IAAI;MACF,MAAM0B,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MACrE,IAAIF,eAAe,EAAE;QACnB;QACA,IAAI,OAAOG,SAAS,KAAK,WAAW,EAAE;UACpC,MAAMC,QAAQ,GAAG,IAAID,SAAS,CAACE,QAAQ,CAACL,eAAe,EAAE;YACvDM,QAAQ,EAAE,IAAI;YACdC,IAAI,EAAE,UAAU;YAChBC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC/C,CAAC,MAAM;UACL;UACA,IAAI,CAACC,mBAAmB,EAAE;UAC1BF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;MACF;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACd6B,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACA,IAAI,CAAC+B,mBAAmB,EAAE;IAC5B;EACF;EAEAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,IAAI,CAAChD,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACtC;IAEA;IACA,IAAI,CAACA,gBAAgB,GAAGiD,WAAW,CAAC,MAAK;MACvC,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAACrD,cAAc,CAACkC,MAAM;IAC9C,IAAImB,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB,GAAG,CAAC,IAAIoD,WAAW;MACnE,IAAI,CAACC,qBAAqB,EAAE;IAC9B;EACF;EAEAC,SAASA,CAAA;IACP,MAAMF,WAAW,GAAG,IAAI,CAACrD,cAAc,CAACkC,MAAM;IAC9C,IAAImB,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,KAAK,CAAC,GAAGoD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACpD,iBAAiB,GAAG,CAAC;MACpG,IAAI,CAACqD,qBAAqB,EAAE;IAC9B;EACF;EAEAA,qBAAqBA,CAAA;IACnB,MAAME,aAAa,GAAGlB,QAAQ,CAACmB,gBAAgB,CAAC,oCAAoC,CAAC;IACrFD,aAAa,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACpC,IAAIA,KAAK,KAAK,IAAI,CAAC3D,iBAAiB,EAAE;QACpC0D,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLH,IAAI,CAACE,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC;EACJ;EAEA/E,eAAeA,CAACgF,QAAa;IAC3BhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,QAAQ,CAAC;IAC1C;IACA;EACF;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,iBAAiBA,CAAA,GAEjB;;qCA9RWvE,aAAa,EAAA3C,EAAA,CAAAmH,iBAAA,CAAAC,EAAA,CAAAC,qBAAA;EAAA;;UAAb1E,aAAa;IAAA2E,SAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAbzH,EAAA,CAAAK,UAAA,mBAAAsH,uCAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAjD,eAAA,CAAAmD,MAAA,CAAuB;QAAA,UAAA5H,EAAA,CAAA6H,iBAAA,CAAV;;;;;;;;QCFlB7H,EARR,CAAAC,cAAA,aAAuB,gBAGO,aAEW,aACD,aAEN;QACxBD,EAAA,CAAAE,SAAA,aAA8G;QAChHF,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,gBACuE;QADhBD,EAAA,CAAAK,UAAA,mBAAAyH,+CAAA;UAAA,OAASJ,GAAA,CAAAnD,gBAAA,EAAkB;QAAA,EAAC;QAEjFvE,EAAA,CAAAE,SAAA,cACO;QACTF,EAAA,CAAAG,YAAA,EAAS;QAMHH,EAHN,CAAAC,cAAA,aAAiD,YACK,cAC7B,aACU;QAACD,EAAA,CAAAI,MAAA,cAAK;QACrCJ,EADqC,CAAAG,YAAA,EAAI,EACpC;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,wBAAe;QAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC9C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,sBAAa;QAC7CJ,EAD6C,CAAAG,YAAA,EAAI,EAC5C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,wBAAe;QAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC9C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,oBAAW;QAG/CJ,EAH+C,CAAAG,YAAA,EAAI,EAC1C,EACF,EACD;QAGNH,EAAA,CAAAsC,UAAA,KAAAyF,6BAAA,mBAAkE;QAkClE/H,EAAA,CAAAC,cAAA,eAA0C;QAoExCD,EAlEA,CAAAsC,UAAA,KAAA0F,6BAAA,kBAAqF,KAAAC,6BAAA,mBAOnB,KAAAC,2BAAA,gBA2DP;QAMjElI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;QAIJH,EADF,CAAAC,cAAA,eAA0B,eACK;QAI3BD,EAHA,CAAAE,SAAA,eAE8C,eACd;QAClCF,EAAA,CAAAG,YAAA,EAAM;QAQMH,EANZ,CAAAC,cAAA,eAA0B,eACD,eACmB,eAClB,eACe,eACH,cACJ;QAACD,EAAA,CAAAI,MAAA,aAAI;QAC7BJ,EAD6B,CAAAG,YAAA,EAAK,EAC5B;QAEJH,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAI,MAAA,eAAM;QAC/BJ,EAD+B,CAAAG,YAAA,EAAK,EAC9B;QAEJH,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAI,MAAA,qBAAY;QAQnDJ,EARmD,CAAAG,YAAA,EAAK,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACC;QAODH,EAJR,CAAAC,cAAA,mBAAoC,eACX,eACJ,eACK,cACyB;QAAAD,EAAA,CAAAI,MAAA,2BAAmB;QAElEJ,EAFkE,CAAAG,YAAA,EAAK,EAC/D,EACF;QAMAH,EAJN,CAAAC,cAAA,eAAqB,eAEsB,eACZ,eACG;QAC1BD,EAAA,CAAAE,SAAA,eAAqF;QACrFF,EAAA,CAAAC,cAAA,eAA4B;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1CH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAE,SAAA,aAAqC;QACrCF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAI,MAAA,iBAAS;QAEnBJ,EAFmB,CAAAG,YAAA,EAAO,EAClB,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8B,cACD;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAChDH,EAAA,CAAAC,cAAA,aAAgC;QAAAD,EAAA,CAAAI,MAAA,oDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,eAA4B,gBACN;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;QAGFH,EAFJ,CAAAC,cAAA,eAAqC,eACN,eACR;QAKjBD,EAJA,CAAAE,SAAA,aAA2B,aACA,aACA,aACA,aACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,gBAA0B;QAAAD,EAAA,CAAAI,MAAA,WAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,eAA8B,kBACqB;QAC/CD,EAAA,CAAAE,SAAA,aAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAiD;QAC/CD,EAAA,CAAAE,SAAA,aAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,eAAyC,eACZ,eACG;QAC1BD,EAAA,CAAAE,SAAA,eAAsF;QACtFF,EAAA,CAAAC,cAAA,eAAgD;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC9DH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAE,SAAA,aAAqC;QACrCF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAEfJ,EAFe,CAAAG,YAAA,EAAO,EACd,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8B,cACD;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,gBAAyC,gBACZ,gBACG;QAC1BD,EAAA,CAAAE,SAAA,gBAAsF;QACtFF,EAAA,CAAAC,cAAA,gBAA4B;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1CH,EAAA,CAAAC,cAAA,gBAA+B;QAC7BD,EAAA,CAAAE,SAAA,cAAqC;QACrCF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,gBAAO;QAEjBJ,EAFiB,CAAAG,YAAA,EAAO,EAChB,EACF;QAEJH,EADF,CAAAC,cAAA,gBAA8B,eACD;QAAAD,EAAA,CAAAI,MAAA,wBAAe;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,gBAAyC,gBACZ,gBACG;QAC1BD,EAAA,CAAAE,SAAA,gBAAsF;QACtFF,EAAA,CAAAC,cAAA,gBAAgD;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC9DH,EAAA,CAAAC,cAAA,gBAA+B;QAC7BD,EAAA,CAAAE,SAAA,cAAqC;QACrCF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,mBAAU;QAEpBJ,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;QAEJH,EADF,CAAAC,cAAA,gBAA8B,eACD;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QASlDF,EARgB,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF,EACF,EACE;QAOFH,EAJR,CAAAC,cAAA,oBAAkD,gBACzB,gBACJ,gBACK,eACyB;QAAAD,EAAA,CAAAI,MAAA,0BAAiB;QAEhEJ,EAFgE,CAAAG,YAAA,EAAK,EAC7D,EACF;QAKFH,EAHJ,CAAAC,cAAA,gBAAkD,gBAEoD,gBACtE;QAE1BD,EAAA,CAAAsC,UAAA,MAAA6F,8BAAA,kBAAyG;QAgB3GnI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,mBACwB;QAAtBD,EAAA,CAAAK,UAAA,mBAAA+H,iDAAA;UAAA,OAASV,GAAA,CAAAlB,SAAA,EAAW;QAAA,EAAC;QACrBxG,EAAA,CAAAE,SAAA,iBAAmE;QACnEF,EAAA,CAAAC,cAAA,iBAA8B;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QACxCJ,EADwC,CAAAG,YAAA,EAAO,EACtC;QACTH,EAAA,CAAAC,cAAA,mBACwB;QAAtBD,EAAA,CAAAK,UAAA,mBAAAgI,iDAAA;UAAA,OAASX,GAAA,CAAArB,SAAA,EAAW;QAAA,EAAC;QACrBrG,EAAA,CAAAE,SAAA,iBAAmE;QACnEF,EAAA,CAAAC,cAAA,iBAA8B;QAAAD,EAAA,CAAAI,MAAA,aAAI;QAGxCJ,EAHwC,CAAAG,YAAA,EAAO,EAClC,EACL,EACF;QAIFH,EAFJ,CAAAC,cAAA,gBAA6C,gBACX,mBACyC;QAA9BD,EAAA,CAAAK,UAAA,mBAAAiI,iDAAA;UAAA,OAASZ,GAAA,CAAAR,iBAAA,EAAmB;QAAA,EAAC;QACpElH,EAAA,CAAAI,MAAA,8BACF;QAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAUEH,EAPZ,CAAAC,cAAA,oBAAuC,gBACd,gBAEC,gBACA,gBAC4D,gBACvB,mBAE5B;QACrBD,EAAA,CAAAE,SAAA,cAAmC;QACrCF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBACuB;QACrBD,EAAA,CAAAE,SAAA,cAAoC;QAExCF,EADE,CAAAG,YAAA,EAAS,EACL;QAENH,EAAA,CAAAC,cAAA,eAAmD;QAAAD,EAAA,CAAAI,MAAA,mCAA0B;QAGnFJ,EAHmF,CAAAG,YAAA,EAAK,EAC9E,EACF,EACF;QAOAH,EAJN,CAAAC,cAAA,gBAAiB,gBAEiE,gBACtD,cACY;QAChCD,EAAA,CAAAE,SAAA,cAAwD;QACxDF,EAAA,CAAAC,cAAA,iBAAgC;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QACnDH,EAAA,CAAAE,SAAA,gBAAmC;QAGzCF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAWQH,EARd,CAAAC,cAAA,gBAAgC,gBACoE,gBACpE,gBAEQ,gBACY,gBACF,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,wDAA+C;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACxDH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,kJAE6C;QAK1DJ,EAL0D,CAAAG,YAAA,EAAI,EAChD,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,8CAAqC;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC9CH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,6KAEsE;QAKnFJ,EALmF,CAAAG,YAAA,EAAI,EACzE,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,oCAA2B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,+JAE0D;QAO3EJ,EAP2E,CAAAG,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF,EACF,EACF;QAOEH,EAJR,CAAAC,cAAA,gBAA2B,gBACmB,gBACF,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,+BAAsB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/BH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,+JAC2D;QAKxEJ,EALwE,CAAAG,YAAA,EAAI,EAC9D,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,+BAAsB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/BH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,gJAC6C;QAK1DJ,EAL0D,CAAAG,YAAA,EAAI,EAChD,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,iBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,wCAA+B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACxCH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,wIAEiC;QAQpDJ,EARoD,CAAAG,YAAA,EAAI,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACF;QAGNH,EAAA,CAAAC,cAAA,iBAAiC;QAG/BD,EAFA,CAAAE,SAAA,oBAAsG,oBACf,oBACA;QAQrGF,EAPY,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACE,EAEN;;;QA1iBIH,EAAA,CAAAa,SAAA,GAAqC;;QA2BjCb,EAAA,CAAAa,SAAA,IAAoB;QAApBb,EAAA,CAAAc,UAAA,SAAA4G,GAAA,CAAA1E,cAAA,CAAoB;QAoClBhD,EAAA,CAAAa,SAAA,GAAgB;QAAhBb,EAAA,CAAAc,UAAA,SAAA4G,GAAA,CAAA5E,UAAA,CAAgB;QAOhB9C,EAAA,CAAAa,SAAA,EAAoC;QAApCb,EAAA,CAAAc,UAAA,SAAA4G,GAAA,CAAA5E,UAAA,IAAA4E,GAAA,CAAA3E,gBAAA,CAAoC;QA2DtC/C,EAAA,CAAAa,SAAA,EAAiB;QAAjBb,EAAA,CAAAc,UAAA,UAAA4G,GAAA,CAAA5E,UAAA,CAAiB;QAsO2B9C,EAAA,CAAAa,SAAA,KAAmB;QAAnBb,EAAA,CAAAc,UAAA,YAAA4G,GAAA,CAAAzE,cAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}