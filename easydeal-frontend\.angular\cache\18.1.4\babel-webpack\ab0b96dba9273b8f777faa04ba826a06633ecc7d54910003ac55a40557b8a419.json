{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication/services/authentication.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../_metronic/shared/keenicon/keenicon.component\";\nfunction HomeComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 169)(1, \"div\", 170)(2, \"span\", 171);\n    i0.ɵɵelement(3, \"i\", 172);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 170)(7, \"span\", 171);\n    i0.ɵɵelement(8, \"i\", 173);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"About EasyDeal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 170)(12, \"span\", 171);\n    i0.ɵɵelement(13, \"i\", 174);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"New Projects\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 170)(17, \"span\", 171);\n    i0.ɵɵelement(18, \"i\", 175);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Advertisements\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 170)(22, \"span\", 171);\n    i0.ɵɵelement(23, \"i\", 176);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Contact Us\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 177);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_27_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserDropdown());\n    });\n    i0.ɵɵelement(1, \"img\", 178);\n    i0.ɵɵelementStart(2, \"span\", 179);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 180);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getUserProfileImage(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n  }\n}\nfunction HomeComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 181)(1, \"div\", 182);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(2, \"span\", 171);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 183)(4, \"g\", 184);\n    i0.ɵɵelement(5, \"path\", 185)(6, \"path\", 186)(7, \"path\", 187);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"defs\")(9, \"clipPath\", 188);\n    i0.ɵɵelement(10, \"rect\", 189);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Requests\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 182);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(14, \"span\", 171);\n    i0.ɵɵelement(15, \"app-keenicon\", 190);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \" My Profile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 182);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(19, \"span\", 171);\n    i0.ɵɵelement(20, \"app-keenicon\", 191);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 182);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(24, \"span\", 171);\n    i0.ɵɵelement(25, \"i\", 192);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \" Help \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 182);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(29, \"span\", 171);\n    i0.ɵɵelement(30, \"app-keenicon\", 193);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \" Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"div\", 194);\n    i0.ɵɵelementStart(34, \"div\", 195);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"span\", 171);\n    i0.ɵɵelement(36, \"i\", 196);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"div\", 194);\n    i0.ɵɵelementStart(40, \"div\", 197);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_40_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(41, \"span\", 198);\n    i0.ɵɵtext(42, \" New Request \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_a_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 199);\n    i0.ɵɵelement(1, \"i\", 200);\n    i0.ɵɵtext(2, \" Register Guest \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_196_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 203)(1, \"div\", 204);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_196_div_2_Template_div_click_1_listener() {\n      const location_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLocationClick(location_r5));\n    });\n    i0.ɵɵelement(2, \"img\", 205);\n    i0.ɵɵelementStart(3, \"div\", 206)(4, \"div\", 207)(5, \"h5\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵelement(8, \"i\", 38);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const location_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", location_r5.image || \"assets/media/auth/404-error.png\", i0.ɵɵsanitizeUrl)(\"alt\", location_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(location_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", location_r5.propertyCount, \" Properties Available\");\n  }\n}\nfunction HomeComponent_div_196_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 201);\n    i0.ɵɵtemplate(2, HomeComponent_div_196_div_2_Template, 10, 4, \"div\", 202);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const slide_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r7 === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", slide_r6);\n  }\n}\nexport class HomeComponent {\n  authService;\n  currentUser = null;\n  isLoggedIn = false;\n  showUserDropdown = false;\n  showMobileMenu = false;\n  // Location Carousel Data\n  locationSlides = [];\n  currentSlideIndex = 0;\n  carouselInterval;\n  locations = [{\n    id: 1,\n    name: ' New Cairo',\n    image: './assets/media/stock/600x400/img-10.jpg',\n    propertyCount: 2341\n  }, {\n    id: 2,\n    name: '  Maadi',\n    image: './assets/media/stock/600x400/img-20.jpg',\n    propertyCount: 1234\n  }, {\n    id: 3,\n    name: ' Sheikh Zayed  ',\n    image: './assets/media/stock/600x400/img-30.jpg',\n    propertyCount: 3421\n  }, {\n    id: 4,\n    name: '   Heliopolis',\n    image: './assets/media/stock/600x400/img-40.jpg',\n    propertyCount: 2341\n  }, {\n    id: 5,\n    name: '   Nasr City',\n    image: './assets/media/stock/600x400/img-50.jpg',\n    propertyCount: 987\n  }, {\n    id: 6,\n    name: '  6 October',\n    image: './assets/media/stock/600x400/img-60.jpg',\n    propertyCount: 1543\n  }, {\n    id: 7,\n    name: '  Maadi',\n    image: './assets/media/stock/600x400/img-70.jpg',\n    propertyCount: 876\n  }, {\n    id: 8,\n    name: '  Zamalek',\n    image: './assets/media/stock/600x400/img-80.jpg',\n    propertyCount: 654\n  }, {\n    id: 9,\n    name: '  New Cairo',\n    image: './assets/media/stock/600x400/img-90.jpg',\n    propertyCount: 1098\n  }, {\n    id: 10,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }, {\n    id: 11,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }, {\n    id: 12,\n    name: '  Nasr City',\n    image: './assets/media/stock/600x400/img-100.jpg',\n    propertyCount: 1432\n  }];\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.checkUserSession();\n    this.initializeLocationSlides();\n  }\n  ngAfterViewInit() {\n    // Initialize Bootstrap carousel after view is loaded\n    setTimeout(() => {\n      this.initializeCarousel();\n    }, 100);\n  }\n  ngOnDestroy() {\n    // Clean up interval when component is destroyed\n    if (this.carouselInterval) {\n      clearInterval(this.carouselInterval);\n    }\n  }\n  checkUserSession() {\n    // Check if user is logged in by checking localStorage\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      try {\n        this.currentUser = JSON.parse(currentUser);\n        this.isLoggedIn = true;\n      } catch (error) {\n        // If parsing fails, user is not logged in\n        this.isLoggedIn = false;\n        this.currentUser = null;\n      }\n    } else {\n      this.isLoggedIn = false;\n      this.currentUser = null;\n    }\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return this.currentUser.fullName || 'User';\n    }\n    return 'Guest';\n  }\n  getUserProfileImage() {\n    if (this.currentUser && this.currentUser.image) {\n      return this.currentUser.image;\n    }\n    // Return default avatar if no profile image\n    return 'assets/media/avatars/blank.png';\n  }\n  toggleUserDropdown() {\n    this.showUserDropdown = !this.showUserDropdown;\n  }\n  closeUserDropdown() {\n    this.showUserDropdown = false;\n  }\n  logout() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('currentUser');\n    this.isLoggedIn = false;\n    this.currentUser = null;\n    this.showUserDropdown = false;\n    // Optionally redirect to login page\n    // this.router.navigate(['/authentication/login']);\n  }\n  toggleMobileMenu() {\n    this.showMobileMenu = !this.showMobileMenu;\n    // Close user dropdown when mobile menu is toggled\n    if (this.showMobileMenu) {\n      this.showUserDropdown = false;\n    }\n  }\n  closeMobileMenu() {\n    this.showMobileMenu = false;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const userProfile = target.closest('.user-profile');\n    const userDropdown = target.closest('.user-dropdown');\n    const navbarToggler = target.closest('.navbar-toggler');\n    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');\n    // Close user dropdown if clicked outside of user profile and dropdown\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\n      this.showUserDropdown = false;\n    }\n    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown\n    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {\n      this.showMobileMenu = false;\n    }\n  }\n  // Location Carousel Methods\n  initializeLocationSlides() {\n    // Split locations into slides of 5 items each\n    const itemsPerSlide = 5;\n    this.locationSlides = [];\n    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {\n      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));\n    }\n  }\n  initializeCarousel() {\n    try {\n      const carouselElement = document.getElementById('horizontalCarousel');\n      if (carouselElement) {\n        // Try Bootstrap first\n        if (typeof bootstrap !== 'undefined') {\n          const carousel = new bootstrap.Carousel(carouselElement, {\n            interval: 5000,\n            ride: 'carousel',\n            wrap: true,\n            keyboard: true,\n            pause: 'hover'\n          });\n          console.log('Bootstrap carousel initialized');\n        } else {\n          // Fallback: Manual carousel control\n          this.startManualCarousel();\n          console.log('Manual carousel initialized');\n        }\n      }\n    } catch (error) {\n      console.error('Error initializing carousel:', error);\n      // Fallback to manual carousel\n      this.startManualCarousel();\n    }\n  }\n  startManualCarousel() {\n    // Clear any existing interval\n    if (this.carouselInterval) {\n      clearInterval(this.carouselInterval);\n    }\n    // Start auto-play\n    this.carouselInterval = setInterval(() => {\n      this.nextSlide();\n    }, 5000);\n  }\n  nextSlide() {\n    const totalSlides = this.locationSlides.length;\n    if (totalSlides > 0) {\n      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;\n      this.updateCarouselDisplay();\n    }\n  }\n  prevSlide() {\n    const totalSlides = this.locationSlides.length;\n    if (totalSlides > 0) {\n      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;\n      this.updateCarouselDisplay();\n    }\n  }\n  updateCarouselDisplay() {\n    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');\n    carouselItems.forEach((item, index) => {\n      if (index === this.currentSlideIndex) {\n        item.classList.add('active');\n      } else {\n        item.classList.remove('active');\n      }\n    });\n  }\n  onLocationClick(location) {\n    console.log('Location clicked:', location);\n    // Add your navigation logic here\n    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });\n  }\n  // Method to load locations from API (for future integration)\n  // loadLocations(): void {\n  //   // Replace with actual API call\n  //   // this.locationService.getLocations().subscribe(data => {\n  //   //   this.locations = data;\n  //   //   this.initializeLocationSlides();\n  //   //   // Re-initialize carousel after data loads\n  //   //   setTimeout(() => this.initializeCarousel(), 100);\n  //   // });\n  // }\n  loadMoreLocations() {}\n  onSubscribeClick() {\n    console.log('Subscribe button clicked');\n    // Add newsletter subscription logic here\n    // You can show a modal or navigate to subscription page\n  }\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function HomeComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 443,\n    vars: 6,\n    consts: [[1, \"home-page\"], [1, \"home-header\"], [1, \"navbar\", \"navbar-expand-lg\"], [1, \"container-fluid\", \"px-4\"], [1, \"navbar-brand\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/loading-logo.png\", 1, \"h-40px\", \"app-sidebar-logo-default\"], [\"type\", \"button\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\", \"d-lg-none\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"navbar-nav\", \"mx-auto\", \"d-none\", \"d-lg-flex\"], [1, \"nav-list\", \"d-flex\", \"align-items-center\", \"mb-0\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\"], [\"class\", \"mobile-nav-dropdown d-lg-none\", 4, \"ngIf\"], [1, \"navbar-nav\", \"position-relative\"], [\"class\", \"nav-link user-profile\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [\"href\", \"#\", \"class\", \"nav-link user-link\", 4, \"ngIf\"], [1, \"hero-section\"], [1, \"hero-background\"], [\"src\", \"./assets/media/home/<USER>\", \"alt\", \"Hero Background\", 1, \"hero-bg-image\"], [1, \"hero-overlay\"], [1, \"hero-content\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-12\"], [1, \"hero-text-container\"], [1, \"hero-text-item\"], [1, \"hero-text\"], [1, \"properties-section\"], [1, \"row\"], [1, \"section-title\", \"text-center\", \"mb-5\"], [1, \"row\", \"g-4\"], [1, \"col-lg-3\", \"col-md-6\", \"col-sm-12\"], [1, \"property-card\"], [1, \"property-image\"], [\"src\", \"./assets/media/stock/600x400/img-1.jpg\", \"alt\", \"Property 1\", 1, \"img-fluid\"], [1, \"property-badge\"], [1, \"property-location\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"property-content\"], [1, \"property-title\"], [1, \"property-description\"], [1, \"property-price\"], [1, \"price\"], [1, \"property-rating-actions\"], [1, \"property-rating\"], [1, \"stars\"], [1, \"fas\", \"fa-star\"], [1, \"rating-text\"], [1, \"property-actions\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"far\", \"fa-heart\"], [1, \"fas\", \"fa-share-alt\"], [\"src\", \"./assets/media/stock/600x400/img-15.jpg\", \"alt\", \"Property 2\", 1, \"img-fluid\"], [1, \"property-badge\", \"property-badge-rent\"], [\"src\", \"./assets/media/stock/600x400/img-25.jpg\", \"alt\", \"Property 3\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-35.jpg\", \"alt\", \"Property 4\", 1, \"img-fluid\"], [1, \"horizontal-carousel-section\", \"py-5\"], [1, \"carousel-container\", \"position-relative\"], [\"id\", \"horizontalCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"5000\", 1, \"carousel\", \"slide\"], [1, \"carousel-inner\"], [\"class\", \"carousel-item \", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"data-bs-target\", \"#horizontalCarousel\", \"data-bs-slide\", \"prev\", 1, \"carousel-control-prev\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"carousel-control-prev-icon\"], [1, \"visually-hidden\"], [\"type\", \"button\", \"data-bs-target\", \"#horizontalCarousel\", \"data-bs-slide\", \"next\", 1, \"carousel-control-next\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"carousel-control-next-icon\"], [1, \"row\", \"justify-content-center\", \"mt-5\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-secondary\", \"btn-lg\", 3, \"click\"], [1, \"articles-section\", \"py-5\"], [1, \"row\", \"mb-5\"], [1, \"section-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"left-controls\", \"d-flex\", \"align-items-center\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide\", \"prev\", 1, \"carousel-control-btn\", \"prev-btn\"], [1, \"fas\", \"fa-chevron-left\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide\", \"next\", 1, \"carousel-control-btn\", \"next-btn\", \"ms-2\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"articles-title\", \"text-center\", \"flex-grow-1\"], [1, \"col-lg-2\", \"col-md-3\", \"col-12\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [1, \"right-link\"], [\"href\", \"#\", 1, \"view-all-link\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\", \"text-success\", \"fs-4\"], [1, \"text-success\", \"fs-2\"], [1, \"green-underline\"], [1, \"col-lg-10\", \"col-md-9\", \"col-12\"], [\"id\", \"articlesCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"6000\", 1, \"carousel\", \"slide\"], [1, \"carousel-item\", \"active\"], [1, \"row\", \"g-4\", \"justify-content-center\"], [1, \"col-lg-4\", \"col-md-6\", \"col-sm-8\"], [1, \"article-card\"], [1, \"article-image\"], [\"src\", \"./assets/media/stock/600x400/img-10.jpg\", \"alt\", \"Article 1\", 1, \"img-fluid\"], [1, \"article-overlay\"], [1, \"article-content\"], [\"src\", \"./assets/media/stock/600x400/img-20.jpg\", \"alt\", \"Article 2\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-30.jpg\", \"alt\", \"Article 3\", 1, \"img-fluid\"], [1, \"carousel-item\"], [\"src\", \"./assets/media/stock/600x400/img-40.jpg\", \"alt\", \"Article 4\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-50.jpg\", \"alt\", \"Article 5\", 1, \"img-fluid\"], [\"src\", \"./assets/media/stock/600x400/img-60.jpg\", \"alt\", \"Article 6\", 1, \"img-fluid\"], [1, \"carousel-indicators\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"0\", 1, \"active\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"1\"], [\"type\", \"button\", \"data-bs-target\", \"#articlesCarousel\", \"data-bs-slide-to\", \"2\"], [1, \"download-app-section\", \"py-5\"], [1, \"row\", \"align-items-center\"], [1, \"col-lg-6\", \"col-md-12\", \"mb-4\", \"mb-lg-0\"], [1, \"download-content\"], [1, \"download-header\", \"mb-4\"], [1, \"download-title\"], [1, \"download-subtitle\"], [1, \"app-store-buttons\", \"d-flex\", \"flex-wrap\", \"gap-3\"], [\"href\", \"#\", 1, \"app-store-btn\"], [1, \"store-button\", \"app-store\"], [1, \"store-icon\"], [1, \"fab\", \"fa-apple\"], [1, \"store-text\"], [1, \"download-text\"], [1, \"store-name\"], [\"href\", \"#\", 1, \"google-play-btn\"], [1, \"store-button\", \"google-play\"], [1, \"fab\", \"fa-google-play\"], [1, \"col-lg-6\", \"col-md-12\", \"text-center\"], [1, \"app-preview\"], [1, \"app-logo-container\"], [\"src\", \"./assets/media/easydeallogos/loading-logo.png\", \"alt\", \"EasyDeal App\", 1, \"app-logo\"], [1, \"app-info\", \"mt-3\"], [1, \"app-name\"], [1, \"app-description\"], [1, \"newsletter-section\", \"py-3\"], [1, \"col-lg-6\", \"col-md-8\", \"col-12\"], [1, \"newsletter-container\"], [1, \"row\", \"align-items-center\", \"g-2\"], [1, \"col-md-7\", \"col-12\", \"text-center\", \"text-md-start\"], [1, \"newsletter-title\", \"mb-1\"], [1, \"newsletter-subtitle\", \"text-muted\"], [1, \"col-md-5\", \"col-12\"], [1, \"newsletter-form\"], [1, \"input-group\", \"input-group-sm\"], [\"type\", \"email\", \"placeholder\", \"Your email\", 1, \"form-control\"], [\"type\", \"button\", 1, \"btn\", \"btn-subscribe\", 3, \"click\"], [1, \"footer-section\"], [1, \"col-lg-4\", \"col-md-6\", \"mb-4\"], [1, \"footer-brand\"], [\"src\", \"./assets/media/easydeallogos/loading-logo.png\", \"alt\", \"EasyDeal Logo\", 1, \"footer-logo\", \"mb-3\"], [1, \"company-name\"], [1, \"company-description\"], [1, \"col-lg-2\", \"col-md-6\", \"mb-4\"], [1, \"footer-links\"], [1, \"footer-title\"], [1, \"footer-menu\"], [\"href\", \"#\"], [1, \"footer-contact\"], [1, \"contact-item\"], [1, \"fas\", \"fa-phone\", \"text-primary\", \"me-2\"], [1, \"fas\", \"fa-envelope\", \"text-primary\", \"me-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"text-primary\", \"me-2\"], [1, \"social-links\", \"mt-3\"], [\"href\", \"#\", 1, \"social-link\"], [1, \"fab\", \"fa-facebook-f\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"fab\", \"fa-twitter\"], [1, \"fab\", \"fa-pinterest\"], [1, \"footer-bottom\"], [1, \"col-md-6\"], [1, \"copyright-text\", \"mb-0\"], [1, \"col-md-6\", \"text-md-end\"], [1, \"footer-bottom-links\"], [1, \"mobile-nav-dropdown\", \"d-lg-none\"], [1, \"dropdown-item\"], [1, \"menu-icon\", \"me-2\"], [1, \"fas\", \"fa-home\", \"fs-6\", \"text-primary\"], [1, \"fas\", \"fa-info-circle\", \"fs-6\", \"text-info\"], [1, \"fas\", \"fa-building\", \"fs-6\", \"text-success\"], [1, \"fas\", \"fa-bullhorn\", \"fs-6\", \"text-warning\"], [1, \"fas\", \"fa-phone\", \"fs-6\", \"text-gray-600\"], [1, \"nav-link\", \"user-profile\", 3, \"click\"], [1, \"user-avatar\", \"me-2\", 3, \"src\", \"alt\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\", \"ms-2\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [\"width\", \"19\", \"height\", \"19\", \"viewBox\", \"0 0 19 19\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"clip-path\", \"url(#clip0_24_2533)\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"], [\"id\", \"clip0_24_2533\"], [\"width\", \"19\", \"height\", \"19\", \"fill\", \"white\"], [\"name\", \"user\", \"type\", \"outline\", 1, \"fs-5\", \"text-primary\"], [\"name\", \"messages\", \"type\", \"outline\", 1, \"fs-5\", \"text-info\"], [1, \"fa-regular\", \"fa-circle-question\", \"fs-6\", \"text-warning\"], [\"name\", \"notification-on\", \"type\", \"outline\", 1, \"fs-5\", \"text-gray-600\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"logout-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"fs-6\", \"text-danger\"], [1, \"dropdown-item\", \"new-request-item\", 3, \"click\"], [1, \"text-success\"], [\"href\", \"#\", 1, \"nav-link\", \"user-link\"], [1, \"fas\", \"fa-user\", \"me-2\"], [1, \"row\", \"justify-content-center\", \"g-2\", \"g-md-3\", \"mt-5\"], [\"class\", \"col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-xl-2\", \"col-lg-2\", \"col-md-3\", \"col-sm-4\", \"col-6\"], [1, \"location-card\", 3, \"click\"], [1, \"img-fluid\", 3, \"src\", \"alt\"], [1, \"location-overlay\"], [1, \"location-info\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"nav\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_6_listener() {\n          return ctx.toggleMobileMenu();\n        });\n        i0.ɵɵelement(7, \"span\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\", 10)(11, \"a\", 11);\n        i0.ɵɵtext(12, \" Home \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"li\", 10)(14, \"a\", 11);\n        i0.ɵɵtext(15, \" About EasyDeal \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 10)(17, \"a\", 11);\n        i0.ɵɵtext(18, \" New Projects \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"li\", 10)(20, \"a\", 11);\n        i0.ɵɵtext(21, \" Advertisements \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"li\", 10)(23, \"a\", 11);\n        i0.ɵɵtext(24, \" Contact Us \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(25, HomeComponent_div_25_Template, 26, 0, \"div\", 12);\n        i0.ɵɵelementStart(26, \"div\", 13);\n        i0.ɵɵtemplate(27, HomeComponent_div_27_Template, 5, 3, \"div\", 14)(28, HomeComponent_div_28_Template, 43, 0, \"div\", 15)(29, HomeComponent_a_29_Template, 3, 0, \"a\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18);\n        i0.ɵɵelement(32, \"img\", 19)(33, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 21)(35, \"div\", 22)(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 25)(39, \"div\", 26)(40, \"h2\", 27);\n        i0.ɵɵtext(41, \" Easy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 26)(43, \"h2\", 27);\n        i0.ɵɵtext(44, \" Speed \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 26)(46, \"h2\", 27);\n        i0.ɵɵtext(47, \" Reliability \");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(48, \"section\", 28)(49, \"div\", 22)(50, \"div\", 29)(51, \"div\", 24)(52, \"h2\", 30);\n        i0.ɵɵtext(53, \"Featured Properties\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(54, \"div\", 31)(55, \"div\", 32)(56, \"div\", 33)(57, \"div\", 34);\n        i0.ɵɵelement(58, \"img\", 35);\n        i0.ɵɵelementStart(59, \"div\", 36);\n        i0.ɵɵtext(60, \"For Sale\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"div\", 37);\n        i0.ɵɵelement(62, \"i\", 38);\n        i0.ɵɵelementStart(63, \"span\");\n        i0.ɵɵtext(64, \"New Cairo\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(65, \"div\", 39)(66, \"h4\", 40);\n        i0.ɵɵtext(67, \"Luxury Apartment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"p\", 41);\n        i0.ɵɵtext(69, \"3 Bedrooms \\u2022 2 Bathrooms \\u2022 150 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 42)(71, \"span\", 43);\n        i0.ɵɵtext(72, \"2.5M EGP\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(73, \"div\", 44)(74, \"div\", 45)(75, \"div\", 46);\n        i0.ɵɵelement(76, \"i\", 47)(77, \"i\", 47)(78, \"i\", 47)(79, \"i\", 47)(80, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"span\", 48);\n        i0.ɵɵtext(82, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"div\", 49)(84, \"button\", 50);\n        i0.ɵɵelement(85, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(86, \"button\", 50);\n        i0.ɵɵelement(87, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(88, \"div\", 32)(89, \"div\", 33)(90, \"div\", 34);\n        i0.ɵɵelement(91, \"img\", 53);\n        i0.ɵɵelementStart(92, \"div\", 54);\n        i0.ɵɵtext(93, \"For Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"div\", 37);\n        i0.ɵɵelement(95, \"i\", 38);\n        i0.ɵɵelementStart(96, \"span\");\n        i0.ɵɵtext(97, \"Maadi\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(98, \"div\", 39)(99, \"h4\", 40);\n        i0.ɵɵtext(100, \"Modern Villa\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(101, \"p\", 41);\n        i0.ɵɵtext(102, \"4 Bedrooms \\u2022 3 Bathrooms \\u2022 250 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"div\", 42)(104, \"span\", 43);\n        i0.ɵɵtext(105, \"25K EGP/month\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(106, \"div\", 44)(107, \"div\", 45)(108, \"div\", 46);\n        i0.ɵɵelement(109, \"i\", 47)(110, \"i\", 47)(111, \"i\", 47)(112, \"i\", 47)(113, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"span\", 48);\n        i0.ɵɵtext(115, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(116, \"div\", 49)(117, \"button\", 50);\n        i0.ɵɵelement(118, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"button\", 50);\n        i0.ɵɵelement(120, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(121, \"div\", 32)(122, \"div\", 33)(123, \"div\", 34);\n        i0.ɵɵelement(124, \"img\", 55);\n        i0.ɵɵelementStart(125, \"div\", 36);\n        i0.ɵɵtext(126, \"For Sale\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"div\", 37);\n        i0.ɵɵelement(128, \"i\", 38);\n        i0.ɵɵelementStart(129, \"span\");\n        i0.ɵɵtext(130, \"Zamalek\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(131, \"div\", 39)(132, \"h4\", 40);\n        i0.ɵɵtext(133, \"Penthouse Suite\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(134, \"p\", 41);\n        i0.ɵɵtext(135, \"5 Bedrooms \\u2022 4 Bathrooms \\u2022 300 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(136, \"div\", 42)(137, \"span\", 43);\n        i0.ɵɵtext(138, \"8.5M EGP\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(139, \"div\", 44)(140, \"div\", 45)(141, \"div\", 46);\n        i0.ɵɵelement(142, \"i\", 47)(143, \"i\", 47)(144, \"i\", 47)(145, \"i\", 47)(146, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(147, \"span\", 48);\n        i0.ɵɵtext(148, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(149, \"div\", 49)(150, \"button\", 50);\n        i0.ɵɵelement(151, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(152, \"button\", 50);\n        i0.ɵɵelement(153, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(154, \"div\", 32)(155, \"div\", 33)(156, \"div\", 34);\n        i0.ɵɵelement(157, \"img\", 56);\n        i0.ɵɵelementStart(158, \"div\", 54);\n        i0.ɵɵtext(159, \"For Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"div\", 37);\n        i0.ɵɵelement(161, \"i\", 38);\n        i0.ɵɵelementStart(162, \"span\");\n        i0.ɵɵtext(163, \"Heliopolis\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(164, \"div\", 39)(165, \"h4\", 40);\n        i0.ɵɵtext(166, \"Family House\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(167, \"p\", 41);\n        i0.ɵɵtext(168, \"3 Bedrooms \\u2022 2 Bathrooms \\u2022 180 sqm\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(169, \"div\", 42)(170, \"span\", 43);\n        i0.ɵɵtext(171, \"18K EGP/month\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(172, \"div\", 44)(173, \"div\", 45)(174, \"div\", 46);\n        i0.ɵɵelement(175, \"i\", 47)(176, \"i\", 47)(177, \"i\", 47)(178, \"i\", 47)(179, \"i\", 47);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(180, \"span\", 48);\n        i0.ɵɵtext(181, \"5.0\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(182, \"div\", 49)(183, \"button\", 50);\n        i0.ɵɵelement(184, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(185, \"button\", 50);\n        i0.ɵɵelement(186, \"i\", 52);\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(187, \"section\", 57)(188, \"div\", 22)(189, \"div\", 29)(190, \"div\", 24)(191, \"h2\", 30);\n        i0.ɵɵtext(192, \"Explore Locations\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(193, \"div\", 58)(194, \"div\", 59)(195, \"div\", 60);\n        i0.ɵɵtemplate(196, HomeComponent_div_196_Template, 3, 3, \"div\", 61);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(197, \"button\", 62);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_197_listener() {\n          return ctx.prevSlide();\n        });\n        i0.ɵɵelement(198, \"span\", 63);\n        i0.ɵɵelementStart(199, \"span\", 64);\n        i0.ɵɵtext(200, \"Previous\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(201, \"button\", 65);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_201_listener() {\n          return ctx.nextSlide();\n        });\n        i0.ɵɵelement(202, \"span\", 66);\n        i0.ɵɵelementStart(203, \"span\", 64);\n        i0.ɵɵtext(204, \"Next\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(205, \"div\", 67)(206, \"div\", 68)(207, \"button\", 69);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_207_listener() {\n          return ctx.loadMoreLocations();\n        });\n        i0.ɵɵtext(208, \" Load More Locations \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(209, \"section\", 70)(210, \"div\", 22)(211, \"div\", 71)(212, \"div\", 24)(213, \"div\", 72)(214, \"div\", 73)(215, \"button\", 74);\n        i0.ɵɵelement(216, \"i\", 75);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(217, \"button\", 76);\n        i0.ɵɵelement(218, \"i\", 77);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(219, \"h1\", 78);\n        i0.ɵɵtext(220, \"Articles That Interest You\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(221, \"div\", 29)(222, \"div\", 79)(223, \"div\", 80)(224, \"a\", 81);\n        i0.ɵɵelement(225, \"i\", 82);\n        i0.ɵɵelementStart(226, \"span\", 83);\n        i0.ɵɵtext(227, \"All Articles\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(228, \"div\", 84);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(229, \"div\", 85)(230, \"div\", 86)(231, \"div\", 60)(232, \"div\", 87)(233, \"div\", 88)(234, \"div\", 89)(235, \"div\", 90)(236, \"div\", 91);\n        i0.ɵɵelement(237, \"img\", 92);\n        i0.ɵɵelementStart(238, \"div\", 93)(239, \"div\", 94)(240, \"h4\");\n        i0.ɵɵtext(241, \"Modern Finishing Materials - Shop with the Best\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(242, \"p\");\n        i0.ɵɵtext(243, \"A very quiet area away from the noise and hustle of the city, suitable for large and small families, spacious area with a private garden.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(244, \"div\", 89)(245, \"div\", 90)(246, \"div\", 91);\n        i0.ɵɵelement(247, \"img\", 95);\n        i0.ɵɵelementStart(248, \"div\", 93)(249, \"div\", 94)(250, \"h4\");\n        i0.ɵɵtext(251, \"Invest Your Money with Hotel Property\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(252, \"p\");\n        i0.ɵɵtext(253, \"Excellent investment opportunity in the heart of the city, guaranteed returns and integrated management, strategic location near the airport and commercial centers.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(254, \"div\", 89)(255, \"div\", 90)(256, \"div\", 91);\n        i0.ɵɵelement(257, \"img\", 96);\n        i0.ɵɵelementStart(258, \"div\", 93)(259, \"div\", 94)(260, \"h4\");\n        i0.ɵɵtext(261, \"Villa 10 October April 2019\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(262, \"p\");\n        i0.ɵɵtext(263, \"Latest international finishing materials, high quality and competitive prices, specialized team to implement finishing works to the highest standards.\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(264, \"div\", 97)(265, \"div\", 88)(266, \"div\", 89)(267, \"div\", 90)(268, \"div\", 91);\n        i0.ɵɵelement(269, \"img\", 98);\n        i0.ɵɵelementStart(270, \"div\", 93)(271, \"div\", 94)(272, \"h4\");\n        i0.ɵɵtext(273, \"Apartment in New Cairo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(274, \"p\");\n        i0.ɵɵtext(275, \"Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated facilities, close to universities and international schools.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(276, \"div\", 89)(277, \"div\", 90)(278, \"div\", 91);\n        i0.ɵɵelement(279, \"img\", 99);\n        i0.ɵɵelementStart(280, \"div\", 93)(281, \"div\", 94)(282, \"h4\");\n        i0.ɵɵtext(283, \"North Coast Properties\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(284, \"p\");\n        i0.ɵɵtext(285, \"Residential units directly on the sea, wonderful panoramic view, integrated recreational facilities and suitable for summer investment.\");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(286, \"div\", 89)(287, \"div\", 90)(288, \"div\", 91);\n        i0.ɵɵelement(289, \"img\", 100);\n        i0.ɵɵelementStart(290, \"div\", 93)(291, \"div\", 94)(292, \"h4\");\n        i0.ɵɵtext(293, \"Administrative Offices Downtown\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(294, \"p\");\n        i0.ɵɵtext(295, \"Modern office spaces in the heart of Cairo, suitable for companies and institutions, parking and integrated service facilities.\");\n        i0.ɵɵelementEnd()()()()()()()()();\n        i0.ɵɵelementStart(296, \"div\", 101);\n        i0.ɵɵelement(297, \"button\", 102)(298, \"button\", 103)(299, \"button\", 104);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(300, \"section\", 105)(301, \"div\", 22)(302, \"div\", 106)(303, \"div\", 107)(304, \"div\", 108)(305, \"div\", 109)(306, \"h2\", 110);\n        i0.ɵɵtext(307, \"Download the Electronic App\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(308, \"p\", 111);\n        i0.ɵɵtext(309, \" Download our app to access the latest real estate offers and properties \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(310, \"div\", 112)(311, \"a\", 113)(312, \"div\", 114)(313, \"div\", 115);\n        i0.ɵɵelement(314, \"i\", 116);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(315, \"div\", 117)(316, \"span\", 118);\n        i0.ɵɵtext(317, \"Download on the\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(318, \"span\", 119);\n        i0.ɵɵtext(319, \"App Store\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(320, \"a\", 120)(321, \"div\", 121)(322, \"div\", 115);\n        i0.ɵɵelement(323, \"i\", 122);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(324, \"div\", 117)(325, \"span\", 118);\n        i0.ɵɵtext(326, \"GET IT ON\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(327, \"span\", 119);\n        i0.ɵɵtext(328, \"Google Play\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(329, \"div\", 123)(330, \"div\", 124)(331, \"div\", 125);\n        i0.ɵɵelement(332, \"img\", 126);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(333, \"div\", 127)(334, \"h4\", 128);\n        i0.ɵɵtext(335, \"EASY DEAL\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(336, \"p\", 129);\n        i0.ɵɵtext(337, \"Your trusted real estate partner\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(338, \"section\", 130)(339, \"div\", 22)(340, \"div\", 23)(341, \"div\", 131)(342, \"div\", 132)(343, \"div\", 133)(344, \"div\", 134)(345, \"h5\", 135);\n        i0.ɵɵtext(346, \"Join Our Mailing List\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(347, \"small\", 136);\n        i0.ɵɵtext(348, \"Get latest offers\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(349, \"div\", 137)(350, \"div\", 138)(351, \"div\", 139);\n        i0.ɵɵelement(352, \"input\", 140);\n        i0.ɵɵelementStart(353, \"button\", 141);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_353_listener() {\n          return ctx.onSubscribeClick();\n        });\n        i0.ɵɵtext(354, \" Subscribe \");\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelementStart(355, \"footer\", 142)(356, \"div\", 22)(357, \"div\", 29)(358, \"div\", 143)(359, \"div\", 144);\n        i0.ɵɵelement(360, \"img\", 145);\n        i0.ɵɵelementStart(361, \"h5\", 146);\n        i0.ɵɵtext(362, \"EASY DEAL\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(363, \"p\", 147);\n        i0.ɵɵtext(364, \" Your trusted real estate partner in Egypt. We provide the best properties and investment opportunities with professional service. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(365, \"div\", 148)(366, \"div\", 149)(367, \"h6\", 150);\n        i0.ɵɵtext(368, \"Quick Links\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(369, \"ul\", 151)(370, \"li\")(371, \"a\", 152);\n        i0.ɵɵtext(372, \"Home\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(373, \"li\")(374, \"a\", 152);\n        i0.ɵɵtext(375, \"About EasyDeal\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(376, \"li\")(377, \"a\", 152);\n        i0.ɵɵtext(378, \"New Projects\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(379, \"li\")(380, \"a\", 152);\n        i0.ɵɵtext(381, \"Advertisements\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(382, \"li\")(383, \"a\", 152);\n        i0.ɵɵtext(384, \"Contact Us\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(385, \"div\", 148)(386, \"div\", 149)(387, \"h6\", 150);\n        i0.ɵɵtext(388, \"Services\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(389, \"ul\", 151)(390, \"li\")(391, \"a\", 152);\n        i0.ɵɵtext(392, \"Property Search\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(393, \"li\")(394, \"a\", 152);\n        i0.ɵɵtext(395, \"Investment Consulting\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(396, \"li\")(397, \"a\", 152);\n        i0.ɵɵtext(398, \"Property Management\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(399, \"li\")(400, \"a\", 152);\n        i0.ɵɵtext(401, \"Legal Support\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(402, \"li\")(403, \"a\", 152);\n        i0.ɵɵtext(404, \"Financing Options\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(405, \"div\", 143)(406, \"div\", 153)(407, \"h6\", 150);\n        i0.ɵɵtext(408, \"Contact Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(409, \"div\", 154);\n        i0.ɵɵelement(410, \"i\", 155);\n        i0.ɵɵelementStart(411, \"span\");\n        i0.ɵɵtext(412, \"19888 - <EMAIL>\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(413, \"div\", 154);\n        i0.ɵɵelement(414, \"i\", 156);\n        i0.ɵɵelementStart(415, \"span\");\n        i0.ɵɵtext(416, \"<EMAIL>\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(417, \"div\", 154);\n        i0.ɵɵelement(418, \"i\", 157);\n        i0.ɵɵelementStart(419, \"span\");\n        i0.ɵɵtext(420, \"Cairo, Egypt\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(421, \"div\", 158)(422, \"a\", 159);\n        i0.ɵɵelement(423, \"i\", 160);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(424, \"a\", 159);\n        i0.ɵɵelement(425, \"i\", 161);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(426, \"a\", 159);\n        i0.ɵɵelement(427, \"i\", 162);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(428, \"a\", 159);\n        i0.ɵɵelement(429, \"i\", 163);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(430, \"div\", 164)(431, \"div\", 106)(432, \"div\", 165)(433, \"p\", 166);\n        i0.ɵɵtext(434, \" \\u00A9 2025 EasyDeal. All rights reserved. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(435, \"div\", 167)(436, \"div\", 168)(437, \"a\", 152);\n        i0.ɵɵtext(438, \"Privacy Policy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(439, \"a\", 152);\n        i0.ɵɵtext(440, \"Terms of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(441, \"a\", 152);\n        i0.ɵɵtext(442, \"Cookie Policy\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵattribute(\"aria-expanded\", ctx.showMobileMenu);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngIf\", ctx.showMobileMenu);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn && ctx.showUserDropdown);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n        i0.ɵɵadvance(167);\n        i0.ɵɵproperty(\"ngForOf\", ctx.locationSlides);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.KeeniconComponent],\n    styles: [\".home-page[_ngcontent-%COMP%] {\\n  background-color: #ffffff !important;\\n}\\n\\n.home-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  min-height: 120vh;\\n  overflow: hidden;\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 1rem 0;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  gap: 2rem;\\n  margin: 0;\\n  padding: 0;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 1rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  direction: rtl;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  color: #27ae60;\\n  transform: translateY(-2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white !important;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n  color: white !important;\\n  text-decoration: none;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 25px;\\n  padding: 0.5rem 1rem;\\n  border: 1px solid rgba(250, 250, 250, 0.3);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(39, 174, 96, 0.5);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.95rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #27ae60;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\\n  min-width: 220px;\\n  z-index: 1000;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(-3px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  width: 18px;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fff5f5;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\\n  border-top: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #d4f4d4, #e8f5e8);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-weight: 600;\\n  text-align: center;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 0;\\n}\\n@keyframes _ngcontent-%COMP%_dropdownFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: calc(100vh - 80px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  margin-top: 20px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-bg-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]:hover   .hero-bg-image[_ngcontent-%COMP%] {\\n  transform: scale(1.02);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(38, 83, 147, 0.515) 0%, rgba(52, 73, 94, 0.5) 30%, rgba(39, 174, 95, 0.518) 70%, rgba(46, 204, 113, 0.8) 100%);\\n  -webkit-backdrop-filter: blur(2px);\\n          backdrop-filter: blur(2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0 4rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 1s ease-out;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(1) {\\n  text-align: right;\\n  animation-delay: 0.2s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(2) {\\n  text-align: center;\\n  animation-delay: 0.4s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(3) {\\n  text-align: left;\\n  animation-delay: 0.6s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 700;\\n  color: white;\\n  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5), 2px 2px 8px rgba(0, 0, 0, 0.7), 4px 4px 15px rgba(0, 0, 0, 0.4);\\n  margin: 0;\\n  padding: 1.5rem 2.5rem;\\n  border-radius: 20px;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3);\\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.6s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px) scale(1.08);\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 10px 30px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.4);\\n  text-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 2px 2px 10px rgba(0, 0, 0, 0.8), 4px 4px 20px rgba(0, 0, 0, 0.5);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 20px;\\n    margin-right: 20px;\\n    border-radius: 20px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    padding: 0 2rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n    padding: 1.2rem 2rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.5rem 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    right: -15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 15px;\\n    margin-right: 15px;\\n    margin-top: 15px;\\n    border-radius: 15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n    padding: 0 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n    text-align: center !important;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    padding: 1rem 1.5rem;\\n    border-radius: 15px;\\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n    height: 40px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n    margin-left: 10px;\\n    margin-right: 10px;\\n    margin-top: 10px;\\n    border-radius: 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n    padding: 0.8rem 1.2rem;\\n    border-radius: 12px;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1);\\n  }\\n}\\n.navbar-toggler[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.1);\\n}\\n.navbar-toggler[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n\\n.mobile-nav-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% - 1.8rem) !important;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  margin-left: -25px !important;\\n  width: 200px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  z-index: 99;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n  margin-top: 0;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  color: #333;\\n  text-decoration: none;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.05);\\n  color: #007bff;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 18px;\\n  height: 18px;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n\\n.download-app-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-top: 1px solid #dee2e6;\\n  margin-top: 150px;\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .download-header[_ngcontent-%COMP%]   .download-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .download-header[_ngcontent-%COMP%]   .download-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .download-header[_ngcontent-%COMP%]   .download-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #6c757d;\\n  line-height: 1.6;\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]:hover, \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  text-decoration: none;\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]:hover   .store-button[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]:hover   .store-button[_ngcontent-%COMP%] {\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 20px;\\n  border-radius: 12px;\\n  background: #000;\\n  color: white;\\n  min-width: 160px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n@media (max-width: 576px) {\\n  .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%], \\n   .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n    padding: 10px 16px;\\n  }\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-icon[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 1.8rem;\\n}\\n@media (max-width: 576px) {\\n  .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-icon[_ngcontent-%COMP%], \\n   .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-icon[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n    margin-right: 10px;\\n  }\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  line-height: 1.2;\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .download-text[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .download-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  opacity: 0.8;\\n  margin-bottom: 2px;\\n}\\n@media (max-width: 576px) {\\n  .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .download-text[_ngcontent-%COMP%], \\n   .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .download-text[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .store-name[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .store-name[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n@media (max-width: 576px) {\\n  .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .store-name[_ngcontent-%COMP%], \\n   .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button[_ngcontent-%COMP%]   .store-text[_ngcontent-%COMP%]   .store-name[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button.app-store[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button.app-store[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #000 0%, #333 100%);\\n}\\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .app-store-btn[_ngcontent-%COMP%]   .store-button.google-play[_ngcontent-%COMP%], \\n.download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%]   .app-store-buttons[_ngcontent-%COMP%]   .google-play-btn[_ngcontent-%COMP%]   .store-button.google-play[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #01875f 0%, #4285f4 100%);\\n}\\n.download-app-section[_ngcontent-%COMP%]   .app-preview[_ngcontent-%COMP%]   .app-logo-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  border-radius: 50%;\\n  width: 120px;\\n  height: 120px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);\\n}\\n.download-app-section[_ngcontent-%COMP%]   .app-preview[_ngcontent-%COMP%]   .app-logo-container[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%] {\\n  height: 80px;\\n  width: auto;\\n  filter: brightness(0) invert(1);\\n}\\n.download-app-section[_ngcontent-%COMP%]   .app-preview[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 0.5rem;\\n}\\n.download-app-section[_ngcontent-%COMP%]   .app-preview[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 1rem;\\n}\\n\\n.newsletter-section[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1rem;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e9ecef;\\n}\\n@media (max-width: 576px) {\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-container[_ngcontent-%COMP%] {\\n    padding: 0.8rem;\\n  }\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  margin-bottom: 0;\\n}\\n@media (max-width: 768px) {\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  font-size: 0.85rem;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #27ae60;\\n  box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn-subscribe[_ngcontent-%COMP%] {\\n  background: #27ae60;\\n  color: white;\\n  border: none;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  transition: all 0.2s ease;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn-subscribe[_ngcontent-%COMP%]:hover {\\n  background: #229954;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn-subscribe[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);\\n}\\n\\n.footer-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\\n  color: #ecf0f1;\\n  padding: 4rem 0 2rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-brand[_ngcontent-%COMP%]   .footer-logo[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: auto;\\n  filter: brightness(0) invert(1);\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-brand[_ngcontent-%COMP%]   .company-name[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #27ae60;\\n  margin-bottom: 1rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-brand[_ngcontent-%COMP%]   .company-description[_ngcontent-%COMP%] {\\n  color: #bdc3c7;\\n  line-height: 1.6;\\n  font-size: 0.95rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-title[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #27ae60;\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 30px;\\n  height: 2px;\\n  background: #27ae60;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-menu[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-menu[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.8rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-menu[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #bdc3c7;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  transition: all 0.3s ease;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-menu[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #27ae60;\\n  padding-left: 5px;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .footer-title[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #27ae60;\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .footer-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 30px;\\n  height: 2px;\\n  background: #27ae60;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  color: #bdc3c7;\\n  font-size: 0.9rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .contact-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 20px;\\n  text-align: center;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(39, 174, 96, 0.1);\\n  border: 1px solid #27ae60;\\n  border-radius: 50%;\\n  color: #27ae60;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]:hover {\\n  background: #27ae60;\\n  color: white;\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-contact[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%] {\\n  border-top: 1px solid #34495e;\\n  margin-top: 3rem;\\n  padding-top: 2rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .copyright-text[_ngcontent-%COMP%] {\\n  color: #95a5a6;\\n  font-size: 0.9rem;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .footer-bottom-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  justify-content: flex-end;\\n}\\n@media (max-width: 768px) {\\n  .footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .footer-bottom-links[_ngcontent-%COMP%] {\\n    justify-content: flex-start;\\n    margin-top: 1rem;\\n  }\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .footer-bottom-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #95a5a6;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  transition: color 0.3s ease;\\n}\\n.footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .footer-bottom-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #27ae60;\\n}\\n\\n@media (max-width: 768px) {\\n  .download-app-section[_ngcontent-%COMP%]   .download-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 2rem;\\n  }\\n  .footer-section[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .footer-bottom-links[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    margin-top: 1rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .mobile-nav-dropdown[_ngcontent-%COMP%] {\\n    width: 180px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 6px 10px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px !important;\\n    height: 16px !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.7rem !important;\\n  }\\n  .mobile-nav-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.75rem !important;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    justify-content: space-between !important;\\n    align-items: center !important;\\n    flex-wrap: nowrap !important;\\n    width: 100% !important;\\n    padding: 0.5rem 1rem !important;\\n    min-height: auto !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 35px !important;\\n    width: auto !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n    padding: 0.25rem 0.5rem !important;\\n    border: none !important;\\n    background: transparent !important;\\n    order: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n    flex: 0 0 auto !important;\\n    margin: 0 !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav.position-relative[_ngcontent-%COMP%] {\\n    position: relative !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem !important;\\n    font-size: 0.85rem !important;\\n    white-space: nowrap !important;\\n    display: flex !important;\\n    align-items: center !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 28px !important;\\n    height: 28px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: inline !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .fas.fa-chevron-down[_ngcontent-%COMP%] {\\n    font-size: 0.7rem !important;\\n    margin-left: 0.25rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    padding: 0.25rem 0.5rem !important;\\n    font-size: 0.85rem !important;\\n    white-space: nowrap !important;\\n    display: flex !important;\\n    align-items: center !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 0.25rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav.mx-auto[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.75rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 30px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 24px !important;\\n    height: 24px !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    padding: 0.2rem 0.4rem !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 0 !important;\\n    font-size: 1rem !important;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    display: flex !important;\\n    flex-direction: row !important;\\n    justify-content: space-between !important;\\n    align-items: center !important;\\n    flex-wrap: nowrap !important;\\n    overflow: visible !important;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]    > .container-fluid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    flex-shrink: 0 !important;\\n    white-space: nowrap !important;\\n  }\\n}\\n@media (max-width: 319px) {\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n.properties-section[_ngcontent-%COMP%] {\\n  padding: 50px 0;\\n}\\n.properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 4rem;\\n  position: relative;\\n}\\n.properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -15px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #3498db, #2980b9);\\n  border-radius: 2px;\\n}\\n\\n.property-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  height: 100%;\\n  margin-top: 30px;\\n  cursor: pointer;\\n}\\n.property-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  left: 15px;\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  padding: 5px 12px;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge.property-badge-rent[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c, #c0392b);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 15px;\\n  left: 15px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 5px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 8px;\\n  line-height: 1.3;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-description[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  font-size: 0.9rem;\\n  margin-bottom: 15px;\\n  line-height: 1.5;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  color: #3498db;\\n  background: linear-gradient(135deg, #3498db, #2980b9);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  font-size: 0.9rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  color: #7f8c8d;\\n  transition: all 0.3s ease;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3498db;\\n  color: #3498db;\\n  background: rgba(52, 152, 219, 0.1);\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 0;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  flex: 1;\\n}\\n.property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating-actions[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n}\\n\\n@media (max-width: 768px) {\\n  .properties-section[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n  .property-card[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .properties-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .properties-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n    margin-bottom: 1.5rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badge[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 4px 8px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 4px 8px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n  }\\n  .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%] {\\n  background-color: #F8F8F8;\\n  margin-top: 90px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 3rem;\\n  position: relative;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -15px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #007bff, #0056b3);\\n  border-radius: 2px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n  margin: 0 30px;\\n  margin-top: 30px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 200px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]:hover   .location-overlay[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  padding: 15px;\\n  transition: all 0.3s ease;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  color: white;\\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin: 0;\\n  opacity: 0.9;\\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n  color: #ffd700;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 50px !important;\\n  height: 50px !important;\\n  background: #031752;\\n  border: none;\\n  border-radius: 50%;\\n  opacity: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]:hover, \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-size: 20px 20px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n  left: 1px;\\n}\\n.horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  right: 1px;\\n}\\n\\n@media (min-width: 1400px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 50px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n}\\n@media (min-width: 992px) and (max-width: 1399px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 40px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 200px;\\n  }\\n}\\n@media (min-width: 768px) and (max-width: 991px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2.2rem;\\n    margin-bottom: 2.5rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 30px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -5px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -5px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    color: white;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n@media (min-width: 576px) and (max-width: 767px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n    color: white;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n@media (max-width: 575px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n    margin-bottom: 1.5rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    background-size: 16px 16px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 140px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n    color: white;\\n    margin-bottom: 3px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 3px;\\n  }\\n}\\n@media (max-width: 400px) {\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .carousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -20px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%] {\\n    height: 120px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .horizontal-carousel-section[_ngcontent-%COMP%]   .carousel-container[_ngcontent-%COMP%]   .location-card[_ngcontent-%COMP%]   .location-overlay[_ngcontent-%COMP%]   .location-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n}\\n.articles-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 100px 0;\\n  margin-top: 80px;\\n  margin-bottom: 50px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #1e3a8a;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.25);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.35);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n  font-size: 2.8rem;\\n  font-weight: 800;\\n  color: #1e3a8a;\\n  text-shadow: 2px 2px 4px rgba(30, 58, 138, 0.1);\\n  margin: 0;\\n  letter-spacing: -0.5px;\\n  direction: ltr;\\n  text-align: center;\\n  margin-left: 50%;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  position: relative;\\n  display: inline-block;\\n  transition: all 0.3s ease;\\n  direction: ltr;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   .green-underline[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 100%;\\n  height: 3px;\\n  background: #28a745;\\n  border-radius: 2px;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover {\\n  transform: translateX(-5px);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   .text-success[_ngcontent-%COMP%] {\\n  color: #1e7e34 !important;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   .green-underline[_ngcontent-%COMP%] {\\n  background: #1e7e34;\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(-3px);\\n}\\n.articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  cursor: pointer;\\n  height: 400px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.03);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover   .article-overlay[_ngcontent-%COMP%] {\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));\\n  color: white;\\n  padding: 40px 25px 25px;\\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%] {\\n  direction: ltr;\\n  text-align: left;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  margin-bottom: 12px;\\n  color: white;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  line-height: 1.3;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n  opacity: 0.95;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n  line-height: 1.6;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 50px;\\n  height: 50px;\\n  background: #1e3a8a;\\n  border: none;\\n  border-radius: 50%;\\n  opacity: 1;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]:hover, \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-size: 20px 20px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n  left: -25px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n  right: -25px;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n  bottom: -50px;\\n  margin-bottom: 0;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(30, 58, 138, 0.3);\\n  margin: 0 6px;\\n  transition: all 0.3s ease;\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%] {\\n  background: #1e3a8a;\\n  transform: scale(1.2);\\n}\\n.articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #1e40af;\\n  transform: scale(1.1);\\n}\\n\\n@media (max-width: 1200px) {\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n    gap: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%] {\\n    order: 2;\\n    margin-top: 20px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%] {\\n    font-size: 1.2rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 1.2rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-10[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-9[_ngcontent-%COMP%] {\\n    order: 1;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n    margin-bottom: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    top: 45%;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    background-size: 16px 16px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%] {\\n    left: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    right: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -40px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n    margin: 0 4px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.6rem;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%] {\\n    margin-top: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.9rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 220px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-bottom: 8px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    line-height: 1.4;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-prev[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-prev-icon[_ngcontent-%COMP%], \\n   .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-control-next[_ngcontent-%COMP%]   .carousel-control-next-icon[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n    background-size: 14px 14px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 8px;\\n    height: 8px;\\n    margin: 0 3px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .articles-section[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .articles-title[_ngcontent-%COMP%] {\\n    font-size: 1.4rem;\\n    text-align: center;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .left-controls[_ngcontent-%COMP%]   .carousel-control-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%]   .right-link[_ngcontent-%COMP%]   .view-all-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.9rem !important;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%], .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-sm-8[_ngcontent-%COMP%] {\\n    max-width: 90%;\\n    margin: 0 auto;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n    height: 200px;\\n    margin-bottom: 15px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    margin-bottom: 6px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-inner[_ngcontent-%COMP%]   .carousel-item[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-overlay[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    line-height: 1.3;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%] {\\n    bottom: -30px;\\n  }\\n  .articles-section[_ngcontent-%COMP%]   #articlesCarousel[_ngcontent-%COMP%]   .carousel-indicators[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 6px;\\n    height: 6px;\\n    margin: 0 2px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "HomeComponent_div_27_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleUserDropdown", "ɵɵadvance", "ɵɵproperty", "getUserProfileImage", "ɵɵsanitizeUrl", "getUserDisplayName", "ɵɵtextInterpolate", "HomeComponent_div_28_Template_div_click_1_listener", "_r3", "closeUserDropdown", "HomeComponent_div_28_Template_div_click_13_listener", "HomeComponent_div_28_Template_div_click_18_listener", "HomeComponent_div_28_Template_div_click_23_listener", "HomeComponent_div_28_Template_div_click_28_listener", "HomeComponent_div_28_Template_div_click_34_listener", "logout", "HomeComponent_div_28_Template_div_click_40_listener", "HomeComponent_div_196_div_2_Template_div_click_1_listener", "location_r5", "_r4", "$implicit", "onLocationClick", "image", "name", "ɵɵtextInterpolate1", "propertyCount", "ɵɵtemplate", "HomeComponent_div_196_div_2_Template", "ɵɵclassProp", "i_r7", "slide_r6", "HomeComponent", "authService", "currentUser", "isLoggedIn", "showUserDropdown", "showMobileMenu", "locationSlides", "currentSlideIndex", "carouselI<PERSON>val", "locations", "id", "constructor", "ngOnInit", "checkUserSession", "initializeLocationSlides", "ngAfterViewInit", "setTimeout", "initializeCarousel", "ngOnDestroy", "clearInterval", "authToken", "localStorage", "getItem", "JSON", "parse", "error", "fullName", "removeItem", "toggleMobileMenu", "closeMobileMenu", "onDocumentClick", "event", "target", "userProfile", "closest", "userDropdown", "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "mobileNavDropdown", "itemsPerSlide", "i", "length", "push", "slice", "carouselElement", "document", "getElementById", "bootstrap", "carousel", "Carousel", "interval", "ride", "wrap", "keyboard", "pause", "console", "log", "startManualCarousel", "setInterval", "nextSlide", "totalSlides", "updateCarouselDisplay", "prevSlide", "carouselItems", "querySelectorAll", "for<PERSON>ach", "item", "index", "classList", "add", "remove", "location", "loadMoreLocations", "onSubscribeClick", "ɵɵdirectiveInject", "i1", "AuthenticationService", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "HomeComponent_Template_button_click_6_listener", "HomeComponent_div_25_Template", "HomeComponent_div_27_Template", "HomeComponent_div_28_Template", "HomeComponent_a_29_Template", "HomeComponent_div_196_Template", "HomeComponent_Template_button_click_197_listener", "HomeComponent_Template_button_click_201_listener", "HomeComponent_Template_button_click_207_listener", "HomeComponent_Template_button_click_353_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener, After<PERSON>iewInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\r\nimport { AuthenticationService } from '../authentication/services/authentication.service';\r\n\r\ndeclare var bootstrap: any;\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  currentUser: any = null;\r\n  isLoggedIn: boolean = false;\r\n  showUserDropdown: boolean = false;\r\n  showMobileMenu: boolean = false;\r\n\r\n  // Location Carousel Data\r\n  locationSlides: any[][] = [];\r\n  currentSlideIndex: number = 0;\r\n  carouselInterval: any;\r\n  locations: any[] = [\r\n    {\r\n      id: 1,\r\n      name: ' New Cairo',\r\n      image: './assets/media/stock/600x400/img-10.jpg',\r\n      propertyCount: 2341\r\n    },\r\n    {\r\n      id: 2,\r\n      name: '  <PERSON><PERSON>',\r\n      image: './assets/media/stock/600x400/img-20.jpg',\r\n      propertyCount: 1234\r\n    },\r\n    {\r\n      id: 3,\r\n      name: ' <PERSON>  ',\r\n      image: './assets/media/stock/600x400/img-30.jpg',\r\n      propertyCount: 3421\r\n    },\r\n    {\r\n      id: 4,\r\n      name: '   Heliopolis',\r\n      image: './assets/media/stock/600x400/img-40.jpg',\r\n      propertyCount: 2341\r\n    },\r\n    {\r\n      id: 5,\r\n      name: '   Nasr City',\r\n      image: './assets/media/stock/600x400/img-50.jpg',\r\n      propertyCount: 987\r\n    },\r\n    {\r\n      id: 6,\r\n      name: '  6 October',\r\n      image: './assets/media/stock/600x400/img-60.jpg',\r\n      propertyCount: 1543\r\n    },\r\n    {\r\n      id: 7,\r\n      name: '  Maadi',\r\n      image: './assets/media/stock/600x400/img-70.jpg',\r\n      propertyCount: 876\r\n    },\r\n    {\r\n      id: 8,\r\n      name: '  Zamalek',\r\n      image: './assets/media/stock/600x400/img-80.jpg',\r\n      propertyCount: 654\r\n    },\r\n    {\r\n      id: 9,\r\n      name: '  New Cairo',\r\n      image: './assets/media/stock/600x400/img-90.jpg',\r\n      propertyCount: 1098\r\n    },\r\n    {\r\n      id: 10,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    },\r\n    {\r\n      id: 11,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    },\r\n    {\r\n      id: 12,\r\n      name: '  Nasr City',\r\n      image: './assets/media/stock/600x400/img-100.jpg',\r\n      propertyCount: 1432\r\n    }\r\n  ];\r\n\r\n  constructor(private authService: AuthenticationService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.checkUserSession();\r\n    this.initializeLocationSlides();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Initialize Bootstrap carousel after view is loaded\r\n    setTimeout(() => {\r\n      this.initializeCarousel();\r\n    }, 100);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up interval when component is destroyed\r\n    if (this.carouselInterval) {\r\n      clearInterval(this.carouselInterval);\r\n    }\r\n  }\r\n\r\n  checkUserSession(): void {\r\n    // Check if user is logged in by checking localStorage\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      try {\r\n        this.currentUser = JSON.parse(currentUser);\r\n        this.isLoggedIn = true;\r\n      } catch (error) {\r\n        // If parsing fails, user is not logged in\r\n        this.isLoggedIn = false;\r\n        this.currentUser = null;\r\n      }\r\n    } else {\r\n      this.isLoggedIn = false;\r\n      this.currentUser = null;\r\n    }\r\n  }\r\n\r\n  getUserDisplayName(): string {\r\n    if (this.currentUser) {\r\n      return this.currentUser.fullName  || 'User';\r\n    }\r\n    return 'Guest';\r\n  }\r\n\r\n  getUserProfileImage(): string {\r\n    if (this.currentUser && this.currentUser.image) {\r\n      return this.currentUser.image;\r\n    }\r\n    // Return default avatar if no profile image\r\n    return 'assets/media/avatars/blank.png';\r\n  }\r\n\r\n  toggleUserDropdown(): void {\r\n    this.showUserDropdown = !this.showUserDropdown;\r\n  }\r\n\r\n  closeUserDropdown(): void {\r\n    this.showUserDropdown = false;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    localStorage.removeItem('currentUser');\r\n    this.isLoggedIn = false;\r\n    this.currentUser = null;\r\n    this.showUserDropdown = false;\r\n    // Optionally redirect to login page\r\n    // this.router.navigate(['/authentication/login']);\r\n  }\r\n\r\n  toggleMobileMenu(): void {\r\n    this.showMobileMenu = !this.showMobileMenu;\r\n    // Close user dropdown when mobile menu is toggled\r\n    if (this.showMobileMenu) {\r\n      this.showUserDropdown = false;\r\n    }\r\n  }\r\n\r\n  closeMobileMenu(): void {\r\n    this.showMobileMenu = false;\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const userProfile = target.closest('.user-profile');\r\n    const userDropdown = target.closest('.user-dropdown');\r\n    const navbarToggler = target.closest('.navbar-toggler');\r\n    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');\r\n\r\n    // Close user dropdown if clicked outside of user profile and dropdown\r\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\r\n      this.showUserDropdown = false;\r\n    }\r\n\r\n    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown\r\n    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {\r\n      this.showMobileMenu = false;\r\n    }\r\n  }\r\n\r\n  // Location Carousel Methods\r\n  initializeLocationSlides(): void {\r\n    // Split locations into slides of 5 items each\r\n    const itemsPerSlide = 5;\r\n    this.locationSlides = [];\r\n\r\n    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {\r\n      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));\r\n    }\r\n  }\r\n\r\n  initializeCarousel(): void {\r\n    try {\r\n      const carouselElement = document.getElementById('horizontalCarousel');\r\n      if (carouselElement) {\r\n        // Try Bootstrap first\r\n        if (typeof bootstrap !== 'undefined') {\r\n          const carousel = new bootstrap.Carousel(carouselElement, {\r\n            interval: 5000,\r\n            ride: 'carousel',\r\n            wrap: true,\r\n            keyboard: true,\r\n            pause: 'hover'\r\n          });\r\n          console.log('Bootstrap carousel initialized');\r\n        } else {\r\n          // Fallback: Manual carousel control\r\n          this.startManualCarousel();\r\n          console.log('Manual carousel initialized');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error initializing carousel:', error);\r\n      // Fallback to manual carousel\r\n      this.startManualCarousel();\r\n    }\r\n  }\r\n\r\n  startManualCarousel(): void {\r\n    // Clear any existing interval\r\n    if (this.carouselInterval) {\r\n      clearInterval(this.carouselInterval);\r\n    }\r\n\r\n    // Start auto-play\r\n    this.carouselInterval = setInterval(() => {\r\n      this.nextSlide();\r\n    }, 5000);\r\n  }\r\n\r\n  nextSlide(): void {\r\n    const totalSlides = this.locationSlides.length;\r\n    if (totalSlides > 0) {\r\n      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;\r\n      this.updateCarouselDisplay();\r\n    }\r\n  }\r\n\r\n  prevSlide(): void {\r\n    const totalSlides = this.locationSlides.length;\r\n    if (totalSlides > 0) {\r\n      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;\r\n      this.updateCarouselDisplay();\r\n    }\r\n  }\r\n\r\n  updateCarouselDisplay(): void {\r\n    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');\r\n    carouselItems.forEach((item, index) => {\r\n      if (index === this.currentSlideIndex) {\r\n        item.classList.add('active');\r\n      } else {\r\n        item.classList.remove('active');\r\n      }\r\n    });\r\n  }\r\n\r\n  onLocationClick(location: any): void {\r\n    console.log('Location clicked:', location);\r\n    // Add your navigation logic here\r\n    // Example: this.router.navigate(['/properties'], { queryParams: { location: location.id } });\r\n  }\r\n\r\n  // Method to load locations from API (for future integration)\r\n  // loadLocations(): void {\r\n  //   // Replace with actual API call\r\n  //   // this.locationService.getLocations().subscribe(data => {\r\n  //   //   this.locations = data;\r\n  //   //   this.initializeLocationSlides();\r\n  //   //   // Re-initialize carousel after data loads\r\n  //   //   setTimeout(() => this.initializeCarousel(), 100);\r\n  //   // });\r\n  // }\r\n\r\n  loadMoreLocations(){\r\n\r\n  }\r\n\r\n  onSubscribeClick() {\r\n    console.log('Subscribe button clicked');\r\n    // Add newsletter subscription logic here\r\n    // You can show a modal or navigate to subscription page\r\n  }\r\n\r\n}\r\n", "<div class=\"home-page\">\r\n\r\n  <!-- Header Section -->\r\n  <header class=\"home-header\">\r\n    <!-- Navigation Bar -->\r\n    <nav class=\"navbar navbar-expand-lg\">\r\n      <div class=\"container-fluid px-4\">\r\n        <!-- Logo -->\r\n        <div class=\"navbar-brand\">\r\n          <img alt=\"Logo\" src=\"./assets/media/easydeallogos/loading-logo.png\" class=\"h-40px app-sidebar-logo-default\" />\r\n        </div>\r\n\r\n        <!-- Mobile Menu Toggle Button -->\r\n        <button class=\"navbar-toggler d-lg-none\" type=\"button\" (click)=\"toggleMobileMenu()\"\r\n          [attr.aria-expanded]=\"showMobileMenu\" aria-label=\"Toggle navigation\">\r\n          <span class=\"navbar-toggler-icon\">\r\n          </span>\r\n        </button>\r\n\r\n        <!-- Navigation Menu -->\r\n        <div class=\"navbar-nav mx-auto d-none d-lg-flex\">\r\n          <ul class=\"nav-list d-flex align-items-center mb-0\">\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Home </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> About EasyDeal </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> New Projects </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Advertisements </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Contact Us </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <!-- Mobile Navigation Dropdown -->\r\n        <div *ngIf=\"showMobileMenu\" class=\"mobile-nav-dropdown d-lg-none\">\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-home fs-6 text-primary\"></i>\r\n            </span>\r\n            <span>Home</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-info-circle fs-6 text-info\"></i>\r\n            </span>\r\n            <span>About EasyDeal</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-building fs-6 text-success\"></i>\r\n            </span>\r\n            <span>New Projects</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-bullhorn fs-6 text-warning\"></i>\r\n            </span>\r\n            <span>Advertisements</span>\r\n          </div>\r\n          <div class=\"dropdown-item\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-phone fs-6 text-gray-600\"></i>\r\n            </span>\r\n            <span>Contact Us</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- User Registration Link / User Profile -->\r\n        <div class=\"navbar-nav position-relative\">\r\n          <!-- If user is logged in, show user profile -->\r\n          <div *ngIf=\"isLoggedIn\" class=\"nav-link user-profile\" (click)=\"toggleUserDropdown()\">\r\n            <img [src]=\"getUserProfileImage()\" [alt]=\"getUserDisplayName()\" class=\"user-avatar me-2\">\r\n            <span class=\"user-name\">{{ getUserDisplayName() }}</span>\r\n            <i class=\"fas fa-chevron-down ms-2\"></i>\r\n          </div>\r\n\r\n          <!-- User Dropdown Menu -->\r\n          <div *ngIf=\"isLoggedIn && showUserDropdown\" class=\"user-dropdown\">\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <svg width=\"19\" height=\"19\" viewBox=\"0 0 19 19\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <g clip-path=\"url(#clip0_24_2533)\">\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\" />\r\n                  </g>\r\n                  <defs>\r\n                    <clipPath id=\"clip0_24_2533\">\r\n                      <rect width=\"19\" height=\"19\" fill=\"white\" />\r\n                    </clipPath>\r\n                  </defs>\r\n                </svg>\r\n              </span>\r\n              <span>Requests</span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"user\" class=\"fs-5 text-primary\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> My Profile </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"messages\" class=\"fs-5 text-info\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Messages </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fa-regular fa-circle-question fs-6 text-warning\"></i>\r\n              </span>\r\n              <span> Help </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"notification-on\" class=\"fs-5 text-gray-600\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Notifications </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item logout-item\" (click)=\"logout()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fas fa-sign-out-alt fs-6 text-danger\"></i>\r\n              </span>\r\n              <span> Logout </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item new-request-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"text-success\"> New Request </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- If user is not logged in, show register button -->\r\n          <a *ngIf=\"!isLoggedIn\" href=\"#\" class=\"nav-link user-link\">\r\n            <i class=\"fas fa-user me-2\"></i>\r\n            Register Guest\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n\r\n    <!-- Hero Section -->\r\n    <div class=\"hero-section\">\r\n      <div class=\"hero-background\">\r\n        <img\r\n          src=\"./assets/media/home/<USER>\"\r\n          alt=\"Hero Background\" class=\"hero-bg-image\">\r\n        <div class=\"hero-overlay\"></div>\r\n      </div>\r\n\r\n      <div class=\"hero-content\">\r\n        <div class=\"container\">\r\n          <div class=\"row justify-content-center\">\r\n            <div class=\"col-12\">\r\n              <div class=\"hero-text-container\">\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Easy</h2>\r\n                </div>\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Speed </h2>\r\n                </div>\r\n                <div class=\"hero-text-item\">\r\n                  <h2 class=\"hero-text\"> Reliability </h2>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </header>\r\n\r\n  <!-- Properties Section -->\r\n  <section class=\"properties-section\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <h2 class=\"section-title text-center mb-5\">Featured Properties</h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row g-4\">\r\n        <!-- Property Card 1 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-1.jpg\" alt=\"Property 1\" class=\"img-fluid\">\r\n              <div class=\"property-badge\">For Sale</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>New Cairo</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Luxury Apartment</h4>\r\n              <p class=\"property-description\">3 Bedrooms • 2 Bathrooms • 150 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">2.5M EGP</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 2 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-15.jpg\" alt=\"Property 2\" class=\"img-fluid\">\r\n              <div class=\"property-badge property-badge-rent\">For Rent</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Maadi</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Modern Villa</h4>\r\n              <p class=\"property-description\">4 Bedrooms • 3 Bathrooms • 250 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">25K EGP/month</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 3 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-25.jpg\" alt=\"Property 3\" class=\"img-fluid\">\r\n              <div class=\"property-badge\">For Sale</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Zamalek</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Penthouse Suite</h4>\r\n              <p class=\"property-description\">5 Bedrooms • 4 Bathrooms • 300 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">8.5M EGP</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Property Card 4 -->\r\n        <div class=\"col-lg-3 col-md-6 col-sm-12\">\r\n          <div class=\"property-card\">\r\n            <div class=\"property-image\">\r\n              <img src=\"./assets/media/stock/600x400/img-35.jpg\" alt=\"Property 4\" class=\"img-fluid\">\r\n              <div class=\"property-badge property-badge-rent\">For Rent</div>\r\n              <div class=\"property-location\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <span>Heliopolis</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"property-content\">\r\n              <h4 class=\"property-title\">Family House</h4>\r\n              <p class=\"property-description\">3 Bedrooms • 2 Bathrooms • 180 sqm</p>\r\n              <div class=\"property-price\">\r\n                <span class=\"price\">18K EGP/month</span>\r\n              </div>\r\n              <div class=\"property-rating-actions\">\r\n                <div class=\"property-rating\">\r\n                  <div class=\"stars\">\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                    <i class=\"fas fa-star\"></i>\r\n                  </div>\r\n                  <span class=\"rating-text\">5.0</span>\r\n                </div>\r\n                <div class=\"property-actions\">\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"far fa-heart\"></i>\r\n                  </button>\r\n                  <button class=\"btn btn-outline-secondary btn-sm\">\r\n                    <i class=\"fas fa-share-alt\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Explore Locations Section -->\r\n  <section class=\"horizontal-carousel-section py-5\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <h2 class=\"section-title text-center mb-5\">Explore Locations</h2>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"carousel-container position-relative\">\r\n        <!-- Bootstrap Carousel -->\r\n        <div id=\"horizontalCarousel\" class=\"carousel slide\" data-bs-ride=\"carousel\" data-bs-interval=\"5000\">\r\n          <div class=\"carousel-inner\">\r\n            <!-- Dynamic Slides -->\r\n            <div class=\"carousel-item \" *ngFor=\"let slide of locationSlides; let i = index\" [class.active]=\"i === 0\">\r\n              <div class=\"row justify-content-center g-2 g-md-3 mt-5\">\r\n                <div class=\"col-xl-2 col-lg-2 col-md-3 col-sm-4 col-6\" *ngFor=\"let location of slide\">\r\n                  <div class=\"location-card\" (click)=\"onLocationClick(location)\">\r\n                    <img [src]=\"location.image || 'assets/media/auth/404-error.png'\" [alt]=\"location.name\"\r\n                      class=\"img-fluid\">\r\n                    <div class=\"location-overlay\">\r\n                      <div class=\"location-info\">\r\n                        <h5>{{ location.name }}</h5>\r\n                        <p><i class=\"fas fa-map-marker-alt\"></i> {{ location.propertyCount }} Properties Available</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Carousel Controls -->\r\n          <button class=\"carousel-control-prev\" type=\"button\" data-bs-target=\"#horizontalCarousel\" data-bs-slide=\"prev\"\r\n            (click)=\"prevSlide()\">\r\n            <span class=\"carousel-control-prev-icon\" aria-hidden=\"true\"></span>\r\n            <span class=\"visually-hidden\">Previous</span>\r\n          </button>\r\n          <button class=\"carousel-control-next\" type=\"button\" data-bs-target=\"#horizontalCarousel\" data-bs-slide=\"next\"\r\n            (click)=\"nextSlide()\">\r\n            <span class=\"carousel-control-next-icon\" aria-hidden=\"true\"></span>\r\n            <span class=\"visually-hidden\">Next</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"row justify-content-center mt-5\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-secondary btn-lg\" (click)=\"loadMoreLocations()\">\r\n            Load More Locations\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Articles Section -->\r\n  <section class=\"articles-section py-5\">\r\n    <div class=\"container\">\r\n      <!-- Section Header -->\r\n      <div class=\"row mb-5\">\r\n        <div class=\"col-12\">\r\n          <div class=\"section-header d-flex justify-content-between align-items-center\">\r\n            <div class=\"left-controls d-flex align-items-center\">\r\n              <button class=\"carousel-control-btn prev-btn\" type=\"button\" data-bs-target=\"#articlesCarousel\"\r\n                data-bs-slide=\"prev\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n              <button class=\"carousel-control-btn next-btn ms-2\" type=\"button\" data-bs-target=\"#articlesCarousel\"\r\n                data-bs-slide=\"next\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <h1 class=\"articles-title text-center flex-grow-1  \">Articles That Interest You</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Bootstrap Carousel for Articles with All Articles Link -->\r\n      <div class=\"row\">\r\n        <!-- All Articles Link on the Left -->\r\n        <div class=\"col-lg-2 col-md-3 col-12 d-flex align-items-center justify-content-center\">\r\n          <div class=\"right-link\">\r\n            <a href=\"#\" class=\"view-all-link\">\r\n              <i class=\"fas fa-arrow-left me-2 text-success fs-4\"></i>\r\n              <span class=\"text-success fs-2\">All Articles</span>\r\n              <div class=\"green-underline\"></div>\r\n            </a>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Carousel on the Right -->\r\n        <div class=\"col-lg-10 col-md-9 col-12\">\r\n          <div id=\"articlesCarousel\" class=\"carousel slide\" data-bs-ride=\"carousel\" data-bs-interval=\"6000\">\r\n            <div class=\"carousel-inner\">\r\n              <!-- Slide 1 -->\r\n              <div class=\"carousel-item active\">\r\n                <div class=\"row g-4 justify-content-center\">\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-10.jpg\" alt=\"Article 1\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Modern Finishing Materials - Shop with the Best</h4>\r\n                            <p>A very quiet area away from the noise and hustle of the city, suitable for large and\r\n                              small\r\n                              families, spacious area with a private garden.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-20.jpg\" alt=\"Article 2\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Invest Your Money with Hotel Property</h4>\r\n                            <p>Excellent investment opportunity in the heart of the city, guaranteed returns and\r\n                              integrated\r\n                              management, strategic location near the airport and commercial centers.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-30.jpg\" alt=\"Article 3\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Villa 10 October April 2019</h4>\r\n                            <p>Latest international finishing materials, high quality and competitive prices,\r\n                              specialized\r\n                              team to implement finishing works to the highest standards.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Slide 2 -->\r\n              <div class=\"carousel-item\">\r\n                <div class=\"row g-4 justify-content-center\">\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-40.jpg\" alt=\"Article 4\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Apartment in New Cairo</h4>\r\n                            <p>Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated\r\n                              facilities, close to universities and international schools.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-50.jpg\" alt=\"Article 5\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>North Coast Properties</h4>\r\n                            <p>Residential units directly on the sea, wonderful panoramic view, integrated recreational\r\n                              facilities and suitable for summer investment.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"col-lg-4 col-md-6 col-sm-8\">\r\n                    <div class=\"article-card\">\r\n                      <div class=\"article-image\">\r\n                        <img src=\"./assets/media/stock/600x400/img-60.jpg\" alt=\"Article 6\" class=\"img-fluid\">\r\n                        <div class=\"article-overlay\">\r\n                          <div class=\"article-content\">\r\n                            <h4>Administrative Offices Downtown</h4>\r\n                            <p>Modern office spaces in the heart of Cairo, suitable for companies and institutions,\r\n                              parking\r\n                              and integrated service facilities.</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Carousel Indicators -->\r\n            <div class=\"carousel-indicators\">\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"0\" class=\"active\"></button>\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"1\"></button>\r\n              <button type=\"button\" data-bs-target=\"#articlesCarousel\" data-bs-slide-to=\"2\"></button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Download App Section -->\r\n  <section class=\"download-app-section py-5\">\r\n    <div class=\"container\">\r\n      <div class=\"row align-items-center\">\r\n        <!-- Left Side - App Download Info -->\r\n        <div class=\"col-lg-6 col-md-12 mb-4 mb-lg-0\">\r\n          <div class=\"download-content\">\r\n            <div class=\"download-header mb-4\">\r\n              <h2 class=\"download-title\">Download the Electronic App</h2>\r\n              <p class=\"download-subtitle\">\r\n                Download our app to access the latest real estate offers and properties\r\n              </p>\r\n            </div>\r\n\r\n            <!-- App Store Buttons -->\r\n            <div class=\"app-store-buttons d-flex flex-wrap gap-3\">\r\n              <a href=\"#\" class=\"app-store-btn\">\r\n                <div class=\"store-button app-store\">\r\n                  <div class=\"store-icon\">\r\n                    <i class=\"fab fa-apple\"></i>\r\n                  </div>\r\n                  <div class=\"store-text\">\r\n                    <span class=\"download-text\">Download on the</span>\r\n                    <span class=\"store-name\">App Store</span>\r\n                  </div>\r\n                </div>\r\n              </a>\r\n              <a href=\"#\" class=\"google-play-btn\">\r\n                <div class=\"store-button google-play\">\r\n                  <div class=\"store-icon\">\r\n                    <i class=\"fab fa-google-play\"></i>\r\n                  </div>\r\n                  <div class=\"store-text\">\r\n                    <span class=\"download-text\">GET IT ON</span>\r\n                    <span class=\"store-name\">Google Play</span>\r\n                  </div>\r\n                </div>\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Right Side - App Preview/Logo -->\r\n        <div class=\"col-lg-6 col-md-12 text-center\">\r\n          <div class=\"app-preview\">\r\n            <div class=\"app-logo-container\">\r\n              <img src=\"./assets/media/easydeallogos/loading-logo.png\" alt=\"EasyDeal App\" class=\"app-logo\">\r\n            </div>\r\n            <div class=\"app-info mt-3\">\r\n              <h4 class=\"app-name\">EASY DEAL</h4>\r\n              <p class=\"app-description\">Your trusted real estate partner</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Newsletter Subscription Section -->\r\n  <section class=\"newsletter-section py-3\">\r\n    <div class=\"container\">\r\n      <div class=\"row justify-content-center\">\r\n        <div class=\"col-lg-6 col-md-8 col-12\">\r\n          <div class=\"newsletter-container\">\r\n            <div class=\"row align-items-center g-2\">\r\n              <!-- Left Side - Newsletter Content -->\r\n              <div class=\"col-md-7 col-12 text-center text-md-start\">\r\n                <h5 class=\"newsletter-title mb-1\">Join Our Mailing List</h5>\r\n                <small class=\"newsletter-subtitle text-muted\">Get latest offers</small>\r\n              </div>\r\n\r\n              <!-- Right Side - Subscribe Form -->\r\n              <div class=\"col-md-5 col-12\">\r\n                <div class=\"newsletter-form\">\r\n                  <div class=\"input-group input-group-sm\">\r\n                    <input type=\"email\" class=\"form-control\" placeholder=\"Your email\" />\r\n                    <button type=\"button\" class=\"btn btn-subscribe\" (click)=\"onSubscribeClick()\">\r\n                      Subscribe\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- Footer Section -->\r\n  <footer class=\"footer-section\">\r\n    <div class=\"container\">\r\n      <div class=\"row\">\r\n        <!-- Company Info -->\r\n        <div class=\"col-lg-4 col-md-6 mb-4\">\r\n          <div class=\"footer-brand\">\r\n            <img src=\"./assets/media/easydeallogos/loading-logo.png\" alt=\"EasyDeal Logo\" class=\"footer-logo mb-3\">\r\n            <h5 class=\"company-name\">EASY DEAL</h5>\r\n            <p class=\"company-description\">\r\n              Your trusted real estate partner in Egypt. We provide the best properties\r\n              and investment opportunities with professional service.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Quick Links -->\r\n        <div class=\"col-lg-2 col-md-6 mb-4\">\r\n          <div class=\"footer-links\">\r\n            <h6 class=\"footer-title\">Quick Links</h6>\r\n            <ul class=\"footer-menu\">\r\n              <li><a href=\"#\">Home</a></li>\r\n              <li><a href=\"#\">About EasyDeal</a></li>\r\n              <li><a href=\"#\">New Projects</a></li>\r\n              <li><a href=\"#\">Advertisements</a></li>\r\n              <li><a href=\"#\">Contact Us</a></li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Services -->\r\n        <div class=\"col-lg-2 col-md-6 mb-4\">\r\n          <div class=\"footer-links\">\r\n            <h6 class=\"footer-title\">Services</h6>\r\n            <ul class=\"footer-menu\">\r\n              <li><a href=\"#\">Property Search</a></li>\r\n              <li><a href=\"#\">Investment Consulting</a></li>\r\n              <li><a href=\"#\">Property Management</a></li>\r\n              <li><a href=\"#\">Legal Support</a></li>\r\n              <li><a href=\"#\">Financing Options</a></li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Contact Info -->\r\n        <div class=\"col-lg-4 col-md-6 mb-4\">\r\n          <div class=\"footer-contact\">\r\n            <h6 class=\"footer-title\">Contact Information</h6>\r\n            <div class=\"contact-item\">\r\n              <i class=\"fas fa-phone text-primary me-2\"></i>\r\n              <span>19888 - info&#64;easydeal.com</span>\r\n            </div>\r\n            <div class=\"contact-item\">\r\n              <i class=\"fas fa-envelope text-primary me-2\"></i>\r\n              <span>info&#64;easydeal.com</span>\r\n            </div>\r\n            <div class=\"contact-item\">\r\n              <i class=\"fas fa-map-marker-alt text-primary me-2\"></i>\r\n              <span>Cairo, Egypt</span>\r\n            </div>\r\n\r\n            <!-- Social Media Links -->\r\n            <div class=\"social-links mt-3\">\r\n              <a href=\"#\" class=\"social-link\">\r\n                <i class=\"fab fa-facebook-f\"></i>\r\n              </a>\r\n              <a href=\"#\" class=\"social-link\">\r\n                <i class=\"fab fa-linkedin-in\"></i>\r\n              </a>\r\n              <a href=\"#\" class=\"social-link\">\r\n                <i class=\"fab fa-twitter\"></i>\r\n              </a>\r\n              <a href=\"#\" class=\"social-link\">\r\n                <i class=\"fab fa-pinterest\"></i>\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Footer Bottom -->\r\n      <div class=\"footer-bottom\">\r\n        <div class=\"row align-items-center\">\r\n          <div class=\"col-md-6\">\r\n            <p class=\"copyright-text mb-0\">\r\n              © 2025 EasyDeal. All rights reserved.\r\n            </p>\r\n          </div>\r\n          <div class=\"col-md-6 text-md-end\">\r\n            <div class=\"footer-bottom-links\">\r\n              <a href=\"#\">Privacy Policy</a>\r\n              <a href=\"#\">Terms of Service</a>\r\n              <a href=\"#\">Cookie Policy</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </footer>\r\n\r\n</div>"], "mappings": ";;;;;;IC2CYA,EAFJ,CAAAC,cAAA,eAAkE,eACrC,gBACI;IAC3BD,EAAA,CAAAE,SAAA,aAA6C;IAC/CF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,WAAI;IACZJ,EADY,CAAAG,YAAA,EAAO,EACb;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACI;IAC3BD,EAAA,CAAAE,SAAA,aAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACtBJ,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAAiD;IACnDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IACtBJ,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACI;IAC3BD,EAAA,CAAAE,SAAA,cAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,kBAAU;IAEpBJ,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;;;;;;IAKJH,EAAA,CAAAC,cAAA,eAAqF;IAA/BD,EAAA,CAAAK,UAAA,mBAAAC,mDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAClFZ,EAAA,CAAAE,SAAA,eAAyF;IACzFF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAE,SAAA,aAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;IAHCH,EAAA,CAAAa,SAAA,EAA6B;IAACb,EAA9B,CAAAc,UAAA,QAAAL,MAAA,CAAAM,mBAAA,IAAAf,EAAA,CAAAgB,aAAA,CAA6B,QAAAP,MAAA,CAAAQ,kBAAA,GAA6B;IACvCjB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAkB,iBAAA,CAAAT,MAAA,CAAAQ,kBAAA,GAA0B;;;;;;IAMlDjB,EADF,CAAAC,cAAA,eAAkE,eACP;IAA9BD,EAAA,CAAAK,UAAA,mBAAAc,mDAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;;IAEzBD,EADF,CAAAC,cAAA,eAA+F,aAC1D;IAKjCD,EAJA,CAAAE,SAAA,gBACmN,gBAEuZ,gBAE4O;IACx1BF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,WAAM,oBACyB;IAC3BD,EAAA,CAAAE,SAAA,iBAA4C;IAIpDF,EAHM,CAAAG,YAAA,EAAW,EACN,EACH,EACD;;IACPH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAChBJ,EADgB,CAAAG,YAAA,EAAO,EACjB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAiB,oDAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAAkF;IACpFF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,oBAAW;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAkB,oDAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAAmF;IACrFF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,kBAAS;IAClBJ,EADkB,CAAAG,YAAA,EAAO,EACnB;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAmB,oDAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA+D;IACjEF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,cAAK;IACdJ,EADc,CAAAG,YAAA,EAAO,EACf;IACNH,EAAA,CAAAC,cAAA,gBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAoB,oDAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,yBAA8F;IAChGF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,uBAAc;IACvBJ,EADuB,CAAAG,YAAA,EAAO,EACxB;IACNH,EAAA,CAAAE,SAAA,gBAAoC;IACpCF,EAAA,CAAAC,cAAA,gBAA0D;IAAnBD,EAAA,CAAAK,UAAA,mBAAAqB,oDAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IACvD3B,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAAoD;IACtDF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAI,MAAA,gBAAO;IAChBJ,EADgB,CAAAG,YAAA,EAAO,EACjB;IACNH,EAAA,CAAAE,SAAA,gBAAoC;IACpCF,EAAA,CAAAC,cAAA,gBAA0E;IAA9BD,EAAA,CAAAK,UAAA,mBAAAuB,oDAAA;MAAA5B,EAAA,CAAAO,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACvErB,EAAA,CAAAC,cAAA,iBAA2B;IAACD,EAAA,CAAAI,MAAA,qBAAY;IAE5CJ,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAsOIH,EADF,CAAAC,cAAA,eAAsF,eACrB;IAApCD,EAAA,CAAAK,UAAA,mBAAAwB,0DAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA,EAAAC,SAAA;MAAA,MAAAvB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwB,eAAA,CAAAH,WAAA,CAAyB;IAAA,EAAC;IAC5D9B,EAAA,CAAAE,SAAA,eACoB;IAGhBF,EAFJ,CAAAC,cAAA,eAA8B,eACD,SACrB;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,SAAA,YAAqC;IAACF,EAAA,CAAAI,MAAA,GAAiD;IAIlGJ,EAJkG,CAAAG,YAAA,EAAI,EAC1F,EACF,EACF,EACF;;;;IATGH,EAAA,CAAAa,SAAA,GAA2D;IAACb,EAA5D,CAAAc,UAAA,QAAAgB,WAAA,CAAAI,KAAA,uCAAAlC,EAAA,CAAAgB,aAAA,CAA2D,QAAAc,WAAA,CAAAK,IAAA,CAAsB;IAI9EnC,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAkB,iBAAA,CAAAY,WAAA,CAAAK,IAAA,CAAmB;IACkBnC,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAoC,kBAAA,MAAAN,WAAA,CAAAO,aAAA,0BAAiD;;;;;IARpGrC,EADF,CAAAC,cAAA,cAAyG,eAC/C;IACtDD,EAAA,CAAAsC,UAAA,IAAAC,oCAAA,oBAAsF;IAa1FvC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAf0EH,EAAA,CAAAwC,WAAA,WAAAC,IAAA,OAAwB;IAExBzC,EAAA,CAAAa,SAAA,GAAQ;IAARb,EAAA,CAAAc,UAAA,YAAA4B,QAAA,CAAQ;;;AD7WpG,OAAM,MAAOC,aAAa;EAqFJC,WAAA;EApFpBC,WAAW,GAAQ,IAAI;EACvBC,UAAU,GAAY,KAAK;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,cAAc,GAAY,KAAK;EAE/B;EACAC,cAAc,GAAY,EAAE;EAC5BC,iBAAiB,GAAW,CAAC;EAC7BC,gBAAgB;EAChBC,SAAS,GAAU,CACjB;IACEC,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,YAAY;IAClBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,SAAS;IACfD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,iBAAiB;IACvBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,eAAe;IACrBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,cAAc;IACpBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,SAAS;IACfD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,WAAW;IACjBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,CAAC;IACLlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,yCAAyC;IAChDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,EACD;IACEgB,EAAE,EAAE,EAAE;IACNlB,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,0CAA0C;IACjDG,aAAa,EAAE;GAChB,CACF;EAEDiB,YAAoBV,WAAkC;IAAlC,KAAAA,WAAW,GAAXA,WAAW;EAA2B;EAE1DW,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACV,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACtC;EACF;EAEAK,gBAAgBA,CAAA;IACd;IACA,MAAMO,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMpB,WAAW,GAAGmB,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,SAAS,IAAIlB,WAAW,EAAE;MAC5B,IAAI;QACF,IAAI,CAACA,WAAW,GAAGqB,IAAI,CAACC,KAAK,CAACtB,WAAW,CAAC;QAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACd;QACA,IAAI,CAACtB,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACzB;EACF;EAEA5B,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC4B,WAAW,EAAE;MACpB,OAAO,IAAI,CAACA,WAAW,CAACwB,QAAQ,IAAK,MAAM;IAC7C;IACA,OAAO,OAAO;EAChB;EAEAtD,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC8B,WAAW,IAAI,IAAI,CAACA,WAAW,CAACX,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACW,WAAW,CAACX,KAAK;IAC/B;IACA;IACA,OAAO,gCAAgC;EACzC;EAEAtB,kBAAkBA,CAAA;IAChB,IAAI,CAACmC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA1B,iBAAiBA,CAAA;IACf,IAAI,CAAC0B,gBAAgB,GAAG,KAAK;EAC/B;EAEApB,MAAMA,CAAA;IACJqC,YAAY,CAACM,UAAU,CAAC,WAAW,CAAC;IACpCN,YAAY,CAACM,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACxB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B;IACA;EACF;EAEAwB,gBAAgBA,CAAA;IACd,IAAI,CAACvB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C;IACA,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEAyB,eAAeA,CAAA;IACb,IAAI,CAACxB,cAAc,GAAG,KAAK;EAC7B;EAGAyB,eAAeA,CAACC,KAAY;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;IACnD,MAAMC,YAAY,GAAGH,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAC;IACrD,MAAME,aAAa,GAAGJ,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;IACvD,MAAMG,iBAAiB,GAAGL,MAAM,CAACE,OAAO,CAAC,sBAAsB,CAAC;IAEhE;IACA,IAAI,CAACD,WAAW,IAAI,CAACE,YAAY,IAAI,IAAI,CAAC/B,gBAAgB,EAAE;MAC1D,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC/B;IAEA;IACA,IAAI,CAACgC,aAAa,IAAI,CAACC,iBAAiB,IAAI,IAAI,CAAChC,cAAc,EAAE;MAC/D,IAAI,CAACA,cAAc,GAAG,KAAK;IAC7B;EACF;EAEA;EACAS,wBAAwBA,CAAA;IACtB;IACA,MAAMwB,aAAa,GAAG,CAAC;IACvB,IAAI,CAAChC,cAAc,GAAG,EAAE;IAExB,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,MAAM,EAAED,CAAC,IAAID,aAAa,EAAE;MAC7D,IAAI,CAAChC,cAAc,CAACmC,IAAI,CAAC,IAAI,CAAChC,SAAS,CAACiC,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGD,aAAa,CAAC,CAAC;IACtE;EACF;EAEArB,kBAAkBA,CAAA;IAChB,IAAI;MACF,MAAM0B,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MACrE,IAAIF,eAAe,EAAE;QACnB;QACA,IAAI,OAAOG,SAAS,KAAK,WAAW,EAAE;UACpC,MAAMC,QAAQ,GAAG,IAAID,SAAS,CAACE,QAAQ,CAACL,eAAe,EAAE;YACvDM,QAAQ,EAAE,IAAI;YACdC,IAAI,EAAE,UAAU;YAChBC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC/C,CAAC,MAAM;UACL;UACA,IAAI,CAACC,mBAAmB,EAAE;UAC1BF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;MACF;IACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACd6B,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACA,IAAI,CAAC+B,mBAAmB,EAAE;IAC5B;EACF;EAEAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,IAAI,CAAChD,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACtC;IAEA;IACA,IAAI,CAACA,gBAAgB,GAAGiD,WAAW,CAAC,MAAK;MACvC,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAACrD,cAAc,CAACkC,MAAM;IAC9C,IAAImB,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB,GAAG,CAAC,IAAIoD,WAAW;MACnE,IAAI,CAACC,qBAAqB,EAAE;IAC9B;EACF;EAEAC,SAASA,CAAA;IACP,MAAMF,WAAW,GAAG,IAAI,CAACrD,cAAc,CAACkC,MAAM;IAC9C,IAAImB,WAAW,GAAG,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,KAAK,CAAC,GAAGoD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACpD,iBAAiB,GAAG,CAAC;MACpG,IAAI,CAACqD,qBAAqB,EAAE;IAC9B;EACF;EAEAA,qBAAqBA,CAAA;IACnB,MAAME,aAAa,GAAGlB,QAAQ,CAACmB,gBAAgB,CAAC,oCAAoC,CAAC;IACrFD,aAAa,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACpC,IAAIA,KAAK,KAAK,IAAI,CAAC3D,iBAAiB,EAAE;QACpC0D,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLH,IAAI,CAACE,SAAS,CAACE,MAAM,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC;EACJ;EAEA/E,eAAeA,CAACgF,QAAa;IAC3BhB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,QAAQ,CAAC;IAC1C;IACA;EACF;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,iBAAiBA,CAAA,GAEjB;EAEAC,gBAAgBA,CAAA;IACdlB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;IACA;EACF;;qCApSWvD,aAAa,EAAA3C,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,qBAAA;EAAA;;UAAb3E,aAAa;IAAA4E,SAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAb1H,EAAA,CAAAK,UAAA,mBAAAuH,uCAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAlD,eAAA,CAAAoD,MAAA,CAAuB;QAAA,UAAA7H,EAAA,CAAA8H,iBAAA,CAAV;;;;;;;;QCFlB9H,EARR,CAAAC,cAAA,aAAuB,gBAGO,aAEW,aACD,aAEN;QACxBD,EAAA,CAAAE,SAAA,aAA8G;QAChHF,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,gBACuE;QADhBD,EAAA,CAAAK,UAAA,mBAAA0H,+CAAA;UAAA,OAASJ,GAAA,CAAApD,gBAAA,EAAkB;QAAA,EAAC;QAEjFvE,EAAA,CAAAE,SAAA,cACO;QACTF,EAAA,CAAAG,YAAA,EAAS;QAMHH,EAHN,CAAAC,cAAA,aAAiD,YACK,cAC7B,aACU;QAACD,EAAA,CAAAI,MAAA,cAAK;QACrCJ,EADqC,CAAAG,YAAA,EAAI,EACpC;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,wBAAe;QAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC9C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,sBAAa;QAC7CJ,EAD6C,CAAAG,YAAA,EAAI,EAC5C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,wBAAe;QAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC9C;QAEHH,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAI,MAAA,oBAAW;QAG/CJ,EAH+C,CAAAG,YAAA,EAAI,EAC1C,EACF,EACD;QAGNH,EAAA,CAAAsC,UAAA,KAAA0F,6BAAA,mBAAkE;QAkClEhI,EAAA,CAAAC,cAAA,eAA0C;QAoExCD,EAlEA,CAAAsC,UAAA,KAAA2F,6BAAA,kBAAqF,KAAAC,6BAAA,mBAOnB,KAAAC,2BAAA,gBA2DP;QAMjEnI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;QAIJH,EADF,CAAAC,cAAA,eAA0B,eACK;QAI3BD,EAHA,CAAAE,SAAA,eAE8C,eACd;QAClCF,EAAA,CAAAG,YAAA,EAAM;QAQMH,EANZ,CAAAC,cAAA,eAA0B,eACD,eACmB,eAClB,eACe,eACH,cACJ;QAACD,EAAA,CAAAI,MAAA,aAAI;QAC7BJ,EAD6B,CAAAG,YAAA,EAAK,EAC5B;QAEJH,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAI,MAAA,eAAM;QAC/BJ,EAD+B,CAAAG,YAAA,EAAK,EAC9B;QAEJH,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAI,MAAA,qBAAY;QAQnDJ,EARmD,CAAAG,YAAA,EAAK,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACC;QAODH,EAJR,CAAAC,cAAA,mBAAoC,eACX,eACJ,eACK,cACyB;QAAAD,EAAA,CAAAI,MAAA,2BAAmB;QAElEJ,EAFkE,CAAAG,YAAA,EAAK,EAC/D,EACF;QAMAH,EAJN,CAAAC,cAAA,eAAqB,eAEsB,eACZ,eACG;QAC1BD,EAAA,CAAAE,SAAA,eAAqF;QACrFF,EAAA,CAAAC,cAAA,eAA4B;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1CH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAE,SAAA,aAAqC;QACrCF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAI,MAAA,iBAAS;QAEnBJ,EAFmB,CAAAG,YAAA,EAAO,EAClB,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8B,cACD;QAAAD,EAAA,CAAAI,MAAA,wBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAChDH,EAAA,CAAAC,cAAA,aAAgC;QAAAD,EAAA,CAAAI,MAAA,oDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,eAA4B,gBACN;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;QAGFH,EAFJ,CAAAC,cAAA,eAAqC,eACN,eACR;QAKjBD,EAJA,CAAAE,SAAA,aAA2B,aACA,aACA,aACA,aACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,gBAA0B;QAAAD,EAAA,CAAAI,MAAA,WAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,eAA8B,kBACqB;QAC/CD,EAAA,CAAAE,SAAA,aAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAiD;QAC/CD,EAAA,CAAAE,SAAA,aAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,eAAyC,eACZ,eACG;QAC1BD,EAAA,CAAAE,SAAA,eAAsF;QACtFF,EAAA,CAAAC,cAAA,eAAgD;QAAAD,EAAA,CAAAI,MAAA,gBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC9DH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAE,SAAA,aAAqC;QACrCF,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAEfJ,EAFe,CAAAG,YAAA,EAAO,EACd,EACF;QAEJH,EADF,CAAAC,cAAA,eAA8B,cACD;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,gBAAyC,gBACZ,gBACG;QAC1BD,EAAA,CAAAE,SAAA,gBAAsF;QACtFF,EAAA,CAAAC,cAAA,gBAA4B;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1CH,EAAA,CAAAC,cAAA,gBAA+B;QAC7BD,EAAA,CAAAE,SAAA,cAAqC;QACrCF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,gBAAO;QAEjBJ,EAFiB,CAAAG,YAAA,EAAO,EAChB,EACF;QAEJH,EADF,CAAAC,cAAA,gBAA8B,eACD;QAAAD,EAAA,CAAAI,MAAA,wBAAe;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAC9BJ,EAD8B,CAAAG,YAAA,EAAO,EAC/B;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QAM5CF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,gBAAyC,gBACZ,gBACG;QAC1BD,EAAA,CAAAE,SAAA,gBAAsF;QACtFF,EAAA,CAAAC,cAAA,gBAAgD;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC9DH,EAAA,CAAAC,cAAA,gBAA+B;QAC7BD,EAAA,CAAAE,SAAA,cAAqC;QACrCF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,mBAAU;QAEpBJ,EAFoB,CAAAG,YAAA,EAAO,EACnB,EACF;QAEJH,EADF,CAAAC,cAAA,gBAA8B,eACD;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5CH,EAAA,CAAAC,cAAA,cAAgC;QAAAD,EAAA,CAAAI,MAAA,qDAAkC;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEpEH,EADF,CAAAC,cAAA,gBAA4B,iBACN;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;QAGFH,EAFJ,CAAAC,cAAA,gBAAqC,gBACN,gBACR;QAKjBD,EAJA,CAAAE,SAAA,cAA2B,cACA,cACA,cACA,cACA;QAC7BF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAI,MAAA,YAAG;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QAEJH,EADF,CAAAC,cAAA,gBAA8B,mBACqB;QAC/CD,EAAA,CAAAE,SAAA,cAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAAiD;QAC/CD,EAAA,CAAAE,SAAA,cAAgC;QASlDF,EARgB,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF,EACF,EACE;QAOFH,EAJR,CAAAC,cAAA,oBAAkD,gBACzB,gBACJ,gBACK,eACyB;QAAAD,EAAA,CAAAI,MAAA,0BAAiB;QAEhEJ,EAFgE,CAAAG,YAAA,EAAK,EAC7D,EACF;QAKFH,EAHJ,CAAAC,cAAA,gBAAkD,gBAEoD,gBACtE;QAE1BD,EAAA,CAAAsC,UAAA,MAAA8F,8BAAA,kBAAyG;QAgB3GpI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,mBACwB;QAAtBD,EAAA,CAAAK,UAAA,mBAAAgI,iDAAA;UAAA,OAASV,GAAA,CAAAnB,SAAA,EAAW;QAAA,EAAC;QACrBxG,EAAA,CAAAE,SAAA,iBAAmE;QACnEF,EAAA,CAAAC,cAAA,iBAA8B;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QACxCJ,EADwC,CAAAG,YAAA,EAAO,EACtC;QACTH,EAAA,CAAAC,cAAA,mBACwB;QAAtBD,EAAA,CAAAK,UAAA,mBAAAiI,iDAAA;UAAA,OAASX,GAAA,CAAAtB,SAAA,EAAW;QAAA,EAAC;QACrBrG,EAAA,CAAAE,SAAA,iBAAmE;QACnEF,EAAA,CAAAC,cAAA,iBAA8B;QAAAD,EAAA,CAAAI,MAAA,aAAI;QAGxCJ,EAHwC,CAAAG,YAAA,EAAO,EAClC,EACL,EACF;QAIFH,EAFJ,CAAAC,cAAA,gBAA6C,gBACX,mBACyC;QAA9BD,EAAA,CAAAK,UAAA,mBAAAkI,iDAAA;UAAA,OAASZ,GAAA,CAAAT,iBAAA,EAAmB;QAAA,EAAC;QACpElH,EAAA,CAAAI,MAAA,8BACF;QAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAUEH,EAPZ,CAAAC,cAAA,oBAAuC,gBACd,gBAEC,gBACA,gBAC4D,gBACvB,mBAE5B;QACrBD,EAAA,CAAAE,SAAA,cAAmC;QACrCF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBACuB;QACrBD,EAAA,CAAAE,SAAA,cAAoC;QAExCF,EADE,CAAAG,YAAA,EAAS,EACL;QAENH,EAAA,CAAAC,cAAA,eAAqD;QAAAD,EAAA,CAAAI,MAAA,mCAA0B;QAGrFJ,EAHqF,CAAAG,YAAA,EAAK,EAChF,EACF,EACF;QAOAH,EAJN,CAAAC,cAAA,gBAAiB,gBAEwE,gBAC7D,cACY;QAChCD,EAAA,CAAAE,SAAA,cAAwD;QACxDF,EAAA,CAAAC,cAAA,iBAAgC;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QACnDH,EAAA,CAAAE,SAAA,gBAAmC;QAGzCF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAWQH,EARd,CAAAC,cAAA,gBAAuC,gBAC6D,gBACpE,gBAEQ,gBACY,gBACF,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,wDAA+C;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACxDH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,kJAE6C;QAK1DJ,EAL0D,CAAAG,YAAA,EAAI,EAChD,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,8CAAqC;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC9CH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,6KAEsE;QAKnFJ,EALmF,CAAAG,YAAA,EAAI,EACzE,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,oCAA2B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACpCH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,+JAE0D;QAO3EJ,EAP2E,CAAAG,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF,EACF,EACF;QAOEH,EAJR,CAAAC,cAAA,gBAA2B,gBACmB,gBACF,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,+BAAsB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/BH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,+JAC2D;QAKxEJ,EALwE,CAAAG,YAAA,EAAI,EAC9D,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,gBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,+BAAsB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC/BH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,gJAC6C;QAK1DJ,EAL0D,CAAAG,YAAA,EAAI,EAChD,EACF,EACF,EACF,EACF;QAGFH,EAFJ,CAAAC,cAAA,gBAAwC,gBACZ,gBACG;QACzBD,EAAA,CAAAE,SAAA,iBAAqF;QAGjFF,EAFJ,CAAAC,cAAA,gBAA6B,gBACE,WACvB;QAAAD,EAAA,CAAAI,MAAA,wCAA+B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACxCH,EAAA,CAAAC,cAAA,UAAG;QAAAD,EAAA,CAAAI,MAAA,wIAEiC;QAQpDJ,EARoD,CAAAG,YAAA,EAAI,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACF;QAGNH,EAAA,CAAAC,cAAA,iBAAiC;QAG/BD,EAFA,CAAAE,SAAA,oBAAsG,oBACf,oBACA;QAMnGF,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACE;QAUEH,EAPZ,CAAAC,cAAA,qBAA2C,gBAClB,iBACe,iBAEW,iBACb,iBACM,gBACL;QAAAD,EAAA,CAAAI,MAAA,oCAA2B;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,eAA6B;QAC3BD,EAAA,CAAAI,MAAA,kFACF;QACFJ,EADE,CAAAG,YAAA,EAAI,EACA;QAMAH,EAHN,CAAAC,cAAA,iBAAsD,eAClB,iBACI,iBACV;QACtBD,EAAA,CAAAE,SAAA,eAA4B;QAC9BF,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,iBAAwB,kBACM;QAAAD,EAAA,CAAAI,MAAA,wBAAe;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QAClDH,EAAA,CAAAC,cAAA,kBAAyB;QAAAD,EAAA,CAAAI,MAAA,kBAAS;QAGxCJ,EAHwC,CAAAG,YAAA,EAAO,EACrC,EACF,EACJ;QAGAH,EAFJ,CAAAC,cAAA,eAAoC,iBACI,iBACZ;QACtBD,EAAA,CAAAE,SAAA,eAAkC;QACpCF,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,iBAAwB,kBACM;QAAAD,EAAA,CAAAI,MAAA,kBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QAC5CH,EAAA,CAAAC,cAAA,kBAAyB;QAAAD,EAAA,CAAAI,MAAA,oBAAW;QAMhDJ,EANgD,CAAAG,YAAA,EAAO,EACvC,EACF,EACJ,EACA,EACF,EACF;QAKFH,EAFJ,CAAAC,cAAA,iBAA4C,iBACjB,iBACS;QAC9BD,EAAA,CAAAE,SAAA,iBAA6F;QAC/FF,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,iBAA2B,gBACJ;QAAAD,EAAA,CAAAI,MAAA,kBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACnCH,EAAA,CAAAC,cAAA,eAA2B;QAAAD,EAAA,CAAAI,MAAA,yCAAgC;QAMvEJ,EANuE,CAAAG,YAAA,EAAI,EAC3D,EACF,EACF,EACF,EACF,EACE;QAWIH,EARd,CAAAC,cAAA,qBAAyC,gBAChB,gBACmB,iBACA,iBACF,iBACQ,iBAEiB,gBACnB;QAAAD,EAAA,CAAAI,MAAA,8BAAqB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC5DH,EAAA,CAAAC,cAAA,mBAA8C;QAAAD,EAAA,CAAAI,MAAA,0BAAiB;QACjEJ,EADiE,CAAAG,YAAA,EAAQ,EACnE;QAKFH,EAFJ,CAAAC,cAAA,iBAA6B,iBACE,iBACa;QACtCD,EAAA,CAAAE,SAAA,mBAAoE;QACpEF,EAAA,CAAAC,cAAA,oBAA6E;QAA7BD,EAAA,CAAAK,UAAA,mBAAAmI,iDAAA;UAAA,OAASb,GAAA,CAAAR,gBAAA,EAAkB;QAAA,EAAC;QAC1EnH,EAAA,CAAAI,MAAA,oBACF;QASlBJ,EATkB,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,oBAA+B,gBACN,gBACJ,iBAEqB,iBACR;QACxBD,EAAA,CAAAE,SAAA,iBAAsG;QACtGF,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAI,MAAA,kBAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACvCH,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAI,MAAA,4IAEF;QAEJJ,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAKFH,EAFJ,CAAAC,cAAA,iBAAoC,iBACR,gBACC;QAAAD,EAAA,CAAAI,MAAA,oBAAW;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAEnCH,EADN,CAAAC,cAAA,gBAAwB,WAClB,eAAY;QAAAD,EAAA,CAAAI,MAAA,aAAI;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QACzBH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,uBAAc;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QACnCH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QACjCH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,uBAAc;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QACnCH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,mBAAU;QAGhCJ,EAHgC,CAAAG,YAAA,EAAI,EAAK,EAChC,EACD,EACF;QAKFH,EAFJ,CAAAC,cAAA,iBAAoC,iBACR,gBACC;QAAAD,EAAA,CAAAI,MAAA,iBAAQ;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAEhCH,EADN,CAAAC,cAAA,gBAAwB,WAClB,eAAY;QAAAD,EAAA,CAAAI,MAAA,wBAAe;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QACpCH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,8BAAqB;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QAC1CH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,4BAAmB;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QACxCH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QAAIJ,EAAJ,CAAAG,YAAA,EAAI,EAAK;QAClCH,EAAJ,CAAAC,cAAA,WAAI,eAAY;QAAAD,EAAA,CAAAI,MAAA,0BAAiB;QAGvCJ,EAHuC,CAAAG,YAAA,EAAI,EAAK,EACvC,EACD,EACF;QAKFH,EAFJ,CAAAC,cAAA,iBAAoC,iBACN,gBACD;QAAAD,EAAA,CAAAI,MAAA,4BAAmB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACjDH,EAAA,CAAAC,cAAA,iBAA0B;QACxBD,EAAA,CAAAE,SAAA,eAA8C;QAC9CF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,kCAA6B;QACrCJ,EADqC,CAAAG,YAAA,EAAO,EACtC;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QACxBD,EAAA,CAAAE,SAAA,eAAiD;QACjDF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,0BAAqB;QAC7BJ,EAD6B,CAAAG,YAAA,EAAO,EAC9B;QACNH,EAAA,CAAAC,cAAA,iBAA0B;QACxBD,EAAA,CAAAE,SAAA,eAAuD;QACvDF,EAAA,CAAAC,cAAA,aAAM;QAAAD,EAAA,CAAAI,MAAA,qBAAY;QACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;QAIJH,EADF,CAAAC,cAAA,iBAA+B,eACG;QAC9BD,EAAA,CAAAE,SAAA,eAAiC;QACnCF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,eAAkC;QACpCF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,eAA8B;QAChCF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,eAAgC;QAK1CF,EAJQ,CAAAG,YAAA,EAAI,EACA,EACF,EACF,EACF;QAMAH,EAHN,CAAAC,cAAA,iBAA2B,iBACW,iBACZ,eACW;QAC7BD,EAAA,CAAAI,MAAA,qDACF;QACFJ,EADE,CAAAG,YAAA,EAAI,EACA;QAGFH,EAFJ,CAAAC,cAAA,iBAAkC,iBACC,eACnB;QAAAD,EAAA,CAAAI,MAAA,uBAAc;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAC9BH,EAAA,CAAAC,cAAA,eAAY;QAAAD,EAAA,CAAAI,MAAA,yBAAgB;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAChCH,EAAA,CAAAC,cAAA,eAAY;QAAAD,EAAA,CAAAI,MAAA,sBAAa;QAQvCJ,EARuC,CAAAG,YAAA,EAAI,EACzB,EACF,EACF,EACF,EACF,EACC,EAEL;;;QAvuBIH,EAAA,CAAAa,SAAA,GAAqC;;QA2BjCb,EAAA,CAAAa,SAAA,IAAoB;QAApBb,EAAA,CAAAc,UAAA,SAAA6G,GAAA,CAAA3E,cAAA,CAAoB;QAoClBhD,EAAA,CAAAa,SAAA,GAAgB;QAAhBb,EAAA,CAAAc,UAAA,SAAA6G,GAAA,CAAA7E,UAAA,CAAgB;QAOhB9C,EAAA,CAAAa,SAAA,EAAoC;QAApCb,EAAA,CAAAc,UAAA,SAAA6G,GAAA,CAAA7E,UAAA,IAAA6E,GAAA,CAAA5E,gBAAA,CAAoC;QA2DtC/C,EAAA,CAAAa,SAAA,EAAiB;QAAjBb,EAAA,CAAAc,UAAA,UAAA6G,GAAA,CAAA7E,UAAA,CAAiB;QAsO2B9C,EAAA,CAAAa,SAAA,KAAmB;QAAnBb,EAAA,CAAAc,UAAA,YAAA6G,GAAA,CAAA1E,cAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}